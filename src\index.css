@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;
}

/* Web-specific normal sizing */
@media (min-width: 1024px) {
  :root {
    font-size: 16px; /* Normal base font size for web */
  }

  /* Normal text sizes for web - no aggressive scaling */
  .text-xs { font-size: 0.75rem; }
  .text-sm { font-size: 0.875rem; }
  .text-base { font-size: 1rem; }
  .text-lg { font-size: 1.125rem; }
  .text-xl { font-size: 1.25rem; }
  .text-2xl { font-size: 1.5rem; }
  .text-3xl { font-size: 1.875rem; }
  .text-4xl { font-size: 2.25rem; }
  .text-5xl { font-size: 3rem; }
  .text-6xl { font-size: 3.75rem; }
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
}

#root {
  width: 100vw;
  height: 100vh;
  margin: 0;
  text-align: left;
}

/* Custom animations */
@keyframes lightning {
  0%, 100% { transform: scale(1) rotate(0deg); }
  25% { transform: scale(1.1) rotate(-5deg); }
  50% { transform: scale(1.2) rotate(5deg); }
  75% { transform: scale(1.1) rotate(-2deg); }
}

.lightning-animation {
  animation: lightning 2s ease-in-out infinite;
}

@keyframes pulse-glow {
  0%, 100% { box-shadow: 0 0 5px rgba(59, 130, 246, 0.5); }
  50% { box-shadow: 0 0 20px rgba(59, 130, 246, 0.8); }
}

.pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

/* Remove spinner arrows from number inputs */
/* Chrome, Safari, Edge, Opera */
input[type="number"]::-webkit-outer-spin-button,
input[type="number"]::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox */
input[type="number"] {
  -moz-appearance: textfield;
}

/* Prevent scroll wheel from changing number input values */
input[type="number"] {
  -webkit-appearance: textfield;
  -moz-appearance: textfield;
  appearance: textfield;
}

/* Additional protection against scroll events */
input[type="number"]:focus {
  -webkit-appearance: textfield;
  -moz-appearance: textfield;
  appearance: textfield;
}

/* Mobile safe area support - Theme-aware and narrower approach */
.safe-top {
  padding-top: calc(env(safe-area-inset-top, 0px) + 0.5rem);
}

.safe-bottom {
  padding-bottom: calc(env(safe-area-inset-bottom, 0px) + 0.25rem);
}

.safe-bottom-nav {
  padding-bottom: calc(env(safe-area-inset-bottom, 0px) + 4rem);
}

/* Mobile-specific adjustments with narrower heights */
@media (max-width: 768px) {
  .mobile-safe-top {
    padding-top: calc(env(safe-area-inset-top, 0px) + 0.5rem);
  }

  .mobile-safe-bottom {
    padding-bottom: calc(env(safe-area-inset-bottom, 0px) + 0.25rem);
  }

  .mobile-safe-bottom-nav {
    padding-bottom: calc(env(safe-area-inset-bottom, 0px) + 4rem);
  }

  /* Ensure proper scrolling on mobile */
  main {
    -webkit-overflow-scrolling: touch;
    overscroll-behavior-y: contain;
  }

  /* Prevent horizontal overflow on mobile */
  body, #root {
    overflow-x: hidden;
  }

  /* Optimize chart rendering on mobile */
  canvas {
    max-width: 100% !important;
    height: auto !important;
  }
}

/* Theme-aware safe area backgrounds */
.safe-area-electric {
  background: linear-gradient(135deg, #60A5FA 0%, #3B82F6 100%);
}

.safe-area-green {
  background: linear-gradient(135deg, #34D399 0%, #10B981 100%);
}

.safe-area-teal {
  background: linear-gradient(135deg, #2DD4BF 0%, #14B8A6 100%);
}

.safe-area-pink {
  background: linear-gradient(135deg, #F472B6 0%, #EC4899 100%);
}

.safe-area-dark {
  background: linear-gradient(135deg, #374151 0%, #111827 100%);
}

/* Additional viewport height fixes for mobile */
@media (max-width: 768px) and (orientation: portrait) {
  html, body {
    height: 100vh;
    height: 100dvh; /* Dynamic viewport height for mobile */
    overscroll-behavior: none;
    -webkit-overflow-scrolling: touch;
  }

  #root {
    height: 100vh;
    height: 100dvh;
  }

  /* Ensure settings content doesn't get cut off on Android */
  .settings-content {
    padding-bottom: calc(env(safe-area-inset-bottom, 0px) + 4rem) !important;
    min-height: auto !important;
  }

  /* Additional padding for theme selector on mobile */
  .theme-selector-mobile {
    padding-bottom: calc(env(safe-area-inset-bottom, 0px) + 3rem) !important;
  }
}

/* Android-specific fixes for Sample Dashboard card content cutoff */
@media (max-width: 768px) {
  /* Ensure Sample Dashboard card content is fully visible on Android */
  .theme-preview-card {
    max-height: none !important;
    overflow: visible !important;
    margin-bottom: 1rem !important;
  }

  /* Specific fix for Sample Dashboard card in ThemeSelector */
  .theme-preview-card .space-y-3 {
    padding-bottom: 0.5rem !important;
  }

  /* Ensure buttons in Sample Dashboard card are fully visible */
  .theme-preview-card .flex.space-x-2 {
    margin-bottom: 0.5rem !important;
    flex-wrap: wrap !important;
    gap: 0.5rem !important;
  }

  /* Android viewport fix for settings page */
  .settings-page-android {
    min-height: calc(100vh - env(safe-area-inset-top, 0px) - env(safe-area-inset-bottom, 0px)) !important;
    padding-bottom: calc(env(safe-area-inset-bottom, 0px) + 2rem) !important;
  }
}

/* Swipeable layout styles */
.swipeable-container {
  touch-action: pan-x;
  -webkit-overflow-scrolling: touch;
  overscroll-behavior-x: contain;
}

/* Smooth transitions for swipe animations */
.swipe-transition {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Prevent text selection during swipe */
.swipe-no-select {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Enhanced touch targets for mobile swipe indicators */
@media (max-width: 768px) {
  .swipe-indicator {
    min-height: 44px;
    min-width: 44px;
    touch-action: manipulation;
  }

  /* Improve swipe gesture recognition - Allow vertical scrolling */
  .swipeable-content {
    touch-action: pan-y;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }

  /* Fix mobile scrolling and content overflow */
  .swipeable-container {
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
  }

  /* Ensure cards can expand to their natural height */
  .mobile-card {
    min-height: auto !important;
    height: auto !important;
  }

  /* Better mobile content spacing - account for fixed footer nav */
  .mobile-content {
    padding-bottom: calc(env(safe-area-inset-bottom, 0px) + 4rem);
  }

  /* Force auto height for all cards on mobile */
  .mobile-card-auto {
    height: auto !important;
    min-height: auto !important;
  }

  /* Ensure proper scrolling behavior */
  body {
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
  }
}

/* Prevent zoom on input focus and ensure proper safe areas */
input, select, textarea {
  font-size: 16px;
  -webkit-user-select: text;
  user-select: text;
}

/* Disable number input spinners */
input[type="number"]::-webkit-outer-spin-button,
input[type="number"]::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

input[type="number"] {
  -moz-appearance: textfield;
}

/* Better paragraph spacing throughout the app */
.space-y-paragraphs > * + * {
  margin-top: 1rem;
}

.space-y-paragraphs-sm > * + * {
  margin-top: 0.75rem;
}

.space-y-paragraphs-lg > * + * {
  margin-top: 1.5rem;
}

/* Improve spacing for help text and descriptions */
.help-text p + p,
.description-text p + p {
  margin-top: 0.75rem;
}

/* Better spacing for list items in help sections */
.help-list li + li {
  margin-top: 0.5rem;
}

/* Proper bullet point alignment - bullet to the left, text wrapping underneath */
.bullet-list-aligned li {
  display: flex;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.bullet-list-aligned li::before {
  content: "•";
  margin-right: 0.75rem;
  flex-shrink: 0;
  line-height: 1.5;
}

.bullet-list-aligned li span,
.bullet-list-aligned li p {
  line-height: 1.5;
}
