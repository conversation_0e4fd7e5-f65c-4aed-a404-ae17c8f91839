import sharp from 'sharp';
import fs from 'fs';
import path from 'path';

// Android icon sizes and their directories
const androidIcons = [
  { size: 48, density: 'mdpi' },
  { size: 72, density: 'hdpi' },
  { size: 96, density: 'xhdpi' },
  { size: 144, density: 'xxhdpi' },
  { size: 192, density: 'xxxhdpi' }
];

const sourceIcon = 'resources/icon.png';

async function generateAndroidIcons() {
  console.log('🎨 Generating Android icons from your custom logo...');

  try {
    // Check if source icon exists
    if (!fs.existsSync(sourceIcon)) {
      console.error('❌ Source icon not found:', sourceIcon);
      return;
    }

    console.log('✅ Found source icon:', sourceIcon);

    // Generate regular icons for all densities
    for (const icon of androidIcons) {
      const outputDir = `android/app/src/main/res/mipmap-${icon.density}`;

      // Ensure directory exists
      if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir, { recursive: true });
        console.log(`📁 Created directory: ${outputDir}`);
      }

      // Generate regular launcher icon
      const launcherPath = path.join(outputDir, 'ic_launcher.png');
      await sharp(sourceIcon)
        .resize(icon.size, icon.size, {
          fit: 'contain',
          background: { r: 255, g: 255, b: 255, alpha: 0 }
        })
        .png()
        .toFile(launcherPath);

      // Generate round launcher icon
      const roundPath = path.join(outputDir, 'ic_launcher_round.png');
      await sharp(sourceIcon)
        .resize(icon.size, icon.size, {
          fit: 'contain',
          background: { r: 255, g: 255, b: 255, alpha: 0 }
        })
        .png()
        .toFile(roundPath);

      // Generate adaptive icon background (white background)
      const backgroundPath = path.join(outputDir, 'ic_launcher_background.png');
      await sharp({
        create: {
          width: icon.size,
          height: icon.size,
          channels: 4,
          background: { r: 255, g: 255, b: 255, alpha: 255 }
        }
      })
        .png()
        .toFile(backgroundPath);

      // Generate adaptive icon foreground (your logo centered)
      const foregroundPath = path.join(outputDir, 'ic_launcher_foreground.png');
      const foregroundSize = Math.round(icon.size * 0.6); // 60% of the icon size for proper adaptive icon proportions
      const padding = Math.round((icon.size - foregroundSize) / 2);

      await sharp({
        create: {
          width: icon.size,
          height: icon.size,
          channels: 4,
          background: { r: 0, g: 0, b: 0, alpha: 0 }
        }
      })
        .composite([{
          input: await sharp(sourceIcon)
            .resize(foregroundSize, foregroundSize, {
              fit: 'contain',
              background: { r: 0, g: 0, b: 0, alpha: 0 }
            })
            .png()
            .toBuffer(),
          top: padding,
          left: padding
        }])
        .png()
        .toFile(foregroundPath);

      console.log(`✅ Generated icons for ${icon.density} (${icon.size}x${icon.size})`);
    }

    console.log('🎉 All Android icons generated successfully!');
    console.log('📱 Your custom logo will now appear as the app icon');

  } catch (error) {
    console.error('❌ Error generating icons:', error);
    console.error(error.stack);
  }
}

generateAndroidIcons();
