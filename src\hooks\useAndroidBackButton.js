import { useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { App } from '@capacitor/app';
import { Capacitor } from '@capacitor/core';

export const useAndroidBackButton = () => {
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    // Only handle back button on Android
    if (!Capacitor.isNativePlatform() || Capacitor.getPlatform() !== 'android') {
      return;
    }

    const handleBackButton = () => {
      const currentPath = location.pathname;
      
      // Define navigation hierarchy
      const navigationStack = {
        '/purchases': '/',
        '/usage': '/',
        '/history': '/',
        '/settings': '/'
      };

      // If we're on a sub-page, navigate back to dashboard
      if (navigationStack[currentPath]) {
        navigate(navigationStack[currentPath]);
        return;
      }

      // If we're on dashboard, exit the app
      if (currentPath === '/') {
        App.exitApp();
        return;
      }

      // Default: go back to dashboard
      navigate('/');
    };

    // Add the back button listener
    const backButtonListener = App.addListener('backButton', handleBackButton);

    // Cleanup listener on unmount
    return () => {
      backButtonListener.remove();
    };
  }, [navigate, location.pathname]);
};
