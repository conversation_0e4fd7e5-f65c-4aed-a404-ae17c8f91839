import { useTheme } from '../../context/ThemeContext'

function LoadingSpinner({ size = 'md', text = 'Loading...' }) {
  const { theme } = useTheme()

  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-8 w-8',
    lg: 'h-12 w-12',
    xl: 'h-16 w-16'
  }

  return (
    <div className={`flex flex-col items-center justify-center p-8 ${theme.background}`}>
      <div className="relative">
        <div className={`${sizeClasses[size]} border-4 border-gray-200 rounded-full animate-spin`}>
          <div className={`absolute inset-0 border-4 border-transparent border-t-blue-500 rounded-full animate-spin`}></div>
        </div>
      </div>
      {text && (
        <p className={`mt-4 text-sm font-medium ${theme.textSecondary} animate-pulse`}>
          {text}
        </p>
      )}
    </div>
  )
}

export default LoadingSpinner
