import { useLocation, useNavigate } from 'react-router-dom'
import { useTheme } from '../../context/ThemeContext'
import { HiHome, HiCurrencyDollar, HiTrendingUp, HiClipboardList } from 'react-icons/hi'

function MobileFooterNav() {
  const location = useLocation()
  const navigate = useNavigate()
  const { currentTheme } = useTheme()

  // Helper function to get appropriate background for footer with glassmorphism
  const getFooterBackground = () => {
    if (currentTheme === 'dark') {
      return 'bg-gray-900/95 backdrop-blur-xl border-gray-700/50'
    }

    // For non-dark themes, use specific color classes with proper opacity (darker 550 shade with 95% opacity)
    const backgroundMap = {
      'electric': 'bg-blue-550/95 backdrop-blur-xl border-white/30',
      'green': 'bg-green-550/95 backdrop-blur-xl border-white/30',
      'teal': 'bg-teal-550/95 backdrop-blur-xl border-white/30',
      'pink': 'bg-pink-550/95 backdrop-blur-xl border-white/30'
    }

    return backgroundMap[currentTheme] || 'bg-blue-550/95 backdrop-blur-xl border-white/30'
  }

  const navItems = [
    {
      id: 'dashboard',
      path: '/',
      icon: HiHome,
      label: 'Dashboard'
    },
    {
      id: 'purchases',
      path: '/purchases',
      icon: HiCurrencyDollar,
      label: 'Purchases'
    },
    {
      id: 'usage',
      path: '/usage',
      icon: HiTrendingUp,
      label: 'Usage'
    },
    {
      id: 'history',
      path: '/history',
      icon: HiClipboardList,
      label: 'History'
    }
  ]

  const isActive = (path) => {
    if (path === '/') {
      return location.pathname === '/' || location.pathname === '/dashboard'
    }
    return location.pathname === path
  }

  return (
    <div className={`md:hidden ${getFooterBackground()} border-t shadow-xl`}
         style={{
           paddingBottom: 'calc(env(safe-area-inset-bottom, 12px) + 0.5rem)',
           position: 'fixed',
           bottom: 0,
           left: 0,
           right: 0,
           zIndex: 50
         }}>
      <div className="flex items-center justify-around px-1 py-1">
        {navItems.map((item) => {
          const Icon = item.icon
          const active = isActive(item.path)

          return (
            <button
              key={item.id}
              onClick={() => navigate(item.path)}
              className={`flex flex-col items-center justify-center p-1.5 rounded-lg transition-all duration-200 min-w-0 flex-1 ${
                active
                  ? `bg-white/20 text-white shadow-lg transform scale-105 border border-white/30`
                  : `text-white/80 hover:bg-white/10 hover:text-white`
              }`}
              style={{ minHeight: '36px' }} // Reduced from 44px but still accessible
            >
              <Icon className={`h-3.5 w-3.5 ${active ? 'text-white' : 'text-white/80'}`} />
              <span className={`text-xs font-medium mt-0.5 truncate ${
                active ? 'text-white' : 'text-white/80'
              }`}>
                {item.label}
              </span>
            </button>
          )
        })}
      </div>
    </div>
  )
}

export default MobileFooterNav
