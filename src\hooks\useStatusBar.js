import { useEffect } from 'react';
import { StatusBar } from '@capacitor/status-bar';
import { NavigationBar } from '@capgo/capacitor-navigation-bar';
import { Capacitor } from '@capacitor/core';
import { useTheme } from '../context/ThemeContext';

export function useStatusBar() {
  const { currentTheme } = useTheme();

  useEffect(() => {
    // Only run on mobile platforms
    if (!Capacitor.isNativePlatform()) {
      return;
    }

    const updateStatusBar = async () => {
      try {
        // Force status bar to not overlay content - this should create proper spacing
        await StatusBar.setOverlaysWebView({ overlay: false });

        // Show the status bar first to ensure it's visible
        await StatusBar.show();

        // Ensure navigation bar is visible and not hidden
        try {
          await NavigationBar.show();
        } catch (navShowError) {
          console.log('NavigationBar.show() not available:', navShowError);
        }

        // Define lighter theme colors for status bar and navigation bar (lighter than header 550 shades)
        const systemBarColors = {
          'dark': '#374151',     // gray-700 (lighter than app header gray-900)
          'electric': '#60a5fa', // blue-400 (lighter than app header blue-550)
          'green': '#34d399',    // green-400 (lighter than app header green-550)
          'teal': '#2dd4bf',     // teal-400 (lighter than app header teal-550)
          'pink': '#f472b6'      // pink-400 (lighter than app header pink-550)
        };

        // Get the lighter theme color for both status bar and navigation bar
        const systemBarColor = systemBarColors[currentTheme] || '#60a5fa';

        // Configure status bar - always use light content since all themes use dark backgrounds
        await StatusBar.setStyle({ style: 'LIGHT' });
        await StatusBar.setBackgroundColor({ color: systemBarColor });

        // Configure navigation bar to use the same lighter color as status bar
        console.log('Setting navigation bar color to:', systemBarColor);
        await NavigationBar.setColor({
          color: systemBarColor,
          darkButtons: false // Light buttons for all themes since we use dark backgrounds
        });

        // Also try setting navigation bar background color directly
        try {
          await NavigationBar.setNavigationBarColor({ color: systemBarColor });
        } catch (navError) {
          console.log('Alternative navigation bar method not available:', navError);
        }

      } catch (error) {
        console.log('StatusBar or NavigationBar plugin not available:', error);
      }
    };

    updateStatusBar();
  }, [currentTheme]);
}
