import { useNavigate } from 'react-router-dom'
import { useApp } from '../../context/AppContext'
import { useTheme } from '../../context/ThemeContext'
import UsageDial from './UsageDial'
import ThresholdWarning from './ThresholdWarning'
import NativeAd from './NativeAd'

import SwipeableLayout from '../Common/SwipeableLayout'
import { HiLightningBolt, HiCurrencyDollar, HiTrendingUp } from 'react-icons/hi'

function Dashboard() {
  const navigate = useNavigate()
  const {
    state,
    isThresholdExceeded,
    getDisplayUnitName
  } = useApp()
  const { theme } = useTheme()

  // Left content for swipeable layout
  const leftContent = (
    <div className="space-y-6">
          {/* Usage dial with embedded cards */}
          <div className={`${theme.card} rounded-2xl shadow-lg p-4 md:p-6 lg:p-8 border ${theme.border} w-full`}>
            <UsageDial />
          </div>

          {/* Native Ad */}
          <NativeAd />

          {/* Quick actions */}
          <div className={`${theme.card} rounded-2xl shadow-lg p-6 lg:p-8 border ${theme.border}`}>
            <h2 className={`text-xl lg:text-2xl font-bold ${theme.text} mb-6 flex items-center gap-3`}>
              <div className={`p-3 lg:p-4 rounded-xl bg-gradient-to-br ${theme.gradient} shadow-lg`}>
                <HiLightningBolt className="h-6 lg:h-7 w-6 lg:w-7 text-white" />
              </div>
              Quick Actions
            </h2>
            <div className="space-y-3">
              <button
                onClick={() => navigate('/purchases')}
                className={`w-full flex items-center gap-3 p-3 ${theme.primary} text-white rounded-xl hover:opacity-90 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]`}
              >
                <HiCurrencyDollar className="h-5 w-5" />
                <div className="text-left">
                  <span className="block text-sm font-semibold">Add Purchase</span>
                  <span className="block text-xs opacity-80">Top up your units</span>
                </div>
              </button>
              <button
                onClick={() => navigate('/usage')}
                className={`w-full flex items-center gap-3 p-3 ${theme.primary} text-white rounded-xl hover:opacity-90 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]`}
              >
                <HiTrendingUp className="h-5 w-5" />
                <div className="text-left">
                  <span className="block text-sm font-semibold">Record Usage</span>
                  <span className="block text-xs opacity-80">Track consumption</span>
                </div>
              </button>
              <button
                onClick={() => navigate('/history')}
                className={`w-full flex items-center gap-3 p-3 ${theme.primary} text-white rounded-xl hover:opacity-90 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]`}
              >
                <HiLightningBolt className="h-5 w-5" />
                <div className="text-left">
                  <span className="block text-sm font-semibold">View History</span>
                  <span className="block text-xs opacity-80">See all records</span>
                </div>
              </button>
            </div>
          </div>
    </div>
  )

  // Right content for swipeable layout
  const rightContent = (
    <div className={`${theme.card} rounded-2xl shadow-lg p-6 lg:p-8 border ${theme.border} flex flex-col`}>
      <h2 className={`text-xl lg:text-2xl font-bold ${theme.text} mb-6 flex items-center gap-3`}>
        <div className={`p-3 lg:p-4 rounded-xl bg-gradient-to-br ${theme.gradient} shadow-lg`}>
          <HiTrendingUp className="h-6 lg:h-7 w-6 lg:w-7 text-white" />
        </div>
        Recent Activity
      </h2>
      <div className="space-y-3">
              {/* Recent purchases - Compact */}
              {state.purchases.slice(0, 2).map((purchase) => (
                <div key={purchase.id} className={`p-3 ${theme.secondary} border ${theme.border} rounded-xl shadow-sm hover:shadow-md transition-all duration-200`}>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className={`p-2 rounded-lg ${theme.accent} shadow-sm`}>
                        <HiCurrencyDollar className="h-4 w-4 text-white" />
                      </div>
                      <div className="ml-3">
                        <p className={`text-sm font-semibold ${theme.text}`}>
                          Purchase: {state.currencySymbol || 'R'}{purchase.currency.toFixed(2)}
                        </p>
                        <p className={`text-xs ${theme.textSecondary}`}>
                          {new Date(purchase.date).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                    <span className={`text-sm font-semibold ${theme.text}`}>
                      +{purchase.units.toFixed(2)} {getDisplayUnitName()}
                    </span>
                  </div>
                </div>
              ))}

              {/* Recent usage - Compact */}
              {state.usageHistory.slice(0, 1).map((usage) => (
                <div key={usage.id} className={`p-3 ${theme.secondary} border ${theme.border} rounded-xl shadow-sm hover:shadow-md transition-all duration-200`}>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className={`p-2 rounded-lg ${theme.accent} shadow-sm`}>
                        <HiTrendingUp className="h-4 w-4 text-white" />
                      </div>
                      <div className="ml-3">
                        <p className={`text-sm font-semibold ${theme.text}`}>
                          Usage recorded
                        </p>
                        <p className={`text-xs ${theme.textSecondary}`}>
                          {new Date(usage.date).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                    <span className={`text-sm font-semibold ${theme.text}`}>
                      -{usage.usage.toFixed(2)} {getDisplayUnitName()}
                    </span>
                  </div>
                </div>
              ))}

              {/* Empty state */}
              {state.purchases.length === 0 && state.usageHistory.length === 0 && (
                <div className={`text-center py-8 ${theme.secondary} border ${theme.border} rounded-xl`}>
                  <div className={`p-3 rounded-2xl bg-gradient-to-br ${theme.gradient} w-fit mx-auto mb-3`}>
                    <HiLightningBolt className={`h-8 w-8 text-white`} />
                  </div>
                  <p className={`text-sm ${theme.textSecondary} font-medium`}>
                    No recent activity
                  </p>
                  <p className={`text-xs ${theme.textSecondary} mt-1`}>
                    Start by making a purchase or recording usage
                  </p>
                </div>
              )}
      </div>
    </div>
  )

  return (
    <div className="w-full space-y-6 pb-6">
      {/* Threshold warning */}
      {isThresholdExceeded && <ThresholdWarning />}

      {/* Main content with swipeable layout */}
      <SwipeableLayout
        leftContent={leftContent}
        rightContent={rightContent}
        rightContentTitle="Recent Activity"
        className=""
      />
    </div>
  )
}

export default Dashboard
