import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],

  // Optimize dependency pre-bundling
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      'react-router-dom',
      'date-fns',
      'chart.js',
      'react-chartjs-2'
    ],
    exclude: []
  },

  // Build optimizations
  build: {
    // Reduce chunk size warning limit since we're optimizing
    chunkSizeWarningLimit: 1000,

    // Enable source maps for debugging (disable in production)
    sourcemap: false,

    // Optimize CSS
    cssCodeSplit: true,
    cssMinify: 'esbuild',

    // Rollup options for advanced bundling
    rollupOptions: {
      output: {
        // Manual chunk splitting for better caching
        manualChunks: {
          // Vendor chunk for React and core libraries
          vendor: [
            'react',
            'react-dom',
            'react-router-dom'
          ],

          // Chart.js in separate chunk (large library)
          charts: [
            'chart.js',
            'react-chartjs-2'
          ],

          // Date utilities
          utils: [
            'date-fns'
          ],

          // Icons in separate chunk
          icons: [
            'react-icons/hi'
          ]
        },

        // Optimize chunk file names
        chunkFileNames: (chunkInfo) => {
          const facadeModuleId = chunkInfo.facadeModuleId
          if (facadeModuleId) {
            const fileName = facadeModuleId.split('/').pop().replace(/\.[^/.]+$/, '')
            return `js/${fileName}-[hash].js`
          }
          return 'js/[name]-[hash].js'
        },

        // Optimize asset file names
        assetFileNames: (assetInfo) => {
          const info = assetInfo.name.split('.')
          const ext = info[info.length - 1]
          if (/png|jpe?g|svg|gif|tiff|bmp|ico/i.test(ext)) {
            return `images/[name]-[hash][extname]`
          }
          if (/css/i.test(ext)) {
            return `css/[name]-[hash][extname]`
          }
          return `assets/[name]-[hash][extname]`
        }
      }
    },

    // Minification settings
    minify: 'esbuild',

    // Target modern browsers for smaller bundles
    target: 'esnext'
  },

  // Server optimizations for development
  server: {
    // Warm up frequently used files
    warmup: {
      clientFiles: [
        './src/App.jsx',
        './src/components/Layout/Layout.jsx',
        './src/components/Dashboard/Dashboard.jsx',
        './src/context/AppContext.jsx',
        './src/context/ThemeContext.jsx'
      ]
    }
  },

  // CSS optimizations
  css: {
    // Enable CSS preprocessing in workers for better performance
    preprocessorMaxWorkers: true
  }
})
