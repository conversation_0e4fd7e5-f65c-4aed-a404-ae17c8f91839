import{a as U,j as e,t as y,u as I,p as A}from"../assets/index-DGp_uodO.js";import{r as n,f as V}from"./vendor-C67cHu0f.js";import{i as _,g as K,u as ee,o as v,v as se,H as te,l as L,f as F}from"./icons-Bhz3yUky.js";import"./utils-CgIdLkdF.js";function re(){const[r,h]=n.useState(!1),b=n.useRef(null),{currentTheme:s,setCurrentTheme:u,theme:a}=U(),i=d=>d?`${a.border} ring-2 ring-opacity-50`:s==="dark"?"border-gray-600 hover:border-gray-500":"border-gray-200 hover:border-gray-300";return n.useEffect(()=>{function d(m){b.current&&!b.current.contains(m.target)&&h(!1)}return document.addEventListener("mousedown",d),()=>{document.removeEventListener("mousedown",d)}},[]),e.jsxs("div",{className:"space-y-8 pb-8 md:pb-6 theme-selector-mobile",style:{paddingBottom:"calc(env(safe-area-inset-bottom, 0px) + 2rem)",minHeight:"auto"},children:[e.jsxs("div",{children:[e.jsxs("h3",{className:`text-lg font-semibold ${a.text} mb-4 flex items-center`,children:[e.jsx(_,{className:"mr-2 h-5 w-5"}),"Choose Theme"]}),e.jsxs("div",{className:"relative",ref:b,children:[e.jsxs("button",{onClick:()=>h(!r),className:`w-full p-4 ${a.card} border ${a.border} rounded-lg flex items-center justify-between hover:${a.secondary} transition-colors min-w-0`,children:[e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("div",{className:`h-6 w-16 ${y[s].gradient} bg-gradient-to-r rounded`}),e.jsxs("div",{className:"flex space-x-1",children:[e.jsx("div",{className:`h-2 w-2 ${y[s].primary} rounded`}),e.jsx("div",{className:`h-2 w-2 ${y[s].accent} rounded`}),e.jsx("div",{className:`h-2 w-2 ${y[s].secondary} rounded`})]})]}),e.jsx("span",{className:`text-lg font-medium ${a.text}`,children:y[s].name})]}),e.jsx(K,{className:`h-5 w-5 ${a.textSecondary} transition-transform ${r?"rotate-180":""}`})]}),r&&e.jsx("div",{className:`absolute top-full left-0 right-0 mt-2 ${a.card} border ${a.border} rounded-lg shadow-lg z-50 max-h-80 overflow-y-auto`,children:e.jsx("div",{className:"grid grid-cols-1 gap-2 p-2 w-full",children:Object.entries(y).map(([d,m])=>e.jsx("button",{onClick:()=>{u(d),h(!1)},className:`relative p-4 rounded-lg border-2 transition-all hover:scale-105 text-left w-full ${i(s===d)}`,children:e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsxs("div",{className:"space-y-2 flex-shrink-0",children:[e.jsx("div",{className:`h-8 w-20 ${m.gradient} bg-gradient-to-r rounded`}),e.jsxs("div",{className:"flex space-x-1",children:[e.jsx("div",{className:`h-3 w-3 ${m.primary} rounded`}),e.jsx("div",{className:`h-3 w-3 ${m.accent} rounded`}),e.jsx("div",{className:`h-3 w-3 ${m.secondary} rounded`})]})]}),e.jsx("div",{className:"flex-1",children:e.jsx("p",{className:`text-sm font-medium ${a.text}`,children:m.name})}),s===d&&e.jsx("div",{className:"flex-shrink-0",children:e.jsx(ee,{className:`h-5 w-5 ${a.text}`})})]})},d))})})]})]}),e.jsxs("div",{children:[e.jsx("h3",{className:`text-lg font-semibold ${a.text} mb-4`,children:"Preview"}),e.jsxs("div",{className:`p-4 ${a.card} rounded-lg border ${a.border} space-y-3 theme-preview-card`,children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h4",{className:`text-lg font-bold ${a.text}`,children:"Sample Dashboard"}),e.jsx("div",{className:`px-2 py-1 ${a.primary} text-white rounded-full text-sm`,children:"Active"})]}),e.jsx("div",{className:"grid grid-cols-1 gap-3",children:e.jsxs("div",{className:`p-3 ${a.card} border ${a.border} rounded-lg shadow-sm h-16`,children:[e.jsx("p",{className:`text-xs ${a.textSecondary}`,children:"Current Units"}),e.jsx("p",{className:`text-lg font-bold ${a.text}`,children:"125.50"})]})}),e.jsxs("div",{className:"flex space-x-2",children:[e.jsx("button",{className:`px-3 py-2 ${a.primary} text-white rounded-lg text-sm h-10`,children:"Primary Button"}),e.jsx("button",{className:`px-3 py-2 border ${a.border} ${a.text} rounded-lg text-sm h-10`,children:"Secondary Button"})]})]})]}),e.jsxs("div",{children:[e.jsx("h3",{className:`text-lg font-semibold ${a.text} mb-4`,children:"Reset Appearance"}),e.jsx("div",{className:"space-y-3",children:e.jsx("button",{onClick:()=>{u("electric"),h(!1)},className:`w-full px-6 py-3 bg-gradient-to-r ${a.gradient} text-white rounded-lg hover:opacity-90 transition-all duration-300 shadow-lg hover:shadow-xl`,children:"Reset to Default Theme"})})]})]})}function ae(){const[r,h]=n.useState(!1),[b,s]=n.useState(!1),[u,a]=n.useState(!1),{state:i,factoryReset:d,dashboardReset:m}=I(),{theme:l,currentTheme:c}=U(),o=(f,p="bg-gray-800/50")=>c==="dark"?p:f,C=async()=>{a(!0);try{d(),h(!1),alert("Factory reset completed successfully! The app will now restart.")}catch(f){console.error("Error during factory reset:",f),alert("Error during factory reset. Please try again.")}finally{a(!1)}},j=async()=>{a(!0);try{m(),s(!1),alert("Dashboard data reset successfully! Your history has been preserved.")}catch(f){console.error("Error during dashboard reset:",f),alert("Error during dashboard reset. Please try again.")}finally{a(!1)}};return e.jsxs("div",{className:"space-y-6 max-w-none",children:[e.jsx("div",{className:`p-4 md:p-6 border ${l.border} rounded-lg ${o("bg-white","bg-gray-800/50")} w-full max-w-none`,children:e.jsxs("div",{className:"w-full",children:[e.jsx("h3",{className:`text-lg font-semibold ${l.text}`,children:"Dashboard Data Reset"}),e.jsx("p",{className:`mt-2 text-sm ${l.textSecondary}`,children:"Reset current units and previous readings to zero. This will clear your dashboard data but preserve your purchase and usage history for reference."}),e.jsxs("div",{className:`mt-4 p-4 ${o("bg-orange-50 border-orange-200","bg-orange-900/20 border-orange-700")} border rounded-lg`,children:[e.jsx("h4",{className:`text-sm font-medium mb-2 ${c==="dark"?"text-orange-300":"text-orange-800"}`,children:"What will be reset:"}),e.jsxs("ul",{className:`text-sm ${c==="dark"?"text-orange-200":"text-orange-700"}`,children:[e.jsxs("li",{className:"flex items-start",children:[e.jsx("span",{className:"mr-3 flex-shrink-0",children:"•"}),e.jsx("span",{children:"Current units will be set to 0"})]}),e.jsxs("li",{className:"flex items-start",children:[e.jsx("span",{className:"mr-3 flex-shrink-0",children:"•"}),e.jsx("span",{children:"Previous units will be set to 0"})]}),e.jsxs("li",{className:"flex items-start",children:[e.jsx("span",{className:"mr-3 flex-shrink-0",children:"•"}),e.jsx("span",{children:"Usage since last recording will be reset"})]})]}),e.jsx("h4",{className:`text-sm font-medium mt-3 mb-2 ${c==="dark"?"text-orange-300":"text-orange-800"}`,children:"What will be preserved:"}),e.jsxs("ul",{className:`text-sm ${c==="dark"?"text-orange-200":"text-orange-700"}`,children:[e.jsxs("li",{className:"flex items-start",children:[e.jsx("span",{className:"mr-3 flex-shrink-0",children:"•"}),e.jsx("span",{children:"All purchase history"})]}),e.jsxs("li",{className:"flex items-start",children:[e.jsx("span",{className:"mr-3 flex-shrink-0",children:"•"}),e.jsx("span",{children:"All usage history"})]}),e.jsxs("li",{className:"flex items-start",children:[e.jsx("span",{className:"mr-3 flex-shrink-0",children:"•"}),e.jsx("span",{children:"Settings and preferences"})]}),e.jsxs("li",{className:"flex items-start",children:[e.jsx("span",{className:"mr-3 flex-shrink-0",children:"•"}),e.jsx("span",{children:"Theme and appearance settings"})]})]})]}),b?e.jsxs("div",{className:"mt-4 space-y-3",children:[e.jsxs("div",{className:`flex items-center p-4 ${o("bg-red-50 border-red-200","bg-red-900/20 border-red-700")} border rounded-lg`,children:[e.jsx(v,{className:"h-6 w-6 text-red-600 mr-3"}),e.jsx("span",{className:`text-sm ${c==="dark"?"text-red-200":"text-red-800"}`,children:"Are you sure? This action cannot be undone."})]}),e.jsxs("div",{className:"flex space-x-3",children:[e.jsx("button",{onClick:j,disabled:u,className:"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50",children:u?"Resetting...":"Yes, Reset Dashboard"}),e.jsx("button",{onClick:()=>s(!1),className:`px-4 py-2 border ${l.border} ${l.text} rounded-lg hover:${l.secondary} transition-colors`,children:"Cancel"})]})]}):e.jsx("button",{onClick:()=>s(!0),className:"mt-4 px-6 py-3 text-sm bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors",children:"Reset Dashboard Data"})]})}),e.jsx("div",{className:`p-4 md:p-6 border ${o("border-red-200 bg-red-50","border-red-700 bg-red-900/20")} rounded-lg w-full max-w-none`,children:e.jsxs("div",{className:"w-full",children:[e.jsx("h3",{className:`text-lg font-semibold ${c==="dark"?"text-red-300":"text-red-800"}`,children:"Factory Reset"}),e.jsx("p",{className:`mt-2 text-sm ${c==="dark"?"text-red-200":"text-red-700"}`,children:"Completely reset the app to its initial state. This will delete ALL data including purchases, usage history, and settings. You will need to set up the app again from scratch."}),e.jsxs("div",{className:`mt-4 p-4 ${o("bg-red-100 border-red-300","bg-red-900/30 border-red-600")} border rounded-lg`,children:[e.jsx("h4",{className:`text-sm font-medium mb-2 ${c==="dark"?"text-red-300":"text-red-800"}`,children:"What will be deleted:"}),e.jsxs("ul",{className:`text-sm ${c==="dark"?"text-red-200":"text-red-700"}`,children:[e.jsxs("li",{className:"flex items-start",children:[e.jsx("span",{className:"mr-3 flex-shrink-0",children:"•"}),e.jsx("span",{children:"All purchase records"})]}),e.jsxs("li",{className:"flex items-start",children:[e.jsx("span",{className:"mr-3 flex-shrink-0",children:"•"}),e.jsx("span",{children:"All usage history"})]}),e.jsxs("li",{className:"flex items-start",children:[e.jsx("span",{className:"mr-3 flex-shrink-0",children:"•"}),e.jsx("span",{children:"Current and previous unit readings"})]}),e.jsxs("li",{className:"flex items-start",children:[e.jsx("span",{className:"mr-3 flex-shrink-0",children:"•"}),e.jsx("span",{children:"All settings and preferences"})]}),e.jsxs("li",{className:"flex items-start",children:[e.jsx("span",{className:"mr-3 flex-shrink-0",children:"•"}),e.jsx("span",{children:"Theme and appearance settings"})]})]}),e.jsxs("div",{className:`mt-3 p-3 ${o("bg-red-200 border-red-400","bg-red-900/40 border-red-500")} border rounded text-sm ${c==="dark"?"text-red-200":"text-red-800"}`,children:[e.jsx("strong",{children:"Warning:"})," This action is irreversible. Make sure you have backed up any important data."]})]}),r?e.jsxs("div",{className:"mt-4 space-y-3",children:[e.jsxs("div",{className:`flex items-center p-4 ${o("bg-red-100 border-red-300","bg-red-900/30 border-red-600")} border rounded-lg`,children:[e.jsx(v,{className:"h-6 w-6 text-red-700 mr-3"}),e.jsx("span",{className:`text-sm font-medium ${c==="dark"?"text-red-200":"text-red-800"}`,children:"This will permanently delete ALL your data. Are you absolutely sure?"})]}),e.jsxs("div",{className:"flex space-x-3",children:[e.jsx("button",{onClick:C,disabled:u,className:"px-4 py-2 bg-red-700 text-white rounded-lg hover:bg-red-800 transition-colors disabled:opacity-50",children:u?"Resetting...":"Yes, Delete Everything"}),e.jsx("button",{onClick:()=>h(!1),className:`px-4 py-2 border ${l.border} ${l.text} rounded-lg hover:${l.secondary} transition-colors`,children:"Cancel"})]})]}):e.jsx("button",{onClick:()=>h(!0),className:"mt-4 px-6 py-3 text-sm bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors",children:"Factory Reset"})]})}),e.jsxs("div",{className:`p-4 md:p-6 ${l.card} border ${l.border} rounded-lg w-full max-w-none`,children:[e.jsx("h3",{className:`text-lg font-semibold ${l.text} mb-4`,children:"Current Data Summary"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm",children:[e.jsxs("div",{children:[e.jsx("h4",{className:`font-medium ${l.text} mb-2`,children:"App Data"}),e.jsxs("ul",{className:`space-y-3 ${l.textSecondary}`,children:[e.jsxs("li",{className:"flex items-start",children:[e.jsx("span",{className:"mr-3 flex-shrink-0 leading-relaxed",children:"•"}),e.jsxs("span",{className:"leading-relaxed",children:["Current Units: ",i.currentUnits.toFixed(2)]})]}),e.jsxs("li",{className:"flex items-start",children:[e.jsx("span",{className:"mr-3 flex-shrink-0 leading-relaxed",children:"•"}),e.jsxs("span",{className:"leading-relaxed",children:["Previous Units: ",i.previousUnits.toFixed(2)]})]}),e.jsxs("li",{className:"flex items-start",children:[e.jsx("span",{className:"mr-3 flex-shrink-0 leading-relaxed",children:"•"}),e.jsxs("span",{className:"leading-relaxed",children:["Unit Cost: R",i.unitCost.toFixed(2)]})]}),e.jsxs("li",{className:"flex items-start",children:[e.jsx("span",{className:"mr-3 flex-shrink-0 leading-relaxed",children:"•"}),e.jsxs("span",{className:"leading-relaxed",children:["Threshold: ",i.thresholdLimit.toFixed(2)," units"]})]})]})]}),e.jsxs("div",{children:[e.jsx("h4",{className:`font-medium ${l.text} mb-2`,children:"History"}),e.jsxs("ul",{className:`space-y-3 ${l.textSecondary}`,children:[e.jsxs("li",{className:"flex items-start",children:[e.jsx("span",{className:"mr-3 flex-shrink-0 leading-relaxed",children:"•"}),e.jsxs("span",{className:"leading-relaxed",children:["Purchases: ",i.purchases.length," records"]})]}),e.jsxs("li",{className:"flex items-start",children:[e.jsx("span",{className:"mr-3 flex-shrink-0 leading-relaxed",children:"•"}),e.jsxs("span",{className:"leading-relaxed",children:["Usage Records: ",i.usageHistory.length," records"]})]}),e.jsxs("li",{className:"flex items-start",children:[e.jsx("span",{className:"mr-3 flex-shrink-0 leading-relaxed",children:"•"}),e.jsxs("span",{className:"leading-relaxed",children:["Last Reset: ",i.lastResetDate?new Date(i.lastResetDate).toLocaleDateString():"Never"]})]}),e.jsxs("li",{className:"flex items-start",children:[e.jsx("span",{className:"mr-3 flex-shrink-0 leading-relaxed",children:"•"}),e.jsxs("span",{className:"leading-relaxed",children:["App Initialized: ",i.isInitialized?"Yes":"No"]})]})]})]})]})]})]})}function de(){const{state:r,updateSettings:h,testNotification:b}=I(),{theme:s,currentTheme:u}=U(),[a]=V();console.log("🔧 Settings component loaded"),console.log("🔔 testNotification function available:",typeof b),console.log("🔔 Notification settings:",{enabled:r.notificationsEnabled,time:r.notificationTime,lastDate:r.lastNotificationDate}),n.useEffect(()=>{console.log("🌐 Browser notification support check:"),console.log("- Notification in window:","Notification"in window),"Notification"in window&&console.log("- Current permission:",Notification.permission),console.log("- User agent:",navigator.userAgent)},[]);const i=(t,x="bg-gray-800/50")=>u==="dark"?x:t,[d,m]=n.useState(r.unitCost.toString()),[l,c]=n.useState(r.thresholdLimit.toString()),[o,C]=n.useState(r.currency||"ZAR"),[j,f]=n.useState(r.customCurrencyName||""),[p,M]=n.useState(r.customCurrencySymbol||""),[g,H]=n.useState(r.unitName||"kWh"),[N,O]=n.useState(r.customUnitName||""),[w,W]=n.useState(r.notificationsEnabled||!1),[S,B]=n.useState(r.notificationTime||"18:00"),[R,D]=n.useState(!1),[E,P]=n.useState(!1),$=[{code:"ZAR",name:"South African Rand",symbol:"R"},{code:"USD",name:"US Dollar",symbol:"$"},{code:"EUR",name:"Euro",symbol:"€"},{code:"GBP",name:"British Pound",symbol:"£"},{code:"JPY",name:"Japanese Yen",symbol:"¥"},{code:"CUSTOM",name:"Custom Currency",symbol:"C"}],Y=[{value:"kWh",label:"kWh (Kilowatt Hours)"},{value:"Units",label:"Units"},{value:"custom",label:"Custom"}],q=async t=>{t.preventDefault(),D(!0);try{const x=parseFloat(d),T=parseFloat(l);if(isNaN(x)||x<=0){alert("Please enter a valid unit cost (greater than 0)");return}if(isNaN(T)||T<0){alert("Please enter a valid threshold limit (0 or greater)");return}if(g==="custom"&&!N.trim()){alert("Please enter a custom unit name");return}if(o==="CUSTOM"&&(!j.trim()||!p.trim())){alert("Please enter both custom currency name and symbol");return}const J=$.find(X=>X.code===o),Q=o==="CUSTOM"?p:J?.symbol||"R";h({unitCost:x,thresholdLimit:T,currency:o,currencySymbol:Q,customCurrencyName:o==="CUSTOM"?j.trim():"",customCurrencySymbol:o==="CUSTOM"?p.trim():"",unitName:g,customUnitName:g==="custom"?N.trim():"",notificationsEnabled:w,notificationTime:S}),alert("Settings saved successfully!")}catch(x){console.error("Error saving settings:",x),alert("Error saving settings. Please try again.")}finally{D(!1)}},Z=async()=>{P(!0);try{console.log("🔔 Settings: Testing notification..."),console.log("Current notification settings:",{enabled:w,time:S,lastNotification:r.lastNotificationDate});const t=await b();alert(t?"Test notification sent! Check your notification panel.":"Unable to send test notification. Please check your notification permissions in device settings.")}catch(t){console.error("Test notification error:",t),alert("Error sending test notification. Please try again.")}finally{P(!1)}},z=async()=>{if(console.log("🔔 Request Permission button clicked!"),!("Notification"in window)){alert("❌ This browser does not support notifications");return}if(console.log("🔔 Current permission:",Notification.permission),Notification.permission==="default")try{console.log("🔔 Requesting permission..."),alert('🔔 About to request notification permission. Please click "Allow" in the browser popup that appears.');const t=await Notification.requestPermission();if(console.log("🔔 Permission result:",t),t==="granted"){alert("✅ Success! Notifications are now enabled. You can test them using the buttons below.");try{const x=new Notification("🎉 Notifications Enabled!",{body:"You will now receive prepaid meter reminders.",icon:"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDJMMTMuMDkgOC4yNkwyMCA5TDEzLjA5IDE1Ljc0TDEyIDIyTDEwLjkxIDE1Ljc0TDQgOUwxMC45MSA4LjI2TDEyIDJaIiBmaWxsPSIjRkY2QjM1Ii8+Cjwvc3ZnPgo="});setTimeout(()=>x.close(),5e3)}catch(x){console.error("Test notification failed:",x)}}else alert(t==="denied"?`❌ Notifications were blocked. To enable them:

1. Click the 🔒 lock icon in your address bar
2. Set Notifications to "Allow"
3. Refresh the page and try again`:"⚠️ Notification permission not granted. Please try again.")}catch(t){console.error("🔔 Permission request failed:",t),alert("❌ Error requesting notification permission. Please try enabling notifications manually in your browser settings.")}else Notification.permission==="granted"?alert("✅ Notifications are already enabled! You can test them using the buttons below."):alert(`❌ Notifications are blocked. To enable them:

1. Click the 🔒 lock icon in your address bar
2. Set Notifications to "Allow"
3. Refresh the page and try again`)},[k,G]=n.useState("general");return n.useEffect(()=>{const t=a.get("section");t&&["general","appearance","reset"].includes(t)&&G(t)},[a]),e.jsx("div",{className:"space-y-6 pb-8 md:pb-6 settings-page-android",children:e.jsx("div",{className:`${s.card} rounded-2xl shadow-lg border ${s.border} w-full settings-content`,children:e.jsxs("div",{className:"p-4 md:p-6",children:[k==="general"&&e.jsx("div",{className:"space-y-6",children:e.jsxs("form",{onSubmit:q,className:"space-y-6",children:[e.jsxs("div",{className:`p-4 md:p-6 ${s.card} rounded-xl border ${s.border} shadow-sm w-full`,children:[e.jsxs("div",{className:"flex items-center mb-4",children:[e.jsx("div",{className:`p-2 rounded-lg bg-gradient-to-br ${s.gradient} mr-3`,children:e.jsx(se,{className:"h-5 w-5 text-white"})}),e.jsx("h3",{className:`font-semibold ${s.text} text-lg`,children:"Currency Settings"})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"currency",className:`block text-sm font-semibold ${s.text} mb-3`,children:"💰 Currency"}),e.jsx("select",{id:"currency",value:o,onChange:t=>C(t.target.value),className:`w-full px-4 py-4 border-4 ${s.border} rounded-xl focus:ring-4 focus:ring-opacity-50 focus:${s.border} ${s.card} ${s.text} font-bold shadow-lg hover:shadow-xl transition-all duration-200 min-w-0`,children:$.map(t=>e.jsxs("option",{value:t.code,children:[t.symbol," - ",t.name," (",t.code,")"]},t.code))}),e.jsx("p",{className:`mt-3 text-xs ${s.textSecondary} opacity-80 font-medium`,children:"💡 Select your preferred currency for cost calculations"}),o==="CUSTOM"&&e.jsxs("div",{className:"mt-4 space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"customCurrencyName",className:`block text-sm font-semibold ${s.text} mb-2`,children:"🏷️ Custom Currency Name"}),e.jsx("input",{type:"text",id:"customCurrencyName",value:j,onChange:t=>f(t.target.value),placeholder:"Enter currency name (e.g., Bitcoin, Credits, Points)",className:`w-full px-4 py-3 border-4 ${s.border} rounded-xl focus:ring-4 focus:ring-opacity-50 focus:${s.border} ${s.card} ${s.text} ${s.textSecondary} font-bold shadow-lg hover:shadow-xl transition-all duration-200 min-w-0`,required:o==="CUSTOM"})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"customCurrencySymbol",className:`block text-sm font-semibold ${s.text} mb-2`,children:"💰 Custom Currency Symbol"}),e.jsx("input",{type:"text",id:"customCurrencySymbol",value:p,onChange:t=>M(t.target.value),placeholder:"Enter symbol (e.g., ₿, Cr, Pts)",maxLength:"5",className:`w-full px-4 py-3 border-4 ${s.border} rounded-xl focus:ring-4 focus:ring-opacity-50 focus:${s.border} ${s.card} ${s.text} ${s.textSecondary} font-bold shadow-lg hover:shadow-xl transition-all duration-200 min-w-0`,required:o==="CUSTOM"}),e.jsxs("p",{className:`mt-3 text-xs ${s.textSecondary} opacity-80 font-medium`,children:['💰 This symbol will be displayed with all amounts (e.g., "',p||"Cr",'100.00")']})]})]})]})]}),e.jsxs("div",{className:`p-4 md:p-6 ${i("bg-gradient-to-br from-blue-50 to-indigo-50","bg-gray-800/50")} rounded-xl border ${s.border} shadow-sm w-full`,children:[e.jsxs("div",{className:"flex items-center mb-4",children:[e.jsx("div",{className:"p-2 rounded-lg bg-gradient-to-br from-blue-400 to-indigo-500 mr-3",children:e.jsx(te,{className:"h-5 w-5 text-white"})}),e.jsx("h3",{className:`font-semibold ${s.text} text-lg`,children:"Unit Settings"})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"unitName",className:`block text-sm font-semibold ${s.text} mb-3`,children:"⚡ Unit Name"}),e.jsx("select",{id:"unitName",value:g,onChange:t=>H(t.target.value),className:`w-full px-4 py-4 border-4 border-blue-300 rounded-xl focus:ring-4 focus:ring-blue-500 focus:border-blue-600 ${s.card} ${s.text} font-bold shadow-lg hover:shadow-xl transition-all duration-200 hover:border-blue-400 min-w-0`,children:Y.map(t=>e.jsx("option",{value:t.value,children:t.label},t.value))}),e.jsx("p",{className:`mt-3 text-xs ${s.textSecondary} opacity-80 font-medium`,children:"⚡ Choose how your units are displayed throughout the app"})]}),g==="custom"&&e.jsxs("div",{children:[e.jsx("label",{htmlFor:"customUnitName",className:`block text-sm font-semibold ${s.text} mb-3`,children:"🎯 Custom Unit Name"}),e.jsx("input",{type:"text",id:"customUnitName",value:N,onChange:t=>O(t.target.value),placeholder:"Enter custom unit name (e.g., Donkey, Credits, Points)",className:`w-full px-4 py-4 border-4 border-purple-300 rounded-xl focus:ring-4 focus:ring-purple-500 focus:border-purple-600 ${s.card} ${s.text} placeholder-gray-400 font-bold shadow-lg hover:shadow-xl transition-all duration-200 hover:border-purple-400 min-w-0`,required:g==="custom"}),e.jsxs("p",{className:`mt-3 text-xs ${s.textSecondary} opacity-80 font-medium`,children:['🎯 This name will be used everywhere (e.g., "Cost per ',N||"YourUnit",'")']})]})]})]}),e.jsxs("div",{className:`p-4 md:p-6 ${i("bg-gradient-to-br from-violet-50 to-purple-50","bg-gray-800/50")} rounded-xl border ${s.border} shadow-sm w-full`,children:[e.jsxs("div",{className:"flex items-center mb-4",children:[e.jsx("div",{className:"p-2 rounded-lg bg-gradient-to-br from-violet-400 to-purple-500 mr-3",children:e.jsx(L,{className:"h-5 w-5 text-white"})}),e.jsx("h3",{className:`font-semibold ${s.text} text-lg`,children:"Cost Settings"})]}),e.jsxs("div",{children:[e.jsxs("label",{htmlFor:"unitCost",className:`block text-sm font-semibold ${s.text} mb-3`,children:["💵 Cost per ",g==="custom"?N||"Unit":g]}),e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none",children:e.jsx("div",{className:"p-1 rounded-lg bg-gradient-to-br from-violet-400 to-purple-500",children:e.jsx(L,{className:"h-4 w-4 text-white"})})}),e.jsx("input",{type:"number",id:"unitCost",value:d,onChange:t=>m(t.target.value),onWheel:A,step:"0.01",min:"0.01",placeholder:"Enter cost per unit",className:`w-full pl-12 pr-4 py-4 border-4 border-violet-300 rounded-xl focus:ring-4 focus:ring-violet-500 focus:border-violet-600 ${s.card} ${s.text} placeholder-gray-400 font-bold shadow-lg hover:shadow-xl transition-all duration-200 hover:border-violet-400 min-w-0`,required:!0})]}),e.jsx("p",{className:`mt-3 text-xs ${s.textSecondary} opacity-80 font-medium`,children:"💡 This is used to calculate the cost of your electricity usage"})]})]}),e.jsxs("div",{className:`p-4 md:p-6 ${i("bg-gradient-to-br from-amber-50 to-yellow-50","bg-gray-800/50")} rounded-xl border ${s.border} shadow-sm w-full`,children:[e.jsxs("div",{className:"flex items-center mb-4",children:[e.jsx("div",{className:"p-2 rounded-lg bg-gradient-to-br from-amber-400 to-yellow-500 mr-3",children:e.jsx(v,{className:"h-5 w-5 text-white"})}),e.jsx("h3",{className:`font-semibold ${s.text} text-lg`,children:"Alert Settings"})]}),e.jsxs("div",{children:[e.jsxs("label",{htmlFor:"thresholdLimit",className:`block text-sm font-semibold ${s.text} mb-3`,children:["⚠️ Low ",g==="custom"?N||"Units":g," Warning Threshold"]}),e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none",children:e.jsx("div",{className:"p-1 rounded-lg bg-gradient-to-br from-amber-400 to-yellow-500",children:e.jsx(v,{className:"h-4 w-4 text-white"})})}),e.jsx("input",{type:"number",id:"thresholdLimit",value:l,onChange:t=>c(t.target.value),onWheel:A,step:"0.01",min:"0",placeholder:"Enter low units warning threshold",className:`w-full pl-12 pr-4 py-4 border-4 border-amber-300 rounded-xl focus:ring-4 focus:ring-amber-500 focus:border-amber-600 ${s.card} ${s.text} placeholder-gray-400 font-bold shadow-lg hover:shadow-xl transition-all duration-200 hover:border-amber-400 min-w-0`,required:!0})]}),e.jsx("p",{className:`mt-3 text-xs ${s.textSecondary} opacity-80 font-medium`,children:"⚠️ You'll receive a warning when your remaining units drop below this threshold"})]})]}),e.jsxs("div",{className:`p-4 md:p-6 ${i("bg-gradient-to-br from-slate-50 to-gray-50","bg-gray-800/50")} rounded-xl border ${s.border} shadow-sm w-full`,children:[e.jsxs("div",{className:"flex items-center mb-4",children:[e.jsx("div",{className:"p-2 rounded-lg bg-gradient-to-br from-gray-400 to-slate-500 mr-3",children:e.jsx(F,{className:"h-5 w-5 text-white"})}),e.jsx("h3",{className:`font-semibold ${s.text} text-lg`,children:"Current Settings Preview"})]}),e.jsxs("div",{className:"space-y-3 text-sm",children:[e.jsxs("div",{className:`flex justify-between items-center p-3 ${i("bg-white/60","bg-gray-700/50")} rounded-lg`,children:[e.jsx("span",{className:`${s.textSecondary} font-medium`,children:"Currency:"}),e.jsx("span",{className:`${s.text} font-bold`,children:r.currency==="CUSTOM"?`${r.customCurrencySymbol||"C"} - ${r.customCurrencyName||"Custom Currency"}`:`${$.find(t=>t.code===(r.currency||"ZAR"))?.symbol||"R"} - ${$.find(t=>t.code===(r.currency||"ZAR"))?.name||"South African Rand"}`})]}),e.jsxs("div",{className:`flex justify-between items-center p-3 ${i("bg-white/60","bg-gray-700/50")} rounded-lg`,children:[e.jsx("span",{className:`${s.textSecondary} font-medium`,children:"Unit Name:"}),e.jsx("span",{className:`${s.text} font-bold`,children:r.unitName==="custom"?r.customUnitName||"Units":r.unitName||"kWh"})]}),e.jsxs("div",{className:`flex justify-between items-center p-3 ${i("bg-white/60","bg-gray-700/50")} rounded-lg`,children:[e.jsx("span",{className:`${s.textSecondary} font-medium`,children:"Unit Cost:"}),e.jsxs("span",{className:`${s.text} font-bold`,children:[r.currencySymbol||"R",r.unitCost.toFixed(2)," per ",r.unitName==="custom"?r.customUnitName||"Unit":r.unitName||"kWh"]})]}),e.jsxs("div",{className:`flex justify-between items-center p-3 ${i("bg-white/60","bg-gray-700/50")} rounded-lg`,children:[e.jsx("span",{className:`${s.textSecondary} font-medium`,children:"Low Units Warning:"}),e.jsxs("span",{className:`${s.text} font-bold`,children:[r.thresholdLimit.toFixed(2)," ",r.unitName==="custom"?r.customUnitName||"Units":r.unitName||"kWh"]})]}),e.jsxs("div",{className:`flex justify-between items-center p-3 ${i("bg-white/60","bg-gray-700/50")} rounded-lg`,children:[e.jsx("span",{className:`${s.textSecondary} font-medium`,children:"Last Reset:"}),e.jsx("span",{className:`${s.text} font-bold`,children:r.lastResetDate?new Date(r.lastResetDate).toLocaleDateString():"Never"})]})]})]}),e.jsxs("div",{className:`p-4 md:p-6 ${i("bg-gradient-to-br from-indigo-50 to-purple-50","bg-gray-800/50")} rounded-xl border ${s.border} shadow-sm w-full`,children:[e.jsxs("div",{className:"flex items-center mb-4",children:[e.jsx("div",{className:"p-2 rounded-lg bg-gradient-to-br from-indigo-400 to-purple-500 mr-3",children:e.jsx(v,{className:"h-5 w-5 text-white"})}),e.jsx("h3",{className:`font-semibold ${s.text} text-lg`,children:"Notification Settings"})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:`p-4 rounded-lg border-2 ${typeof window<"u"&&"Notification"in window?Notification.permission==="granted"?"border-green-300 bg-green-50 dark:bg-green-900/20":Notification.permission==="denied"?"border-red-300 bg-red-50 dark:bg-red-900/20":"border-yellow-300 bg-yellow-50 dark:bg-yellow-900/20":"border-gray-300 bg-gray-50 dark:bg-gray-800/50"}`,children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:`text-2xl ${typeof window<"u"&&"Notification"in window?Notification.permission==="granted"?"text-green-600":Notification.permission==="denied"?"text-red-600":"text-yellow-600":"text-gray-600"}`,children:typeof window<"u"&&"Notification"in window?Notification.permission==="granted"?"✅":Notification.permission==="denied"?"❌":"⚠️":"❓"}),e.jsxs("div",{children:[e.jsx("h4",{className:`font-semibold ${s.text}`,children:"Browser Notification Status"}),e.jsx("p",{className:`text-sm ${s.textSecondary}`,children:typeof window<"u"&&"Notification"in window?Notification.permission==="granted"?"Notifications are enabled and working!":Notification.permission==="denied"?"Notifications are blocked. Please enable them manually.":"Notifications need permission. Click the button below.":"Checking notification support..."})]})]})}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("label",{className:`text-sm font-semibold ${s.text}`,children:"🔔 Daily Usage Reminders"}),e.jsx("p",{className:`text-xs ${s.textSecondary} opacity-80 mt-3`,children:"Get reminded to record your electricity usage every day"})]}),e.jsxs("label",{className:"relative inline-flex items-center cursor-pointer",children:[e.jsx("input",{type:"checkbox",checked:w,onChange:t=>W(t.target.checked),className:"sr-only peer","aria-label":"Enable daily usage reminder notifications"}),e.jsx("div",{className:`w-11 h-6 ${u==="dark"?"bg-gray-600":"bg-gray-200"} peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600`})]})]}),w&&e.jsxs("div",{children:[e.jsx("label",{htmlFor:"notificationTime",className:`block text-sm font-semibold ${s.text} mb-3`,children:"⏰ Reminder Time"}),e.jsx("input",{type:"time",id:"notificationTime",value:S,onChange:t=>B(t.target.value),className:`w-full px-4 py-4 border-4 border-indigo-300 rounded-xl focus:ring-4 focus:ring-indigo-500 focus:border-indigo-600 ${s.card} ${s.text} font-bold shadow-lg hover:shadow-xl transition-all duration-200 hover:border-indigo-400 min-w-0`}),e.jsx("p",{className:`mt-3 text-xs ${s.textSecondary} opacity-80 font-medium`,children:"⏰ You'll receive a notification at this time every day"})]}),w&&e.jsxs("div",{className:"pt-4 border-t border-gray-200 dark:border-gray-600",children:[e.jsx("button",{type:"button",onClick:Z,disabled:E,className:"w-full px-4 py-3 bg-gradient-to-r from-indigo-500 to-purple-600 text-white rounded-lg font-semibold hover:opacity-90 transition-all duration-300 focus:ring-4 focus:ring-indigo-300 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl",children:e.jsx("div",{className:"flex items-center justify-center gap-2",children:E?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"}),"Sending Test…"]}):e.jsx(e.Fragment,{children:"🔔 Test Notification"})})}),e.jsx("p",{className:`mt-2 text-xs ${s.textSecondary} opacity-80 text-center`,children:"Send a test notification to verify everything is working"}),e.jsxs("div",{className:"pt-4 border-t border-gray-200 dark:border-gray-600",children:[e.jsx("button",{type:"button",onClick:z,className:"w-full px-6 py-4 text-lg font-bold text-white bg-gradient-to-r from-red-500 to-pink-600 hover:from-red-600 hover:to-pink-700 rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98] focus:ring-4 focus:ring-red-300",children:e.jsx("div",{className:"flex items-center justify-center gap-3",children:"🔔 Grant Notification Permission"})}),e.jsxs("p",{className:`mt-3 text-sm ${s.textSecondary} text-center font-medium`,children:["⚠️ ",e.jsx("strong",{children:"Required:"})," Click to enable browser notifications for daily reminders"]})]})]})]})]}),e.jsx("button",{type:"submit",disabled:R,className:`w-full bg-gradient-to-r ${s.gradient} text-white py-4 px-6 rounded-xl font-semibold hover:opacity-90 transition-all duration-300 focus:ring-4 focus:ring-opacity-50 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]`,children:e.jsx("div",{className:"flex items-center justify-center gap-2",children:R?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent"}),"Saving Settings..."]}):e.jsxs(e.Fragment,{children:[e.jsx(F,{className:"h-5 w-5"}),"Save Settings"]})})})]})}),k==="appearance"&&e.jsx("div",{className:"space-y-6 pb-8 md:pb-6",children:e.jsx(re,{})}),k==="reset"&&e.jsx("div",{className:"space-y-6",children:e.jsx(ae,{})})]})})})}export{de as default};
