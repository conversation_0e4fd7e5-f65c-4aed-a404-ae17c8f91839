import { registerPlugin } from '@capacitor/core'

export interface AdMobNativePlugin {
  initialize(): Promise<{ success: boolean; error?: string }>
  loadNativeAd(options: { adUnitId: string }): Promise<{
    success: boolean
    error?: string
    adData?: {
      headline?: string
      body?: string
      callToAction?: string
      advertiser?: string
      icon?: string
      starRating?: number
      store?: string
      price?: string
    }
  }>
}

const AdMobNative = registerPlugin<AdMobNativePlugin>('AdMobNative', {
  web: () => import('./web').then(m => new m.AdMobNativeWeb()),
})

export default AdMobNative
