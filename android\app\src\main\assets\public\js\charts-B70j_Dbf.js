import{r as st,R as ks}from"./vendor-C67cHu0f.js";/*!
 * @kurkle/color v0.3.4
 * https://github.com/kurkle/color#readme
 * (c) 2024 <PERSON><PERSON>
 * Released under the MIT License
 */function te(i){return i+.5|0}const ht=(i,t,e)=>Math.max(Math.min(i,e),t);function Nt(i){return ht(te(i*2.55),0,255)}function ft(i){return ht(te(i*255),0,255)}function lt(i){return ht(te(i/2.55)/100,0,1)}function di(i){return ht(te(i*100),0,100)}const G={0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,A:10,B:11,C:12,D:13,E:14,F:15,a:10,b:11,c:12,d:13,e:14,f:15},je=[..."0123456789ABCDEF"],un=i=>je[i&15],gn=i=>je[(i&240)>>4]+je[i&15],ie=i=>(i&240)>>4===(i&15),pn=i=>ie(i.r)&&ie(i.g)&&ie(i.b)&&ie(i.a);function mn(i){var t=i.length,e;return i[0]==="#"&&(t===4||t===5?e={r:255&G[i[1]]*17,g:255&G[i[2]]*17,b:255&G[i[3]]*17,a:t===5?G[i[4]]*17:255}:(t===7||t===9)&&(e={r:G[i[1]]<<4|G[i[2]],g:G[i[3]]<<4|G[i[4]],b:G[i[5]]<<4|G[i[6]],a:t===9?G[i[7]]<<4|G[i[8]]:255})),e}const bn=(i,t)=>i<255?t(i):"";function _n(i){var t=pn(i)?un:gn;return i?"#"+t(i.r)+t(i.g)+t(i.b)+bn(i.a,t):void 0}const xn=/^(hsla?|hwb|hsv)\(\s*([-+.e\d]+)(?:deg)?[\s,]+([-+.e\d]+)%[\s,]+([-+.e\d]+)%(?:[\s,]+([-+.e\d]+)(%)?)?\s*\)$/;function ws(i,t,e){const s=t*Math.min(e,1-e),n=(o,r=(o+i/30)%12)=>e-s*Math.max(Math.min(r-3,9-r,1),-1);return[n(0),n(8),n(4)]}function yn(i,t,e){const s=(n,o=(n+i/60)%6)=>e-e*t*Math.max(Math.min(o,4-o,1),0);return[s(5),s(3),s(1)]}function vn(i,t,e){const s=ws(i,1,.5);let n;for(t+e>1&&(n=1/(t+e),t*=n,e*=n),n=0;n<3;n++)s[n]*=1-t-e,s[n]+=t;return s}function Mn(i,t,e,s,n){return i===n?(t-e)/s+(t<e?6:0):t===n?(e-i)/s+2:(i-t)/s+4}function Ue(i){const e=i.r/255,s=i.g/255,n=i.b/255,o=Math.max(e,s,n),r=Math.min(e,s,n),a=(o+r)/2;let l,c,h;return o!==r&&(h=o-r,c=a>.5?h/(2-o-r):h/(o+r),l=Mn(e,s,n,h,o),l=l*60+.5),[l|0,c||0,a]}function Ke(i,t,e,s){return(Array.isArray(t)?i(t[0],t[1],t[2]):i(t,e,s)).map(ft)}function qe(i,t,e){return Ke(ws,i,t,e)}function kn(i,t,e){return Ke(vn,i,t,e)}function wn(i,t,e){return Ke(yn,i,t,e)}function Ss(i){return(i%360+360)%360}function Sn(i){const t=xn.exec(i);let e=255,s;if(!t)return;t[5]!==s&&(e=t[6]?Nt(+t[5]):ft(+t[5]));const n=Ss(+t[2]),o=+t[3]/100,r=+t[4]/100;return t[1]==="hwb"?s=kn(n,o,r):t[1]==="hsv"?s=wn(n,o,r):s=qe(n,o,r),{r:s[0],g:s[1],b:s[2],a:e}}function On(i,t){var e=Ue(i);e[0]=Ss(e[0]+t),e=qe(e),i.r=e[0],i.g=e[1],i.b=e[2]}function Pn(i){if(!i)return;const t=Ue(i),e=t[0],s=di(t[1]),n=di(t[2]);return i.a<255?`hsla(${e}, ${s}%, ${n}%, ${lt(i.a)})`:`hsl(${e}, ${s}%, ${n}%)`}const fi={x:"dark",Z:"light",Y:"re",X:"blu",W:"gr",V:"medium",U:"slate",A:"ee",T:"ol",S:"or",B:"ra",C:"lateg",D:"ights",R:"in",Q:"turquois",E:"hi",P:"ro",O:"al",N:"le",M:"de",L:"yello",F:"en",K:"ch",G:"arks",H:"ea",I:"ightg",J:"wh"},ui={OiceXe:"f0f8ff",antiquewEte:"faebd7",aqua:"ffff",aquamarRe:"7fffd4",azuY:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"0",blanKedOmond:"ffebcd",Xe:"ff",XeviTet:"8a2be2",bPwn:"a52a2a",burlywood:"deb887",caMtXe:"5f9ea0",KartYuse:"7fff00",KocTate:"d2691e",cSO:"ff7f50",cSnflowerXe:"6495ed",cSnsilk:"fff8dc",crimson:"dc143c",cyan:"ffff",xXe:"8b",xcyan:"8b8b",xgTMnPd:"b8860b",xWay:"a9a9a9",xgYF:"6400",xgYy:"a9a9a9",xkhaki:"bdb76b",xmagFta:"8b008b",xTivegYF:"556b2f",xSange:"ff8c00",xScEd:"9932cc",xYd:"8b0000",xsOmon:"e9967a",xsHgYF:"8fbc8f",xUXe:"483d8b",xUWay:"2f4f4f",xUgYy:"2f4f4f",xQe:"ced1",xviTet:"9400d3",dAppRk:"ff1493",dApskyXe:"bfff",dimWay:"696969",dimgYy:"696969",dodgerXe:"1e90ff",fiYbrick:"b22222",flSOwEte:"fffaf0",foYstWAn:"228b22",fuKsia:"ff00ff",gaRsbSo:"dcdcdc",ghostwEte:"f8f8ff",gTd:"ffd700",gTMnPd:"daa520",Way:"808080",gYF:"8000",gYFLw:"adff2f",gYy:"808080",honeyMw:"f0fff0",hotpRk:"ff69b4",RdianYd:"cd5c5c",Rdigo:"4b0082",ivSy:"fffff0",khaki:"f0e68c",lavFMr:"e6e6fa",lavFMrXsh:"fff0f5",lawngYF:"7cfc00",NmoncEffon:"fffacd",ZXe:"add8e6",ZcSO:"f08080",Zcyan:"e0ffff",ZgTMnPdLw:"fafad2",ZWay:"d3d3d3",ZgYF:"90ee90",ZgYy:"d3d3d3",ZpRk:"ffb6c1",ZsOmon:"ffa07a",ZsHgYF:"20b2aa",ZskyXe:"87cefa",ZUWay:"778899",ZUgYy:"778899",ZstAlXe:"b0c4de",ZLw:"ffffe0",lime:"ff00",limegYF:"32cd32",lRF:"faf0e6",magFta:"ff00ff",maPon:"800000",VaquamarRe:"66cdaa",VXe:"cd",VScEd:"ba55d3",VpurpN:"9370db",VsHgYF:"3cb371",VUXe:"7b68ee",VsprRggYF:"fa9a",VQe:"48d1cc",VviTetYd:"c71585",midnightXe:"191970",mRtcYam:"f5fffa",mistyPse:"ffe4e1",moccasR:"ffe4b5",navajowEte:"ffdead",navy:"80",Tdlace:"fdf5e6",Tive:"808000",TivedBb:"6b8e23",Sange:"ffa500",SangeYd:"ff4500",ScEd:"da70d6",pOegTMnPd:"eee8aa",pOegYF:"98fb98",pOeQe:"afeeee",pOeviTetYd:"db7093",papayawEp:"ffefd5",pHKpuff:"ffdab9",peru:"cd853f",pRk:"ffc0cb",plum:"dda0dd",powMrXe:"b0e0e6",purpN:"800080",YbeccapurpN:"663399",Yd:"ff0000",Psybrown:"bc8f8f",PyOXe:"4169e1",saddNbPwn:"8b4513",sOmon:"fa8072",sandybPwn:"f4a460",sHgYF:"2e8b57",sHshell:"fff5ee",siFna:"a0522d",silver:"c0c0c0",skyXe:"87ceeb",UXe:"6a5acd",UWay:"708090",UgYy:"708090",snow:"fffafa",sprRggYF:"ff7f",stAlXe:"4682b4",tan:"d2b48c",teO:"8080",tEstN:"d8bfd8",tomato:"ff6347",Qe:"40e0d0",viTet:"ee82ee",JHt:"f5deb3",wEte:"ffffff",wEtesmoke:"f5f5f5",Lw:"ffff00",LwgYF:"9acd32"};function An(){const i={},t=Object.keys(ui),e=Object.keys(fi);let s,n,o,r,a;for(s=0;s<t.length;s++){for(r=a=t[s],n=0;n<e.length;n++)o=e[n],a=a.replace(o,fi[o]);o=parseInt(ui[r],16),i[a]=[o>>16&255,o>>8&255,o&255]}return i}let se;function Cn(i){se||(se=An(),se.transparent=[0,0,0,0]);const t=se[i.toLowerCase()];return t&&{r:t[0],g:t[1],b:t[2],a:t.length===4?t[3]:255}}const Dn=/^rgba?\(\s*([-+.\d]+)(%)?[\s,]+([-+.e\d]+)(%)?[\s,]+([-+.e\d]+)(%)?(?:[\s,/]+([-+.e\d]+)(%)?)?\s*\)$/;function Tn(i){const t=Dn.exec(i);let e=255,s,n,o;if(t){if(t[7]!==s){const r=+t[7];e=t[8]?Nt(r):ht(r*255,0,255)}return s=+t[1],n=+t[3],o=+t[5],s=255&(t[2]?Nt(s):ht(s,0,255)),n=255&(t[4]?Nt(n):ht(n,0,255)),o=255&(t[6]?Nt(o):ht(o,0,255)),{r:s,g:n,b:o,a:e}}}function Rn(i){return i&&(i.a<255?`rgba(${i.r}, ${i.g}, ${i.b}, ${lt(i.a)})`:`rgb(${i.r}, ${i.g}, ${i.b})`)}const De=i=>i<=.0031308?i*12.92:Math.pow(i,1/2.4)*1.055-.055,Ct=i=>i<=.04045?i/12.92:Math.pow((i+.055)/1.055,2.4);function Ln(i,t,e){const s=Ct(lt(i.r)),n=Ct(lt(i.g)),o=Ct(lt(i.b));return{r:ft(De(s+e*(Ct(lt(t.r))-s))),g:ft(De(n+e*(Ct(lt(t.g))-n))),b:ft(De(o+e*(Ct(lt(t.b))-o))),a:i.a+e*(t.a-i.a)}}function ne(i,t,e){if(i){let s=Ue(i);s[t]=Math.max(0,Math.min(s[t]+s[t]*e,t===0?360:1)),s=qe(s),i.r=s[0],i.g=s[1],i.b=s[2]}}function Os(i,t){return i&&Object.assign(t||{},i)}function gi(i){var t={r:0,g:0,b:0,a:255};return Array.isArray(i)?i.length>=3&&(t={r:i[0],g:i[1],b:i[2],a:255},i.length>3&&(t.a=ft(i[3]))):(t=Os(i,{r:0,g:0,b:0,a:1}),t.a=ft(t.a)),t}function En(i){return i.charAt(0)==="r"?Tn(i):Sn(i)}class qt{constructor(t){if(t instanceof qt)return t;const e=typeof t;let s;e==="object"?s=gi(t):e==="string"&&(s=mn(t)||Cn(t)||En(t)),this._rgb=s,this._valid=!!s}get valid(){return this._valid}get rgb(){var t=Os(this._rgb);return t&&(t.a=lt(t.a)),t}set rgb(t){this._rgb=gi(t)}rgbString(){return this._valid?Rn(this._rgb):void 0}hexString(){return this._valid?_n(this._rgb):void 0}hslString(){return this._valid?Pn(this._rgb):void 0}mix(t,e){if(t){const s=this.rgb,n=t.rgb;let o;const r=e===o?.5:e,a=2*r-1,l=s.a-n.a,c=((a*l===-1?a:(a+l)/(1+a*l))+1)/2;o=1-c,s.r=255&c*s.r+o*n.r+.5,s.g=255&c*s.g+o*n.g+.5,s.b=255&c*s.b+o*n.b+.5,s.a=r*s.a+(1-r)*n.a,this.rgb=s}return this}interpolate(t,e){return t&&(this._rgb=Ln(this._rgb,t._rgb,e)),this}clone(){return new qt(this.rgb)}alpha(t){return this._rgb.a=ft(t),this}clearer(t){const e=this._rgb;return e.a*=1-t,this}greyscale(){const t=this._rgb,e=te(t.r*.3+t.g*.59+t.b*.11);return t.r=t.g=t.b=e,this}opaquer(t){const e=this._rgb;return e.a*=1+t,this}negate(){const t=this._rgb;return t.r=255-t.r,t.g=255-t.g,t.b=255-t.b,this}lighten(t){return ne(this._rgb,2,t),this}darken(t){return ne(this._rgb,2,-t),this}saturate(t){return ne(this._rgb,1,t),this}desaturate(t){return ne(this._rgb,1,-t),this}rotate(t){return On(this._rgb,t),this}}/*!
 * Chart.js v4.4.9
 * https://www.chartjs.org
 * (c) 2025 Chart.js Contributors
 * Released under the MIT License
 */function ot(){}const Fn=(()=>{let i=0;return()=>i++})();function L(i){return i==null}function W(i){if(Array.isArray&&Array.isArray(i))return!0;const t=Object.prototype.toString.call(i);return t.slice(0,7)==="[object"&&t.slice(-6)==="Array]"}function C(i){return i!==null&&Object.prototype.toString.call(i)==="[object Object]"}function Q(i){return(typeof i=="number"||i instanceof Number)&&isFinite(+i)}function et(i,t){return Q(i)?i:t}function D(i,t){return typeof i>"u"?t:i}const In=(i,t)=>typeof i=="string"&&i.endsWith("%")?parseFloat(i)/100:+i/t,Ps=(i,t)=>typeof i=="string"&&i.endsWith("%")?parseFloat(i)/100*t:+i;function E(i,t,e){if(i&&typeof i.call=="function")return i.apply(e,t)}function T(i,t,e,s){let n,o,r;if(W(i))for(o=i.length,n=0;n<o;n++)t.call(e,i[n],n);else if(C(i))for(r=Object.keys(i),o=r.length,n=0;n<o;n++)t.call(e,i[r[n]],r[n])}function _e(i,t){let e,s,n,o;if(!i||!t||i.length!==t.length)return!1;for(e=0,s=i.length;e<s;++e)if(n=i[e],o=t[e],n.datasetIndex!==o.datasetIndex||n.index!==o.index)return!1;return!0}function xe(i){if(W(i))return i.map(xe);if(C(i)){const t=Object.create(null),e=Object.keys(i),s=e.length;let n=0;for(;n<s;++n)t[e[n]]=xe(i[e[n]]);return t}return i}function As(i){return["__proto__","prototype","constructor"].indexOf(i)===-1}function zn(i,t,e,s){if(!As(i))return;const n=t[i],o=e[i];C(n)&&C(o)?Gt(n,o,s):t[i]=xe(o)}function Gt(i,t,e){const s=W(t)?t:[t],n=s.length;if(!C(i))return i;e=e||{};const o=e.merger||zn;let r;for(let a=0;a<n;++a){if(r=s[a],!C(r))continue;const l=Object.keys(r);for(let c=0,h=l.length;c<h;++c)o(l[c],i,r,e)}return i}function Xt(i,t){return Gt(i,t,{merger:Bn})}function Bn(i,t,e){if(!As(i))return;const s=t[i],n=e[i];C(s)&&C(n)?Xt(s,n):Object.prototype.hasOwnProperty.call(t,i)||(t[i]=xe(n))}const pi={"":i=>i,x:i=>i.x,y:i=>i.y};function Hn(i){const t=i.split("."),e=[];let s="";for(const n of t)s+=n,s.endsWith("\\")?s=s.slice(0,-1)+".":(e.push(s),s="");return e}function Wn(i){const t=Hn(i);return e=>{for(const s of t){if(s==="")break;e=e&&e[s]}return e}}function wt(i,t){return(pi[t]||(pi[t]=Wn(t)))(i)}function Ge(i){return i.charAt(0).toUpperCase()+i.slice(1)}const Zt=i=>typeof i<"u",gt=i=>typeof i=="function",mi=(i,t)=>{if(i.size!==t.size)return!1;for(const e of i)if(!t.has(e))return!1;return!0};function Vn(i){return i.type==="mouseup"||i.type==="click"||i.type==="contextmenu"}const H=Math.PI,B=2*H,ye=Number.POSITIVE_INFINITY,jn=H/180,V=H/2,bt=H/4,bi=H*2/3,Cs=Math.log10,ut=Math.sign;function pe(i,t,e){return Math.abs(i-t)<e}function _i(i){const t=Math.round(i);i=pe(i,t,i/1e3)?t:i;const e=Math.pow(10,Math.floor(Cs(i))),s=i/e;return(s<=1?1:s<=2?2:s<=5?5:10)*e}function Nn(i){const t=[],e=Math.sqrt(i);let s;for(s=1;s<e;s++)i%s===0&&(t.push(s),t.push(i/s));return e===(e|0)&&t.push(e),t.sort((n,o)=>n-o).pop(),t}function $n(i){return typeof i=="symbol"||typeof i=="object"&&i!==null&&!(Symbol.toPrimitive in i||"toString"in i||"valueOf"in i)}function ve(i){return!$n(i)&&!isNaN(parseFloat(i))&&isFinite(i)}function Yn(i,t){const e=Math.round(i);return e-t<=i&&e+t>=i}function Xn(i,t,e){let s,n,o;for(s=0,n=i.length;s<n;s++)o=i[s][e],isNaN(o)||(t.min=Math.min(t.min,o),t.max=Math.max(t.max,o))}function ct(i){return i*(H/180)}function Un(i){return i*(180/H)}function xi(i){if(!Q(i))return;let t=1,e=0;for(;Math.round(i*t)/t!==i;)t*=10,e++;return e}function Ds(i,t){const e=t.x-i.x,s=t.y-i.y,n=Math.sqrt(e*e+s*s);let o=Math.atan2(s,e);return o<-.5*H&&(o+=B),{angle:o,distance:n}}function Kn(i,t){return Math.sqrt(Math.pow(t.x-i.x,2)+Math.pow(t.y-i.y,2))}function _t(i){return(i%B+B)%B}function Me(i,t,e,s){const n=_t(i),o=_t(t),r=_t(e),a=_t(o-n),l=_t(r-n),c=_t(n-o),h=_t(n-r);return n===o||n===r||s&&o===r||a>l&&c<h}function K(i,t,e){return Math.max(t,Math.min(e,i))}function qn(i){return K(i,-32768,32767)}function Mt(i,t,e,s=1e-6){return i>=Math.min(t,e)-s&&i<=Math.max(t,e)+s}function Ze(i,t,e){e=e||(r=>i[r]<t);let s=i.length-1,n=0,o;for(;s-n>1;)o=n+s>>1,e(o)?n=o:s=o;return{lo:n,hi:s}}const Ne=(i,t,e,s)=>Ze(i,e,s?n=>{const o=i[n][t];return o<e||o===e&&i[n+1][t]===e}:n=>i[n][t]<e),Gn=(i,t,e)=>Ze(i,e,s=>i[s][t]>=e);function Zn(i,t,e){let s=0,n=i.length;for(;s<n&&i[s]<t;)s++;for(;n>s&&i[n-1]>e;)n--;return s>0||n<i.length?i.slice(s,n):i}const Ts=["push","pop","shift","splice","unshift"];function Qn(i,t){if(i._chartjs){i._chartjs.listeners.push(t);return}Object.defineProperty(i,"_chartjs",{configurable:!0,enumerable:!1,value:{listeners:[t]}}),Ts.forEach(e=>{const s="_onData"+Ge(e),n=i[e];Object.defineProperty(i,e,{configurable:!0,enumerable:!1,value(...o){const r=n.apply(this,o);return i._chartjs.listeners.forEach(a=>{typeof a[s]=="function"&&a[s](...o)}),r}})})}function yi(i,t){const e=i._chartjs;if(!e)return;const s=e.listeners,n=s.indexOf(t);n!==-1&&s.splice(n,1),!(s.length>0)&&(Ts.forEach(o=>{delete i[o]}),delete i._chartjs)}function Rs(i){const t=new Set(i);return t.size===i.length?i:Array.from(t)}const Ls=function(){return typeof window>"u"?function(i){return i()}:window.requestAnimationFrame}();function Es(i,t){let e=[],s=!1;return function(...n){e=n,s||(s=!0,Ls.call(window,()=>{s=!1,i.apply(t,e)}))}}function Jn(i,t){let e;return function(...s){return t?(clearTimeout(e),e=setTimeout(i,t,s)):i.apply(this,s),t}}const Qe=i=>i==="start"?"left":i==="end"?"right":"center",$=(i,t,e)=>i==="start"?t:i==="end"?e:(t+e)/2,to=(i,t,e,s)=>i===(s?"left":"right")?e:i==="center"?(t+e)/2:t,oe=i=>i===0||i===1,vi=(i,t,e)=>-(Math.pow(2,10*(i-=1))*Math.sin((i-t)*B/e)),Mi=(i,t,e)=>Math.pow(2,-10*i)*Math.sin((i-t)*B/e)+1,Ut={linear:i=>i,easeInQuad:i=>i*i,easeOutQuad:i=>-i*(i-2),easeInOutQuad:i=>(i/=.5)<1?.5*i*i:-.5*(--i*(i-2)-1),easeInCubic:i=>i*i*i,easeOutCubic:i=>(i-=1)*i*i+1,easeInOutCubic:i=>(i/=.5)<1?.5*i*i*i:.5*((i-=2)*i*i+2),easeInQuart:i=>i*i*i*i,easeOutQuart:i=>-((i-=1)*i*i*i-1),easeInOutQuart:i=>(i/=.5)<1?.5*i*i*i*i:-.5*((i-=2)*i*i*i-2),easeInQuint:i=>i*i*i*i*i,easeOutQuint:i=>(i-=1)*i*i*i*i+1,easeInOutQuint:i=>(i/=.5)<1?.5*i*i*i*i*i:.5*((i-=2)*i*i*i*i+2),easeInSine:i=>-Math.cos(i*V)+1,easeOutSine:i=>Math.sin(i*V),easeInOutSine:i=>-.5*(Math.cos(H*i)-1),easeInExpo:i=>i===0?0:Math.pow(2,10*(i-1)),easeOutExpo:i=>i===1?1:-Math.pow(2,-10*i)+1,easeInOutExpo:i=>oe(i)?i:i<.5?.5*Math.pow(2,10*(i*2-1)):.5*(-Math.pow(2,-10*(i*2-1))+2),easeInCirc:i=>i>=1?i:-(Math.sqrt(1-i*i)-1),easeOutCirc:i=>Math.sqrt(1-(i-=1)*i),easeInOutCirc:i=>(i/=.5)<1?-.5*(Math.sqrt(1-i*i)-1):.5*(Math.sqrt(1-(i-=2)*i)+1),easeInElastic:i=>oe(i)?i:vi(i,.075,.3),easeOutElastic:i=>oe(i)?i:Mi(i,.075,.3),easeInOutElastic(i){return oe(i)?i:i<.5?.5*vi(i*2,.1125,.45):.5+.5*Mi(i*2-1,.1125,.45)},easeInBack(i){return i*i*((1.70158+1)*i-1.70158)},easeOutBack(i){return(i-=1)*i*((1.70158+1)*i********)+1},easeInOutBack(i){let t=1.70158;return(i/=.5)<1?.5*(i*i*(((t*=1.525)+1)*i-t)):.5*((i-=2)*i*(((t*=1.525)+1)*i+t)+2)},easeInBounce:i=>1-Ut.easeOutBounce(1-i),easeOutBounce(i){return i<1/2.75?7.5625*i*i:i<2/2.75?7.5625*(i-=1.5/2.75)*i+.75:i<2.5/2.75?7.5625*(i-=2.25/2.75)*i+.9375:7.5625*(i-=2.625/2.75)*i+.984375},easeInOutBounce:i=>i<.5?Ut.easeInBounce(i*2)*.5:Ut.easeOutBounce(i*2-1)*.5+.5};function Fs(i){if(i&&typeof i=="object"){const t=i.toString();return t==="[object CanvasPattern]"||t==="[object CanvasGradient]"}return!1}function ki(i){return Fs(i)?i:new qt(i)}function Te(i){return Fs(i)?i:new qt(i).saturate(.5).darken(.1).hexString()}const eo=["x","y","borderWidth","radius","tension"],io=["color","borderColor","backgroundColor"];function so(i){i.set("animation",{delay:void 0,duration:1e3,easing:"easeOutQuart",fn:void 0,from:void 0,loop:void 0,to:void 0,type:void 0}),i.describe("animation",{_fallback:!1,_indexable:!1,_scriptable:t=>t!=="onProgress"&&t!=="onComplete"&&t!=="fn"}),i.set("animations",{colors:{type:"color",properties:io},numbers:{type:"number",properties:eo}}),i.describe("animations",{_fallback:"animation"}),i.set("transitions",{active:{animation:{duration:400}},resize:{animation:{duration:0}},show:{animations:{colors:{from:"transparent"},visible:{type:"boolean",duration:0}}},hide:{animations:{colors:{to:"transparent"},visible:{type:"boolean",easing:"linear",fn:t=>t|0}}}})}function no(i){i.set("layout",{autoPadding:!0,padding:{top:0,right:0,bottom:0,left:0}})}const wi=new Map;function oo(i,t){t=t||{};const e=i+JSON.stringify(t);let s=wi.get(e);return s||(s=new Intl.NumberFormat(i,t),wi.set(e,s)),s}function Je(i,t,e){return oo(t,e).format(i)}const ro={values(i){return W(i)?i:""+i},numeric(i,t,e){if(i===0)return"0";const s=this.chart.options.locale;let n,o=i;if(e.length>1){const c=Math.max(Math.abs(e[0].value),Math.abs(e[e.length-1].value));(c<1e-4||c>1e15)&&(n="scientific"),o=ao(i,e)}const r=Cs(Math.abs(o)),a=isNaN(r)?1:Math.max(Math.min(-1*Math.floor(r),20),0),l={notation:n,minimumFractionDigits:a,maximumFractionDigits:a};return Object.assign(l,this.options.ticks.format),Je(i,s,l)}};function ao(i,t){let e=t.length>3?t[2].value-t[1].value:t[1].value-t[0].value;return Math.abs(e)>=1&&i!==Math.floor(i)&&(e=i-Math.floor(i)),e}var Is={formatters:ro};function lo(i){i.set("scale",{display:!0,offset:!1,reverse:!1,beginAtZero:!1,bounds:"ticks",clip:!0,grace:0,grid:{display:!0,lineWidth:1,drawOnChartArea:!0,drawTicks:!0,tickLength:8,tickWidth:(t,e)=>e.lineWidth,tickColor:(t,e)=>e.color,offset:!1},border:{display:!0,dash:[],dashOffset:0,width:1},title:{display:!1,text:"",padding:{top:4,bottom:4}},ticks:{minRotation:0,maxRotation:50,mirror:!1,textStrokeWidth:0,textStrokeColor:"",padding:3,display:!0,autoSkip:!0,autoSkipPadding:3,labelOffset:0,callback:Is.formatters.values,minor:{},major:{},align:"center",crossAlign:"near",showLabelBackdrop:!1,backdropColor:"rgba(255, 255, 255, 0.75)",backdropPadding:2}}),i.route("scale.ticks","color","","color"),i.route("scale.grid","color","","borderColor"),i.route("scale.border","color","","borderColor"),i.route("scale.title","color","","color"),i.describe("scale",{_fallback:!1,_scriptable:t=>!t.startsWith("before")&&!t.startsWith("after")&&t!=="callback"&&t!=="parser",_indexable:t=>t!=="borderDash"&&t!=="tickBorderDash"&&t!=="dash"}),i.describe("scales",{_fallback:"scale"}),i.describe("scale.ticks",{_scriptable:t=>t!=="backdropPadding"&&t!=="callback",_indexable:t=>t!=="backdropPadding"})}const St=Object.create(null),$e=Object.create(null);function Kt(i,t){if(!t)return i;const e=t.split(".");for(let s=0,n=e.length;s<n;++s){const o=e[s];i=i[o]||(i[o]=Object.create(null))}return i}function Re(i,t,e){return typeof t=="string"?Gt(Kt(i,t),e):Gt(Kt(i,""),t)}class co{constructor(t,e){this.animation=void 0,this.backgroundColor="rgba(0,0,0,0.1)",this.borderColor="rgba(0,0,0,0.1)",this.color="#666",this.datasets={},this.devicePixelRatio=s=>s.chart.platform.getDevicePixelRatio(),this.elements={},this.events=["mousemove","mouseout","click","touchstart","touchmove"],this.font={family:"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif",size:12,style:"normal",lineHeight:1.2,weight:null},this.hover={},this.hoverBackgroundColor=(s,n)=>Te(n.backgroundColor),this.hoverBorderColor=(s,n)=>Te(n.borderColor),this.hoverColor=(s,n)=>Te(n.color),this.indexAxis="x",this.interaction={mode:"nearest",intersect:!0,includeInvisible:!1},this.maintainAspectRatio=!0,this.onHover=null,this.onClick=null,this.parsing=!0,this.plugins={},this.responsive=!0,this.scale=void 0,this.scales={},this.showLine=!0,this.drawActiveElementsOnTop=!0,this.describe(t),this.apply(e)}set(t,e){return Re(this,t,e)}get(t){return Kt(this,t)}describe(t,e){return Re($e,t,e)}override(t,e){return Re(St,t,e)}route(t,e,s,n){const o=Kt(this,t),r=Kt(this,s),a="_"+e;Object.defineProperties(o,{[a]:{value:o[e],writable:!0},[e]:{enumerable:!0,get(){const l=this[a],c=r[n];return C(l)?Object.assign({},c,l):D(l,c)},set(l){this[a]=l}}})}apply(t){t.forEach(e=>e(this))}}var z=new co({_scriptable:i=>!i.startsWith("on"),_indexable:i=>i!=="events",hover:{_fallback:"interaction"},interaction:{_scriptable:!1,_indexable:!1}},[so,no,lo]);function ho(i){return!i||L(i.size)||L(i.family)?null:(i.style?i.style+" ":"")+(i.weight?i.weight+" ":"")+i.size+"px "+i.family}function Si(i,t,e,s,n){let o=t[n];return o||(o=t[n]=i.measureText(n).width,e.push(n)),o>s&&(s=o),s}function xt(i,t,e){const s=i.currentDevicePixelRatio,n=e!==0?Math.max(e/2,.5):0;return Math.round((t-n)*s)/s+n}function Oi(i,t){!t&&!i||(t=t||i.getContext("2d"),t.save(),t.resetTransform(),t.clearRect(0,0,i.width,i.height),t.restore())}function Pi(i,t,e,s){zs(i,t,e,s,null)}function zs(i,t,e,s,n){let o,r,a,l,c,h,d,f;const u=t.pointStyle,g=t.rotation,p=t.radius;let m=(g||0)*jn;if(u&&typeof u=="object"&&(o=u.toString(),o==="[object HTMLImageElement]"||o==="[object HTMLCanvasElement]")){i.save(),i.translate(e,s),i.rotate(m),i.drawImage(u,-u.width/2,-u.height/2,u.width,u.height),i.restore();return}if(!(isNaN(p)||p<=0)){switch(i.beginPath(),u){default:n?i.ellipse(e,s,n/2,p,0,0,B):i.arc(e,s,p,0,B),i.closePath();break;case"triangle":h=n?n/2:p,i.moveTo(e+Math.sin(m)*h,s-Math.cos(m)*p),m+=bi,i.lineTo(e+Math.sin(m)*h,s-Math.cos(m)*p),m+=bi,i.lineTo(e+Math.sin(m)*h,s-Math.cos(m)*p),i.closePath();break;case"rectRounded":c=p*.516,l=p-c,r=Math.cos(m+bt)*l,d=Math.cos(m+bt)*(n?n/2-c:l),a=Math.sin(m+bt)*l,f=Math.sin(m+bt)*(n?n/2-c:l),i.arc(e-d,s-a,c,m-H,m-V),i.arc(e+f,s-r,c,m-V,m),i.arc(e+d,s+a,c,m,m+V),i.arc(e-f,s+r,c,m+V,m+H),i.closePath();break;case"rect":if(!g){l=Math.SQRT1_2*p,h=n?n/2:l,i.rect(e-h,s-l,2*h,2*l);break}m+=bt;case"rectRot":d=Math.cos(m)*(n?n/2:p),r=Math.cos(m)*p,a=Math.sin(m)*p,f=Math.sin(m)*(n?n/2:p),i.moveTo(e-d,s-a),i.lineTo(e+f,s-r),i.lineTo(e+d,s+a),i.lineTo(e-f,s+r),i.closePath();break;case"crossRot":m+=bt;case"cross":d=Math.cos(m)*(n?n/2:p),r=Math.cos(m)*p,a=Math.sin(m)*p,f=Math.sin(m)*(n?n/2:p),i.moveTo(e-d,s-a),i.lineTo(e+d,s+a),i.moveTo(e+f,s-r),i.lineTo(e-f,s+r);break;case"star":d=Math.cos(m)*(n?n/2:p),r=Math.cos(m)*p,a=Math.sin(m)*p,f=Math.sin(m)*(n?n/2:p),i.moveTo(e-d,s-a),i.lineTo(e+d,s+a),i.moveTo(e+f,s-r),i.lineTo(e-f,s+r),m+=bt,d=Math.cos(m)*(n?n/2:p),r=Math.cos(m)*p,a=Math.sin(m)*p,f=Math.sin(m)*(n?n/2:p),i.moveTo(e-d,s-a),i.lineTo(e+d,s+a),i.moveTo(e+f,s-r),i.lineTo(e-f,s+r);break;case"line":r=n?n/2:Math.cos(m)*p,a=Math.sin(m)*p,i.moveTo(e-r,s-a),i.lineTo(e+r,s+a);break;case"dash":i.moveTo(e,s),i.lineTo(e+Math.cos(m)*(n?n/2:p),s+Math.sin(m)*p);break;case!1:i.closePath();break}i.fill(),t.borderWidth>0&&i.stroke()}}function Bs(i,t,e){return e=e||.5,!t||i&&i.x>t.left-e&&i.x<t.right+e&&i.y>t.top-e&&i.y<t.bottom+e}function ti(i,t){i.save(),i.beginPath(),i.rect(t.left,t.top,t.right-t.left,t.bottom-t.top),i.clip()}function ei(i){i.restore()}function fo(i,t){t.translation&&i.translate(t.translation[0],t.translation[1]),L(t.rotation)||i.rotate(t.rotation),t.color&&(i.fillStyle=t.color),t.textAlign&&(i.textAlign=t.textAlign),t.textBaseline&&(i.textBaseline=t.textBaseline)}function uo(i,t,e,s,n){if(n.strikethrough||n.underline){const o=i.measureText(s),r=t-o.actualBoundingBoxLeft,a=t+o.actualBoundingBoxRight,l=e-o.actualBoundingBoxAscent,c=e+o.actualBoundingBoxDescent,h=n.strikethrough?(l+c)/2:c;i.strokeStyle=i.fillStyle,i.beginPath(),i.lineWidth=n.decorationWidth||2,i.moveTo(r,h),i.lineTo(a,h),i.stroke()}}function go(i,t){const e=i.fillStyle;i.fillStyle=t.color,i.fillRect(t.left,t.top,t.width,t.height),i.fillStyle=e}function Qt(i,t,e,s,n,o={}){const r=W(t)?t:[t],a=o.strokeWidth>0&&o.strokeColor!=="";let l,c;for(i.save(),i.font=n.string,fo(i,o),l=0;l<r.length;++l)c=r[l],o.backdrop&&go(i,o.backdrop),a&&(o.strokeColor&&(i.strokeStyle=o.strokeColor),L(o.strokeWidth)||(i.lineWidth=o.strokeWidth),i.strokeText(c,e,s,o.maxWidth)),i.fillText(c,e,s,o.maxWidth),uo(i,e,s,c,o),s+=Number(n.lineHeight);i.restore()}function ke(i,t){const{x:e,y:s,w:n,h:o,radius:r}=t;i.arc(e+r.topLeft,s+r.topLeft,r.topLeft,1.5*H,H,!0),i.lineTo(e,s+o-r.bottomLeft),i.arc(e+r.bottomLeft,s+o-r.bottomLeft,r.bottomLeft,H,V,!0),i.lineTo(e+n-r.bottomRight,s+o),i.arc(e+n-r.bottomRight,s+o-r.bottomRight,r.bottomRight,V,0,!0),i.lineTo(e+n,s+r.topRight),i.arc(e+n-r.topRight,s+r.topRight,r.topRight,0,-V,!0),i.lineTo(e+r.topLeft,s)}const po=/^(normal|(\d+(?:\.\d+)?)(px|em|%)?)$/,mo=/^(normal|italic|initial|inherit|unset|(oblique( -?[0-9]?[0-9]deg)?))$/;function bo(i,t){const e=(""+i).match(po);if(!e||e[1]==="normal")return t*1.2;switch(i=+e[2],e[3]){case"px":return i;case"%":i/=100;break}return t*i}const _o=i=>+i||0;function ii(i,t){const e={},s=C(t),n=s?Object.keys(t):t,o=C(i)?s?r=>D(i[r],i[t[r]]):r=>i[r]:()=>i;for(const r of n)e[r]=_o(o(r));return e}function Hs(i){return ii(i,{top:"y",right:"x",bottom:"y",left:"x"})}function Tt(i){return ii(i,["topLeft","topRight","bottomLeft","bottomRight"])}function J(i){const t=Hs(i);return t.width=t.left+t.right,t.height=t.top+t.bottom,t}function Y(i,t){i=i||{},t=t||z.font;let e=D(i.size,t.size);typeof e=="string"&&(e=parseInt(e,10));let s=D(i.style,t.style);s&&!(""+s).match(mo)&&(console.warn('Invalid font style specified: "'+s+'"'),s=void 0);const n={family:D(i.family,t.family),lineHeight:bo(D(i.lineHeight,t.lineHeight),e),size:e,style:s,weight:D(i.weight,t.weight),string:""};return n.string=ho(n),n}function re(i,t,e,s){let n,o,r;for(n=0,o=i.length;n<o;++n)if(r=i[n],r!==void 0&&r!==void 0)return r}function xo(i,t,e){const{min:s,max:n}=i,o=Ps(t,(n-s)/2),r=(a,l)=>e&&a===0?0:a+l;return{min:r(s,-Math.abs(o)),max:r(n,o)}}function Et(i,t){return Object.assign(Object.create(i),t)}function si(i,t=[""],e,s,n=()=>i[0]){const o=e||i;typeof s>"u"&&(s=Ns("_fallback",i));const r={[Symbol.toStringTag]:"Object",_cacheable:!0,_scopes:i,_rootScopes:o,_fallback:s,_getTarget:n,override:a=>si([a,...i],t,o,s)};return new Proxy(r,{deleteProperty(a,l){return delete a[l],delete a._keys,delete i[0][l],!0},get(a,l){return Vs(a,l,()=>Po(l,t,i,a))},getOwnPropertyDescriptor(a,l){return Reflect.getOwnPropertyDescriptor(a._scopes[0],l)},getPrototypeOf(){return Reflect.getPrototypeOf(i[0])},has(a,l){return Ci(a).includes(l)},ownKeys(a){return Ci(a)},set(a,l,c){const h=a._storage||(a._storage=n());return a[l]=h[l]=c,delete a._keys,!0}})}function Lt(i,t,e,s){const n={_cacheable:!1,_proxy:i,_context:t,_subProxy:e,_stack:new Set,_descriptors:Ws(i,s),setContext:o=>Lt(i,o,e,s),override:o=>Lt(i.override(o),t,e,s)};return new Proxy(n,{deleteProperty(o,r){return delete o[r],delete i[r],!0},get(o,r,a){return Vs(o,r,()=>vo(o,r,a))},getOwnPropertyDescriptor(o,r){return o._descriptors.allKeys?Reflect.has(i,r)?{enumerable:!0,configurable:!0}:void 0:Reflect.getOwnPropertyDescriptor(i,r)},getPrototypeOf(){return Reflect.getPrototypeOf(i)},has(o,r){return Reflect.has(i,r)},ownKeys(){return Reflect.ownKeys(i)},set(o,r,a){return i[r]=a,delete o[r],!0}})}function Ws(i,t={scriptable:!0,indexable:!0}){const{_scriptable:e=t.scriptable,_indexable:s=t.indexable,_allKeys:n=t.allKeys}=i;return{allKeys:n,scriptable:e,indexable:s,isScriptable:gt(e)?e:()=>e,isIndexable:gt(s)?s:()=>s}}const yo=(i,t)=>i?i+Ge(t):t,ni=(i,t)=>C(t)&&i!=="adapters"&&(Object.getPrototypeOf(t)===null||t.constructor===Object);function Vs(i,t,e){if(Object.prototype.hasOwnProperty.call(i,t)||t==="constructor")return i[t];const s=e();return i[t]=s,s}function vo(i,t,e){const{_proxy:s,_context:n,_subProxy:o,_descriptors:r}=i;let a=s[t];return gt(a)&&r.isScriptable(t)&&(a=Mo(t,a,i,e)),W(a)&&a.length&&(a=ko(t,a,i,r.isIndexable)),ni(t,a)&&(a=Lt(a,n,o&&o[t],r)),a}function Mo(i,t,e,s){const{_proxy:n,_context:o,_subProxy:r,_stack:a}=e;if(a.has(i))throw new Error("Recursion detected: "+Array.from(a).join("->")+"->"+i);a.add(i);let l=t(o,r||s);return a.delete(i),ni(i,l)&&(l=oi(n._scopes,n,i,l)),l}function ko(i,t,e,s){const{_proxy:n,_context:o,_subProxy:r,_descriptors:a}=e;if(typeof o.index<"u"&&s(i))return t[o.index%t.length];if(C(t[0])){const l=t,c=n._scopes.filter(h=>h!==l);t=[];for(const h of l){const d=oi(c,n,i,h);t.push(Lt(d,o,r&&r[i],a))}}return t}function js(i,t,e){return gt(i)?i(t,e):i}const wo=(i,t)=>i===!0?t:typeof i=="string"?wt(t,i):void 0;function So(i,t,e,s,n){for(const o of t){const r=wo(e,o);if(r){i.add(r);const a=js(r._fallback,e,n);if(typeof a<"u"&&a!==e&&a!==s)return a}else if(r===!1&&typeof s<"u"&&e!==s)return null}return!1}function oi(i,t,e,s){const n=t._rootScopes,o=js(t._fallback,e,s),r=[...i,...n],a=new Set;a.add(s);let l=Ai(a,r,e,o||e,s);return l===null||typeof o<"u"&&o!==e&&(l=Ai(a,r,o,l,s),l===null)?!1:si(Array.from(a),[""],n,o,()=>Oo(t,e,s))}function Ai(i,t,e,s,n){for(;e;)e=So(i,t,e,s,n);return e}function Oo(i,t,e){const s=i._getTarget();t in s||(s[t]={});const n=s[t];return W(n)&&C(e)?e:n||{}}function Po(i,t,e,s){let n;for(const o of t)if(n=Ns(yo(o,i),e),typeof n<"u")return ni(i,n)?oi(e,s,i,n):n}function Ns(i,t){for(const e of t){if(!e)continue;const s=e[i];if(typeof s<"u")return s}}function Ci(i){let t=i._keys;return t||(t=i._keys=Ao(i._scopes)),t}function Ao(i){const t=new Set;for(const e of i)for(const s of Object.keys(e).filter(n=>!n.startsWith("_")))t.add(s);return Array.from(t)}function ri(){return typeof window<"u"&&typeof document<"u"}function ai(i){let t=i.parentNode;return t&&t.toString()==="[object ShadowRoot]"&&(t=t.host),t}function we(i,t,e){let s;return typeof i=="string"?(s=parseInt(i,10),i.indexOf("%")!==-1&&(s=s/100*t.parentNode[e])):s=i,s}const Pe=i=>i.ownerDocument.defaultView.getComputedStyle(i,null);function Co(i,t){return Pe(i).getPropertyValue(t)}const Do=["top","right","bottom","left"];function kt(i,t,e){const s={};e=e?"-"+e:"";for(let n=0;n<4;n++){const o=Do[n];s[o]=parseFloat(i[t+"-"+o+e])||0}return s.width=s.left+s.right,s.height=s.top+s.bottom,s}const To=(i,t,e)=>(i>0||t>0)&&(!e||!e.shadowRoot);function Ro(i,t){const e=i.touches,s=e&&e.length?e[0]:i,{offsetX:n,offsetY:o}=s;let r=!1,a,l;if(To(n,o,i.target))a=n,l=o;else{const c=t.getBoundingClientRect();a=s.clientX-c.left,l=s.clientY-c.top,r=!0}return{x:a,y:l,box:r}}function vt(i,t){if("native"in i)return i;const{canvas:e,currentDevicePixelRatio:s}=t,n=Pe(e),o=n.boxSizing==="border-box",r=kt(n,"padding"),a=kt(n,"border","width"),{x:l,y:c,box:h}=Ro(i,e),d=r.left+(h&&a.left),f=r.top+(h&&a.top);let{width:u,height:g}=t;return o&&(u-=r.width+a.width,g-=r.height+a.height),{x:Math.round((l-d)/u*e.width/s),y:Math.round((c-f)/g*e.height/s)}}function Lo(i,t,e){let s,n;if(t===void 0||e===void 0){const o=i&&ai(i);if(!o)t=i.clientWidth,e=i.clientHeight;else{const r=o.getBoundingClientRect(),a=Pe(o),l=kt(a,"border","width"),c=kt(a,"padding");t=r.width-c.width-l.width,e=r.height-c.height-l.height,s=we(a.maxWidth,o,"clientWidth"),n=we(a.maxHeight,o,"clientHeight")}}return{width:t,height:e,maxWidth:s||ye,maxHeight:n||ye}}const ae=i=>Math.round(i*10)/10;function Eo(i,t,e,s){const n=Pe(i),o=kt(n,"margin"),r=we(n.maxWidth,i,"clientWidth")||ye,a=we(n.maxHeight,i,"clientHeight")||ye,l=Lo(i,t,e);let{width:c,height:h}=l;if(n.boxSizing==="content-box"){const f=kt(n,"border","width"),u=kt(n,"padding");c-=u.width+f.width,h-=u.height+f.height}return c=Math.max(0,c-o.width),h=Math.max(0,s?c/s:h-o.height),c=ae(Math.min(c,r,l.maxWidth)),h=ae(Math.min(h,a,l.maxHeight)),c&&!h&&(h=ae(c/2)),(t!==void 0||e!==void 0)&&s&&l.height&&h>l.height&&(h=l.height,c=ae(Math.floor(h*s))),{width:c,height:h}}function Di(i,t,e){const s=t||1,n=Math.floor(i.height*s),o=Math.floor(i.width*s);i.height=Math.floor(i.height),i.width=Math.floor(i.width);const r=i.canvas;return r.style&&(e||!r.style.height&&!r.style.width)&&(r.style.height=`${i.height}px`,r.style.width=`${i.width}px`),i.currentDevicePixelRatio!==s||r.height!==n||r.width!==o?(i.currentDevicePixelRatio=s,r.height=n,r.width=o,i.ctx.setTransform(s,0,0,s,0,0),!0):!1}const Fo=function(){let i=!1;try{const t={get passive(){return i=!0,!1}};ri()&&(window.addEventListener("test",null,t),window.removeEventListener("test",null,t))}catch{}return i}();function Ti(i,t){const e=Co(i,t),s=e&&e.match(/^(\d+)(\.\d+)?px$/);return s?+s[1]:void 0}const Io=function(i,t){return{x(e){return i+i+t-e},setWidth(e){t=e},textAlign(e){return e==="center"?e:e==="right"?"left":"right"},xPlus(e,s){return e-s},leftForLtr(e,s){return e-s}}},zo=function(){return{x(i){return i},setWidth(i){},textAlign(i){return i},xPlus(i,t){return i+t},leftForLtr(i,t){return i}}};function Rt(i,t,e){return i?Io(t,e):zo()}function $s(i,t){let e,s;(t==="ltr"||t==="rtl")&&(e=i.canvas.style,s=[e.getPropertyValue("direction"),e.getPropertyPriority("direction")],e.setProperty("direction",t,"important"),i.prevTextDirection=s)}function Ys(i,t){t!==void 0&&(delete i.prevTextDirection,i.canvas.style.setProperty("direction",t[0],t[1]))}function le(i,t,e){return i.options.clip?i[e]:t[e]}function Bo(i,t){const{xScale:e,yScale:s}=i;return e&&s?{left:le(e,t,"left"),right:le(e,t,"right"),top:le(s,t,"top"),bottom:le(s,t,"bottom")}:t}function Ho(i,t){const e=t._clip;if(e.disabled)return!1;const s=Bo(t,i.chartArea);return{left:e.left===!1?0:s.left-(e.left===!0?0:e.left),right:e.right===!1?i.width:s.right+(e.right===!0?0:e.right),top:e.top===!1?0:s.top-(e.top===!0?0:e.top),bottom:e.bottom===!1?i.height:s.bottom+(e.bottom===!0?0:e.bottom)}}/*!
 * Chart.js v4.4.9
 * https://www.chartjs.org
 * (c) 2025 Chart.js Contributors
 * Released under the MIT License
 */class Wo{constructor(){this._request=null,this._charts=new Map,this._running=!1,this._lastDate=void 0}_notify(t,e,s,n){const o=e.listeners[n],r=e.duration;o.forEach(a=>a({chart:t,initial:e.initial,numSteps:r,currentStep:Math.min(s-e.start,r)}))}_refresh(){this._request||(this._running=!0,this._request=Ls.call(window,()=>{this._update(),this._request=null,this._running&&this._refresh()}))}_update(t=Date.now()){let e=0;this._charts.forEach((s,n)=>{if(!s.running||!s.items.length)return;const o=s.items;let r=o.length-1,a=!1,l;for(;r>=0;--r)l=o[r],l._active?(l._total>s.duration&&(s.duration=l._total),l.tick(t),a=!0):(o[r]=o[o.length-1],o.pop());a&&(n.draw(),this._notify(n,s,t,"progress")),o.length||(s.running=!1,this._notify(n,s,t,"complete"),s.initial=!1),e+=o.length}),this._lastDate=t,e===0&&(this._running=!1)}_getAnims(t){const e=this._charts;let s=e.get(t);return s||(s={running:!1,initial:!0,items:[],listeners:{complete:[],progress:[]}},e.set(t,s)),s}listen(t,e,s){this._getAnims(t).listeners[e].push(s)}add(t,e){!e||!e.length||this._getAnims(t).items.push(...e)}has(t){return this._getAnims(t).items.length>0}start(t){const e=this._charts.get(t);e&&(e.running=!0,e.start=Date.now(),e.duration=e.items.reduce((s,n)=>Math.max(s,n._duration),0),this._refresh())}running(t){if(!this._running)return!1;const e=this._charts.get(t);return!(!e||!e.running||!e.items.length)}stop(t){const e=this._charts.get(t);if(!e||!e.items.length)return;const s=e.items;let n=s.length-1;for(;n>=0;--n)s[n].cancel();e.items=[],this._notify(t,e,Date.now(),"complete")}remove(t){return this._charts.delete(t)}}var rt=new Wo;const Ri="transparent",Vo={boolean(i,t,e){return e>.5?t:i},color(i,t,e){const s=ki(i||Ri),n=s.valid&&ki(t||Ri);return n&&n.valid?n.mix(s,e).hexString():t},number(i,t,e){return i+(t-i)*e}};class jo{constructor(t,e,s,n){const o=e[s];n=re([t.to,n,o,t.from]);const r=re([t.from,o,n]);this._active=!0,this._fn=t.fn||Vo[t.type||typeof r],this._easing=Ut[t.easing]||Ut.linear,this._start=Math.floor(Date.now()+(t.delay||0)),this._duration=this._total=Math.floor(t.duration),this._loop=!!t.loop,this._target=e,this._prop=s,this._from=r,this._to=n,this._promises=void 0}active(){return this._active}update(t,e,s){if(this._active){this._notify(!1);const n=this._target[this._prop],o=s-this._start,r=this._duration-o;this._start=s,this._duration=Math.floor(Math.max(r,t.duration)),this._total+=o,this._loop=!!t.loop,this._to=re([t.to,e,n,t.from]),this._from=re([t.from,n,e])}}cancel(){this._active&&(this.tick(Date.now()),this._active=!1,this._notify(!1))}tick(t){const e=t-this._start,s=this._duration,n=this._prop,o=this._from,r=this._loop,a=this._to;let l;if(this._active=o!==a&&(r||e<s),!this._active){this._target[n]=a,this._notify(!0);return}if(e<0){this._target[n]=o;return}l=e/s%2,l=r&&l>1?2-l:l,l=this._easing(Math.min(1,Math.max(0,l))),this._target[n]=this._fn(o,a,l)}wait(){const t=this._promises||(this._promises=[]);return new Promise((e,s)=>{t.push({res:e,rej:s})})}_notify(t){const e=t?"res":"rej",s=this._promises||[];for(let n=0;n<s.length;n++)s[n][e]()}}class Xs{constructor(t,e){this._chart=t,this._properties=new Map,this.configure(e)}configure(t){if(!C(t))return;const e=Object.keys(z.animation),s=this._properties;Object.getOwnPropertyNames(t).forEach(n=>{const o=t[n];if(!C(o))return;const r={};for(const a of e)r[a]=o[a];(W(o.properties)&&o.properties||[n]).forEach(a=>{(a===n||!s.has(a))&&s.set(a,r)})})}_animateOptions(t,e){const s=e.options,n=$o(t,s);if(!n)return[];const o=this._createAnimations(n,s);return s.$shared&&No(t.options.$animations,s).then(()=>{t.options=s},()=>{}),o}_createAnimations(t,e){const s=this._properties,n=[],o=t.$animations||(t.$animations={}),r=Object.keys(e),a=Date.now();let l;for(l=r.length-1;l>=0;--l){const c=r[l];if(c.charAt(0)==="$")continue;if(c==="options"){n.push(...this._animateOptions(t,e));continue}const h=e[c];let d=o[c];const f=s.get(c);if(d)if(f&&d.active()){d.update(f,h,a);continue}else d.cancel();if(!f||!f.duration){t[c]=h;continue}o[c]=d=new jo(f,t,c,h),n.push(d)}return n}update(t,e){if(this._properties.size===0){Object.assign(t,e);return}const s=this._createAnimations(t,e);if(s.length)return rt.add(this._chart,s),!0}}function No(i,t){const e=[],s=Object.keys(t);for(let n=0;n<s.length;n++){const o=i[s[n]];o&&o.active()&&e.push(o.wait())}return Promise.all(e)}function $o(i,t){if(!t)return;let e=i.options;if(!e){i.options=t;return}return e.$shared&&(i.options=e=Object.assign({},e,{$shared:!1,$animations:{}})),e}function Li(i,t){const e=i&&i.options||{},s=e.reverse,n=e.min===void 0?t:0,o=e.max===void 0?t:0;return{start:s?o:n,end:s?n:o}}function Yo(i,t,e){if(e===!1)return!1;const s=Li(i,e),n=Li(t,e);return{top:n.end,right:s.end,bottom:n.start,left:s.start}}function Xo(i){let t,e,s,n;return C(i)?(t=i.top,e=i.right,s=i.bottom,n=i.left):t=e=s=n=i,{top:t,right:e,bottom:s,left:n,disabled:i===!1}}function Us(i,t){const e=[],s=i._getSortedDatasetMetas(t);let n,o;for(n=0,o=s.length;n<o;++n)e.push(s[n].index);return e}function Ei(i,t,e,s={}){const n=i.keys,o=s.mode==="single";let r,a,l,c;if(t===null)return;let h=!1;for(r=0,a=n.length;r<a;++r){if(l=+n[r],l===e){if(h=!0,s.all)continue;break}c=i.values[l],Q(c)&&(o||t===0||ut(t)===ut(c))&&(t+=c)}return!h&&!s.all?0:t}function Uo(i,t){const{iScale:e,vScale:s}=t,n=e.axis==="x"?"x":"y",o=s.axis==="x"?"x":"y",r=Object.keys(i),a=new Array(r.length);let l,c,h;for(l=0,c=r.length;l<c;++l)h=r[l],a[l]={[n]:h,[o]:i[h]};return a}function Le(i,t){const e=i&&i.options.stacked;return e||e===void 0&&t.stack!==void 0}function Ko(i,t,e){return`${i.id}.${t.id}.${e.stack||e.type}`}function qo(i){const{min:t,max:e,minDefined:s,maxDefined:n}=i.getUserBounds();return{min:s?t:Number.NEGATIVE_INFINITY,max:n?e:Number.POSITIVE_INFINITY}}function Go(i,t,e){const s=i[t]||(i[t]={});return s[e]||(s[e]={})}function Fi(i,t,e,s){for(const n of t.getMatchingVisibleMetas(s).reverse()){const o=i[n.index];if(e&&o>0||!e&&o<0)return n.index}return null}function Ii(i,t){const{chart:e,_cachedMeta:s}=i,n=e._stacks||(e._stacks={}),{iScale:o,vScale:r,index:a}=s,l=o.axis,c=r.axis,h=Ko(o,r,s),d=t.length;let f;for(let u=0;u<d;++u){const g=t[u],{[l]:p,[c]:m}=g,b=g._stacks||(g._stacks={});f=b[c]=Go(n,h,p),f[a]=m,f._top=Fi(f,r,!0,s.type),f._bottom=Fi(f,r,!1,s.type);const _=f._visualValues||(f._visualValues={});_[a]=m}}function Ee(i,t){const e=i.scales;return Object.keys(e).filter(s=>e[s].axis===t).shift()}function Zo(i,t){return Et(i,{active:!1,dataset:void 0,datasetIndex:t,index:t,mode:"default",type:"dataset"})}function Qo(i,t,e){return Et(i,{active:!1,dataIndex:t,parsed:void 0,raw:void 0,element:e,index:t,mode:"default",type:"data"})}function Bt(i,t){const e=i.controller.index,s=i.vScale&&i.vScale.axis;if(s){t=t||i._parsed;for(const n of t){const o=n._stacks;if(!o||o[s]===void 0||o[s][e]===void 0)return;delete o[s][e],o[s]._visualValues!==void 0&&o[s]._visualValues[e]!==void 0&&delete o[s]._visualValues[e]}}}const Fe=i=>i==="reset"||i==="none",zi=(i,t)=>t?i:Object.assign({},i),Jo=(i,t,e)=>i&&!t.hidden&&t._stacked&&{keys:Us(e,!0),values:null};class li{static defaults={};static datasetElementType=null;static dataElementType=null;constructor(t,e){this.chart=t,this._ctx=t.ctx,this.index=e,this._cachedDataOpts={},this._cachedMeta=this.getMeta(),this._type=this._cachedMeta.type,this.options=void 0,this._parsing=!1,this._data=void 0,this._objectData=void 0,this._sharedOptions=void 0,this._drawStart=void 0,this._drawCount=void 0,this.enableOptionSharing=!1,this.supportsDecimation=!1,this.$context=void 0,this._syncList=[],this.datasetElementType=new.target.datasetElementType,this.dataElementType=new.target.dataElementType,this.initialize()}initialize(){const t=this._cachedMeta;this.configure(),this.linkScales(),t._stacked=Le(t.vScale,t),this.addElements(),this.options.fill&&!this.chart.isPluginEnabled("filler")&&console.warn("Tried to use the 'fill' option without the 'Filler' plugin enabled. Please import and register the 'Filler' plugin and make sure it is not disabled in the options")}updateIndex(t){this.index!==t&&Bt(this._cachedMeta),this.index=t}linkScales(){const t=this.chart,e=this._cachedMeta,s=this.getDataset(),n=(d,f,u,g)=>d==="x"?f:d==="r"?g:u,o=e.xAxisID=D(s.xAxisID,Ee(t,"x")),r=e.yAxisID=D(s.yAxisID,Ee(t,"y")),a=e.rAxisID=D(s.rAxisID,Ee(t,"r")),l=e.indexAxis,c=e.iAxisID=n(l,o,r,a),h=e.vAxisID=n(l,r,o,a);e.xScale=this.getScaleForId(o),e.yScale=this.getScaleForId(r),e.rScale=this.getScaleForId(a),e.iScale=this.getScaleForId(c),e.vScale=this.getScaleForId(h)}getDataset(){return this.chart.data.datasets[this.index]}getMeta(){return this.chart.getDatasetMeta(this.index)}getScaleForId(t){return this.chart.scales[t]}_getOtherScale(t){const e=this._cachedMeta;return t===e.iScale?e.vScale:e.iScale}reset(){this._update("reset")}_destroy(){const t=this._cachedMeta;this._data&&yi(this._data,this),t._stacked&&Bt(t)}_dataCheck(){const t=this.getDataset(),e=t.data||(t.data=[]),s=this._data;if(C(e)){const n=this._cachedMeta;this._data=Uo(e,n)}else if(s!==e){if(s){yi(s,this);const n=this._cachedMeta;Bt(n),n._parsed=[]}e&&Object.isExtensible(e)&&Qn(e,this),this._syncList=[],this._data=e}}addElements(){const t=this._cachedMeta;this._dataCheck(),this.datasetElementType&&(t.dataset=new this.datasetElementType)}buildOrUpdateElements(t){const e=this._cachedMeta,s=this.getDataset();let n=!1;this._dataCheck();const o=e._stacked;e._stacked=Le(e.vScale,e),e.stack!==s.stack&&(n=!0,Bt(e),e.stack=s.stack),this._resyncElements(t),(n||o!==e._stacked)&&(Ii(this,e._parsed),e._stacked=Le(e.vScale,e))}configure(){const t=this.chart.config,e=t.datasetScopeKeys(this._type),s=t.getOptionScopes(this.getDataset(),e,!0);this.options=t.createResolver(s,this.getContext()),this._parsing=this.options.parsing,this._cachedDataOpts={}}parse(t,e){const{_cachedMeta:s,_data:n}=this,{iScale:o,_stacked:r}=s,a=o.axis;let l=t===0&&e===n.length?!0:s._sorted,c=t>0&&s._parsed[t-1],h,d,f;if(this._parsing===!1)s._parsed=n,s._sorted=!0,f=n;else{W(n[t])?f=this.parseArrayData(s,n,t,e):C(n[t])?f=this.parseObjectData(s,n,t,e):f=this.parsePrimitiveData(s,n,t,e);const u=()=>d[a]===null||c&&d[a]<c[a];for(h=0;h<e;++h)s._parsed[h+t]=d=f[h],l&&(u()&&(l=!1),c=d);s._sorted=l}r&&Ii(this,f)}parsePrimitiveData(t,e,s,n){const{iScale:o,vScale:r}=t,a=o.axis,l=r.axis,c=o.getLabels(),h=o===r,d=new Array(n);let f,u,g;for(f=0,u=n;f<u;++f)g=f+s,d[f]={[a]:h||o.parse(c[g],g),[l]:r.parse(e[g],g)};return d}parseArrayData(t,e,s,n){const{xScale:o,yScale:r}=t,a=new Array(n);let l,c,h,d;for(l=0,c=n;l<c;++l)h=l+s,d=e[h],a[l]={x:o.parse(d[0],h),y:r.parse(d[1],h)};return a}parseObjectData(t,e,s,n){const{xScale:o,yScale:r}=t,{xAxisKey:a="x",yAxisKey:l="y"}=this._parsing,c=new Array(n);let h,d,f,u;for(h=0,d=n;h<d;++h)f=h+s,u=e[f],c[h]={x:o.parse(wt(u,a),f),y:r.parse(wt(u,l),f)};return c}getParsed(t){return this._cachedMeta._parsed[t]}getDataElement(t){return this._cachedMeta.data[t]}applyStack(t,e,s){const n=this.chart,o=this._cachedMeta,r=e[t.axis],a={keys:Us(n,!0),values:e._stacks[t.axis]._visualValues};return Ei(a,r,o.index,{mode:s})}updateRangeFromParsed(t,e,s,n){const o=s[e.axis];let r=o===null?NaN:o;const a=n&&s._stacks[e.axis];n&&a&&(n.values=a,r=Ei(n,o,this._cachedMeta.index)),t.min=Math.min(t.min,r),t.max=Math.max(t.max,r)}getMinMax(t,e){const s=this._cachedMeta,n=s._parsed,o=s._sorted&&t===s.iScale,r=n.length,a=this._getOtherScale(t),l=Jo(e,s,this.chart),c={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY},{min:h,max:d}=qo(a);let f,u;function g(){u=n[f];const p=u[a.axis];return!Q(u[t.axis])||h>p||d<p}for(f=0;f<r&&!(!g()&&(this.updateRangeFromParsed(c,t,u,l),o));++f);if(o){for(f=r-1;f>=0;--f)if(!g()){this.updateRangeFromParsed(c,t,u,l);break}}return c}getAllParsedValues(t){const e=this._cachedMeta._parsed,s=[];let n,o,r;for(n=0,o=e.length;n<o;++n)r=e[n][t.axis],Q(r)&&s.push(r);return s}getMaxOverflow(){return!1}getLabelAndValue(t){const e=this._cachedMeta,s=e.iScale,n=e.vScale,o=this.getParsed(t);return{label:s?""+s.getLabelForValue(o[s.axis]):"",value:n?""+n.getLabelForValue(o[n.axis]):""}}_update(t){const e=this._cachedMeta;this.update(t||"default"),e._clip=Xo(D(this.options.clip,Yo(e.xScale,e.yScale,this.getMaxOverflow())))}update(t){}draw(){const t=this._ctx,e=this.chart,s=this._cachedMeta,n=s.data||[],o=e.chartArea,r=[],a=this._drawStart||0,l=this._drawCount||n.length-a,c=this.options.drawActiveElementsOnTop;let h;for(s.dataset&&s.dataset.draw(t,o,a,l),h=a;h<a+l;++h){const d=n[h];d.hidden||(d.active&&c?r.push(d):d.draw(t,o))}for(h=0;h<r.length;++h)r[h].draw(t,o)}getStyle(t,e){const s=e?"active":"default";return t===void 0&&this._cachedMeta.dataset?this.resolveDatasetElementOptions(s):this.resolveDataElementOptions(t||0,s)}getContext(t,e,s){const n=this.getDataset();let o;if(t>=0&&t<this._cachedMeta.data.length){const r=this._cachedMeta.data[t];o=r.$context||(r.$context=Qo(this.getContext(),t,r)),o.parsed=this.getParsed(t),o.raw=n.data[t],o.index=o.dataIndex=t}else o=this.$context||(this.$context=Zo(this.chart.getContext(),this.index)),o.dataset=n,o.index=o.datasetIndex=this.index;return o.active=!!e,o.mode=s,o}resolveDatasetElementOptions(t){return this._resolveElementOptions(this.datasetElementType.id,t)}resolveDataElementOptions(t,e){return this._resolveElementOptions(this.dataElementType.id,e,t)}_resolveElementOptions(t,e="default",s){const n=e==="active",o=this._cachedDataOpts,r=t+"-"+e,a=o[r],l=this.enableOptionSharing&&Zt(s);if(a)return zi(a,l);const c=this.chart.config,h=c.datasetElementScopeKeys(this._type,t),d=n?[`${t}Hover`,"hover",t,""]:[t,""],f=c.getOptionScopes(this.getDataset(),h),u=Object.keys(z.elements[t]),g=()=>this.getContext(s,n,e),p=c.resolveNamedOptions(f,u,g,d);return p.$shared&&(p.$shared=l,o[r]=Object.freeze(zi(p,l))),p}_resolveAnimations(t,e,s){const n=this.chart,o=this._cachedDataOpts,r=`animation-${e}`,a=o[r];if(a)return a;let l;if(n.options.animation!==!1){const h=this.chart.config,d=h.datasetAnimationScopeKeys(this._type,e),f=h.getOptionScopes(this.getDataset(),d);l=h.createResolver(f,this.getContext(t,s,e))}const c=new Xs(n,l&&l.animations);return l&&l._cacheable&&(o[r]=Object.freeze(c)),c}getSharedOptions(t){if(t.$shared)return this._sharedOptions||(this._sharedOptions=Object.assign({},t))}includeOptions(t,e){return!e||Fe(t)||this.chart._animationsDisabled}_getSharedOptions(t,e){const s=this.resolveDataElementOptions(t,e),n=this._sharedOptions,o=this.getSharedOptions(s),r=this.includeOptions(e,o)||o!==n;return this.updateSharedOptions(o,e,s),{sharedOptions:o,includeOptions:r}}updateElement(t,e,s,n){Fe(n)?Object.assign(t,s):this._resolveAnimations(e,n).update(t,s)}updateSharedOptions(t,e,s){t&&!Fe(e)&&this._resolveAnimations(void 0,e).update(t,s)}_setStyle(t,e,s,n){t.active=n;const o=this.getStyle(e,n);this._resolveAnimations(e,s,n).update(t,{options:!n&&this.getSharedOptions(o)||o})}removeHoverStyle(t,e,s){this._setStyle(t,s,"active",!1)}setHoverStyle(t,e,s){this._setStyle(t,s,"active",!0)}_removeDatasetHoverStyle(){const t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",!1)}_setDatasetHoverStyle(){const t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",!0)}_resyncElements(t){const e=this._data,s=this._cachedMeta.data;for(const[a,l,c]of this._syncList)this[a](l,c);this._syncList=[];const n=s.length,o=e.length,r=Math.min(o,n);r&&this.parse(0,r),o>n?this._insertElements(n,o-n,t):o<n&&this._removeElements(o,n-o)}_insertElements(t,e,s=!0){const n=this._cachedMeta,o=n.data,r=t+e;let a;const l=c=>{for(c.length+=e,a=c.length-1;a>=r;a--)c[a]=c[a-e]};for(l(o),a=t;a<r;++a)o[a]=new this.dataElementType;this._parsing&&l(n._parsed),this.parse(t,e),s&&this.updateElements(o,t,e,"reset")}updateElements(t,e,s,n){}_removeElements(t,e){const s=this._cachedMeta;if(this._parsing){const n=s._parsed.splice(t,e);s._stacked&&Bt(s,n)}s.data.splice(t,e)}_sync(t){if(this._parsing)this._syncList.push(t);else{const[e,s,n]=t;this[e](s,n)}this.chart._dataChanges.push([this.index,...t])}_onDataPush(){const t=arguments.length;this._sync(["_insertElements",this.getDataset().data.length-t,t])}_onDataPop(){this._sync(["_removeElements",this._cachedMeta.data.length-1,1])}_onDataShift(){this._sync(["_removeElements",0,1])}_onDataSplice(t,e){e&&this._sync(["_removeElements",t,e]);const s=arguments.length-2;s&&this._sync(["_insertElements",t,s])}_onDataUnshift(){this._sync(["_insertElements",0,arguments.length])}}function tr(i,t){if(!i._cache.$bar){const e=i.getMatchingVisibleMetas(t);let s=[];for(let n=0,o=e.length;n<o;n++)s=s.concat(e[n].controller.getAllParsedValues(i));i._cache.$bar=Rs(s.sort((n,o)=>n-o))}return i._cache.$bar}function er(i){const t=i.iScale,e=tr(t,i.type);let s=t._length,n,o,r,a;const l=()=>{r===32767||r===-32768||(Zt(a)&&(s=Math.min(s,Math.abs(r-a)||s)),a=r)};for(n=0,o=e.length;n<o;++n)r=t.getPixelForValue(e[n]),l();for(a=void 0,n=0,o=t.ticks.length;n<o;++n)r=t.getPixelForTick(n),l();return s}function ir(i,t,e,s){const n=e.barThickness;let o,r;return L(n)?(o=t.min*e.categoryPercentage,r=e.barPercentage):(o=n*s,r=1),{chunk:o/s,ratio:r,start:t.pixels[i]-o/2}}function sr(i,t,e,s){const n=t.pixels,o=n[i];let r=i>0?n[i-1]:null,a=i<n.length-1?n[i+1]:null;const l=e.categoryPercentage;r===null&&(r=o-(a===null?t.end-t.start:a-o)),a===null&&(a=o+o-r);const c=o-(o-Math.min(r,a))/2*l;return{chunk:Math.abs(a-r)/2*l/s,ratio:e.barPercentage,start:c}}function nr(i,t,e,s){const n=e.parse(i[0],s),o=e.parse(i[1],s),r=Math.min(n,o),a=Math.max(n,o);let l=r,c=a;Math.abs(r)>Math.abs(a)&&(l=a,c=r),t[e.axis]=c,t._custom={barStart:l,barEnd:c,start:n,end:o,min:r,max:a}}function Ks(i,t,e,s){return W(i)?nr(i,t,e,s):t[e.axis]=e.parse(i,s),t}function Bi(i,t,e,s){const n=i.iScale,o=i.vScale,r=n.getLabels(),a=n===o,l=[];let c,h,d,f;for(c=e,h=e+s;c<h;++c)f=t[c],d={},d[n.axis]=a||n.parse(r[c],c),l.push(Ks(f,d,o,c));return l}function Ie(i){return i&&i.barStart!==void 0&&i.barEnd!==void 0}function or(i,t,e){return i!==0?ut(i):(t.isHorizontal()?1:-1)*(t.min>=e?1:-1)}function rr(i){let t,e,s,n,o;return i.horizontal?(t=i.base>i.x,e="left",s="right"):(t=i.base<i.y,e="bottom",s="top"),t?(n="end",o="start"):(n="start",o="end"),{start:e,end:s,reverse:t,top:n,bottom:o}}function ar(i,t,e,s){let n=t.borderSkipped;const o={};if(!n){i.borderSkipped=o;return}if(n===!0){i.borderSkipped={top:!0,right:!0,bottom:!0,left:!0};return}const{start:r,end:a,reverse:l,top:c,bottom:h}=rr(i);n==="middle"&&e&&(i.enableBorderRadius=!0,(e._top||0)===s?n=c:(e._bottom||0)===s?n=h:(o[Hi(h,r,a,l)]=!0,n=c)),o[Hi(n,r,a,l)]=!0,i.borderSkipped=o}function Hi(i,t,e,s){return s?(i=lr(i,t,e),i=Wi(i,e,t)):i=Wi(i,t,e),i}function lr(i,t,e){return i===t?e:i===e?t:i}function Wi(i,t,e){return i==="start"?t:i==="end"?e:i}function cr(i,{inflateAmount:t},e){i.inflateAmount=t==="auto"?e===1?.33:0:t}class hr extends li{static id="bar";static defaults={datasetElementType:!1,dataElementType:"bar",categoryPercentage:.8,barPercentage:.9,grouped:!0,animations:{numbers:{type:"number",properties:["x","y","base","width","height"]}}};static overrides={scales:{_index_:{type:"category",offset:!0,grid:{offset:!0}},_value_:{type:"linear",beginAtZero:!0}}};parsePrimitiveData(t,e,s,n){return Bi(t,e,s,n)}parseArrayData(t,e,s,n){return Bi(t,e,s,n)}parseObjectData(t,e,s,n){const{iScale:o,vScale:r}=t,{xAxisKey:a="x",yAxisKey:l="y"}=this._parsing,c=o.axis==="x"?a:l,h=r.axis==="x"?a:l,d=[];let f,u,g,p;for(f=s,u=s+n;f<u;++f)p=e[f],g={},g[o.axis]=o.parse(wt(p,c),f),d.push(Ks(wt(p,h),g,r,f));return d}updateRangeFromParsed(t,e,s,n){super.updateRangeFromParsed(t,e,s,n);const o=s._custom;o&&e===this._cachedMeta.vScale&&(t.min=Math.min(t.min,o.min),t.max=Math.max(t.max,o.max))}getMaxOverflow(){return 0}getLabelAndValue(t){const e=this._cachedMeta,{iScale:s,vScale:n}=e,o=this.getParsed(t),r=o._custom,a=Ie(r)?"["+r.start+", "+r.end+"]":""+n.getLabelForValue(o[n.axis]);return{label:""+s.getLabelForValue(o[s.axis]),value:a}}initialize(){this.enableOptionSharing=!0,super.initialize();const t=this._cachedMeta;t.stack=this.getDataset().stack}update(t){const e=this._cachedMeta;this.updateElements(e.data,0,e.data.length,t)}updateElements(t,e,s,n){const o=n==="reset",{index:r,_cachedMeta:{vScale:a}}=this,l=a.getBasePixel(),c=a.isHorizontal(),h=this._getRuler(),{sharedOptions:d,includeOptions:f}=this._getSharedOptions(e,n);for(let u=e;u<e+s;u++){const g=this.getParsed(u),p=o||L(g[a.axis])?{base:l,head:l}:this._calculateBarValuePixels(u),m=this._calculateBarIndexPixels(u,h),b=(g._stacks||{})[a.axis],_={horizontal:c,base:p.base,enableBorderRadius:!b||Ie(g._custom)||r===b._top||r===b._bottom,x:c?p.head:m.center,y:c?m.center:p.head,height:c?m.size:Math.abs(p.size),width:c?Math.abs(p.size):m.size};f&&(_.options=d||this.resolveDataElementOptions(u,t[u].active?"active":n));const y=_.options||t[u].options;ar(_,y,b,r),cr(_,y,h.ratio),this.updateElement(t[u],u,_,n)}}_getStacks(t,e){const{iScale:s}=this._cachedMeta,n=s.getMatchingVisibleMetas(this._type).filter(h=>h.controller.options.grouped),o=s.options.stacked,r=[],a=this._cachedMeta.controller.getParsed(e),l=a&&a[s.axis],c=h=>{const d=h._parsed.find(u=>u[s.axis]===l),f=d&&d[h.vScale.axis];if(L(f)||isNaN(f))return!0};for(const h of n)if(!(e!==void 0&&c(h))&&((o===!1||r.indexOf(h.stack)===-1||o===void 0&&h.stack===void 0)&&r.push(h.stack),h.index===t))break;return r.length||r.push(void 0),r}_getStackCount(t){return this._getStacks(void 0,t).length}_getStackIndex(t,e,s){const n=this._getStacks(t,s),o=e!==void 0?n.indexOf(e):-1;return o===-1?n.length-1:o}_getRuler(){const t=this.options,e=this._cachedMeta,s=e.iScale,n=[];let o,r;for(o=0,r=e.data.length;o<r;++o)n.push(s.getPixelForValue(this.getParsed(o)[s.axis],o));const a=t.barThickness;return{min:a||er(e),pixels:n,start:s._startPixel,end:s._endPixel,stackCount:this._getStackCount(),scale:s,grouped:t.grouped,ratio:a?1:t.categoryPercentage*t.barPercentage}}_calculateBarValuePixels(t){const{_cachedMeta:{vScale:e,_stacked:s,index:n},options:{base:o,minBarLength:r}}=this,a=o||0,l=this.getParsed(t),c=l._custom,h=Ie(c);let d=l[e.axis],f=0,u=s?this.applyStack(e,l,s):d,g,p;u!==d&&(f=u-d,u=d),h&&(d=c.barStart,u=c.barEnd-c.barStart,d!==0&&ut(d)!==ut(c.barEnd)&&(f=0),f+=d);const m=!L(o)&&!h?o:f;let b=e.getPixelForValue(m);if(this.chart.getDataVisibility(t)?g=e.getPixelForValue(f+u):g=b,p=g-b,Math.abs(p)<r){p=or(p,e,a)*r,d===a&&(b-=p/2);const _=e.getPixelForDecimal(0),y=e.getPixelForDecimal(1),v=Math.min(_,y),x=Math.max(_,y);b=Math.max(Math.min(b,x),v),g=b+p,s&&!h&&(l._stacks[e.axis]._visualValues[n]=e.getValueForPixel(g)-e.getValueForPixel(b))}if(b===e.getPixelForValue(a)){const _=ut(p)*e.getLineWidthForValue(a)/2;b+=_,p-=_}return{size:p,base:b,head:g,center:g+p/2}}_calculateBarIndexPixels(t,e){const s=e.scale,n=this.options,o=n.skipNull,r=D(n.maxBarThickness,1/0);let a,l;if(e.grouped){const c=o?this._getStackCount(t):e.stackCount,h=n.barThickness==="flex"?sr(t,e,n,c):ir(t,e,n,c),d=this._getStackIndex(this.index,this._cachedMeta.stack,o?t:void 0);a=h.start+h.chunk*d+h.chunk/2,l=Math.min(r,h.chunk*h.ratio)}else a=s.getPixelForValue(this.getParsed(t)[s.axis],t),l=Math.min(r,e.min*e.ratio);return{base:a-l/2,head:a+l/2,center:a,size:l}}draw(){const t=this._cachedMeta,e=t.vScale,s=t.data,n=s.length;let o=0;for(;o<n;++o)this.getParsed(o)[e.axis]!==null&&!s[o].hidden&&s[o].draw(this._ctx)}}function dr(i,t,e){let s=1,n=1,o=0,r=0;if(t<B){const a=i,l=a+t,c=Math.cos(a),h=Math.sin(a),d=Math.cos(l),f=Math.sin(l),u=(y,v,x)=>Me(y,a,l,!0)?1:Math.max(v,v*e,x,x*e),g=(y,v,x)=>Me(y,a,l,!0)?-1:Math.min(v,v*e,x,x*e),p=u(0,c,d),m=u(V,h,f),b=g(H,c,d),_=g(H+V,h,f);s=(p-b)/2,n=(m-_)/2,o=-(p+b)/2,r=-(m+_)/2}return{ratioX:s,ratioY:n,offsetX:o,offsetY:r}}class fr extends li{static id="doughnut";static defaults={datasetElementType:!1,dataElementType:"arc",animation:{animateRotate:!0,animateScale:!1},animations:{numbers:{type:"number",properties:["circumference","endAngle","innerRadius","outerRadius","startAngle","x","y","offset","borderWidth","spacing"]}},cutout:"50%",rotation:0,circumference:360,radius:"100%",spacing:0,indexAxis:"r"};static descriptors={_scriptable:t=>t!=="spacing",_indexable:t=>t!=="spacing"&&!t.startsWith("borderDash")&&!t.startsWith("hoverBorderDash")};static overrides={aspectRatio:1,plugins:{legend:{labels:{generateLabels(t){const e=t.data;if(e.labels.length&&e.datasets.length){const{labels:{pointStyle:s,color:n}}=t.legend.options;return e.labels.map((o,r)=>{const l=t.getDatasetMeta(0).controller.getStyle(r);return{text:o,fillStyle:l.backgroundColor,strokeStyle:l.borderColor,fontColor:n,lineWidth:l.borderWidth,pointStyle:s,hidden:!t.getDataVisibility(r),index:r}})}return[]}},onClick(t,e,s){s.chart.toggleDataVisibility(e.index),s.chart.update()}}}};constructor(t,e){super(t,e),this.enableOptionSharing=!0,this.innerRadius=void 0,this.outerRadius=void 0,this.offsetX=void 0,this.offsetY=void 0}linkScales(){}parse(t,e){const s=this.getDataset().data,n=this._cachedMeta;if(this._parsing===!1)n._parsed=s;else{let o=l=>+s[l];if(C(s[t])){const{key:l="value"}=this._parsing;o=c=>+wt(s[c],l)}let r,a;for(r=t,a=t+e;r<a;++r)n._parsed[r]=o(r)}}_getRotation(){return ct(this.options.rotation-90)}_getCircumference(){return ct(this.options.circumference)}_getRotationExtents(){let t=B,e=-B;for(let s=0;s<this.chart.data.datasets.length;++s)if(this.chart.isDatasetVisible(s)&&this.chart.getDatasetMeta(s).type===this._type){const n=this.chart.getDatasetMeta(s).controller,o=n._getRotation(),r=n._getCircumference();t=Math.min(t,o),e=Math.max(e,o+r)}return{rotation:t,circumference:e-t}}update(t){const e=this.chart,{chartArea:s}=e,n=this._cachedMeta,o=n.data,r=this.getMaxBorderWidth()+this.getMaxOffset(o)+this.options.spacing,a=Math.max((Math.min(s.width,s.height)-r)/2,0),l=Math.min(In(this.options.cutout,a),1),c=this._getRingWeight(this.index),{circumference:h,rotation:d}=this._getRotationExtents(),{ratioX:f,ratioY:u,offsetX:g,offsetY:p}=dr(d,h,l),m=(s.width-r)/f,b=(s.height-r)/u,_=Math.max(Math.min(m,b)/2,0),y=Ps(this.options.radius,_),v=Math.max(y*l,0),x=(y-v)/this._getVisibleDatasetWeightTotal();this.offsetX=g*y,this.offsetY=p*y,n.total=this.calculateTotal(),this.outerRadius=y-x*this._getRingWeightOffset(this.index),this.innerRadius=Math.max(this.outerRadius-x*c,0),this.updateElements(o,0,o.length,t)}_circumference(t,e){const s=this.options,n=this._cachedMeta,o=this._getCircumference();return e&&s.animation.animateRotate||!this.chart.getDataVisibility(t)||n._parsed[t]===null||n.data[t].hidden?0:this.calculateCircumference(n._parsed[t]*o/B)}updateElements(t,e,s,n){const o=n==="reset",r=this.chart,a=r.chartArea,c=r.options.animation,h=(a.left+a.right)/2,d=(a.top+a.bottom)/2,f=o&&c.animateScale,u=f?0:this.innerRadius,g=f?0:this.outerRadius,{sharedOptions:p,includeOptions:m}=this._getSharedOptions(e,n);let b=this._getRotation(),_;for(_=0;_<e;++_)b+=this._circumference(_,o);for(_=e;_<e+s;++_){const y=this._circumference(_,o),v=t[_],x={x:h+this.offsetX,y:d+this.offsetY,startAngle:b,endAngle:b+y,circumference:y,outerRadius:g,innerRadius:u};m&&(x.options=p||this.resolveDataElementOptions(_,v.active?"active":n)),b+=y,this.updateElement(v,_,x,n)}}calculateTotal(){const t=this._cachedMeta,e=t.data;let s=0,n;for(n=0;n<e.length;n++){const o=t._parsed[n];o!==null&&!isNaN(o)&&this.chart.getDataVisibility(n)&&!e[n].hidden&&(s+=Math.abs(o))}return s}calculateCircumference(t){const e=this._cachedMeta.total;return e>0&&!isNaN(t)?B*(Math.abs(t)/e):0}getLabelAndValue(t){const e=this._cachedMeta,s=this.chart,n=s.data.labels||[],o=Je(e._parsed[t],s.options.locale);return{label:n[t]||"",value:o}}getMaxBorderWidth(t){let e=0;const s=this.chart;let n,o,r,a,l;if(!t){for(n=0,o=s.data.datasets.length;n<o;++n)if(s.isDatasetVisible(n)){r=s.getDatasetMeta(n),t=r.data,a=r.controller;break}}if(!t)return 0;for(n=0,o=t.length;n<o;++n)l=a.resolveDataElementOptions(n),l.borderAlign!=="inner"&&(e=Math.max(e,l.borderWidth||0,l.hoverBorderWidth||0));return e}getMaxOffset(t){let e=0;for(let s=0,n=t.length;s<n;++s){const o=this.resolveDataElementOptions(s);e=Math.max(e,o.offset||0,o.hoverOffset||0)}return e}_getRingWeightOffset(t){let e=0;for(let s=0;s<t;++s)this.chart.isDatasetVisible(s)&&(e+=this._getRingWeight(s));return e}_getRingWeight(t){return Math.max(D(this.chart.data.datasets[t].weight,1),0)}_getVisibleDatasetWeightTotal(){return this._getRingWeightOffset(this.chart.data.datasets.length)||1}}function yt(){throw new Error("This method is not implemented: Check that a complete date adapter is provided.")}class ci{static override(t){Object.assign(ci.prototype,t)}options;constructor(t){this.options=t||{}}init(){}formats(){return yt()}parse(){return yt()}format(){return yt()}add(){return yt()}diff(){return yt()}startOf(){return yt()}endOf(){return yt()}}var ur={_date:ci};function gr(i,t,e,s){const{controller:n,data:o,_sorted:r}=i,a=n._cachedMeta.iScale,l=i.dataset&&i.dataset.options?i.dataset.options.spanGaps:null;if(a&&t===a.axis&&t!=="r"&&r&&o.length){const c=a._reversePixels?Gn:Ne;if(s){if(n._sharedOptions){const h=o[0],d=typeof h.getRange=="function"&&h.getRange(t);if(d){const f=c(o,t,e-d),u=c(o,t,e+d);return{lo:f.lo,hi:u.hi}}}}else{const h=c(o,t,e);if(l){const{vScale:d}=n._cachedMeta,{_parsed:f}=i,u=f.slice(0,h.lo+1).reverse().findIndex(p=>!L(p[d.axis]));h.lo-=Math.max(0,u);const g=f.slice(h.hi).findIndex(p=>!L(p[d.axis]));h.hi+=Math.max(0,g)}return h}}return{lo:0,hi:o.length-1}}function Ae(i,t,e,s,n){const o=i.getSortedVisibleDatasetMetas(),r=e[t];for(let a=0,l=o.length;a<l;++a){const{index:c,data:h}=o[a],{lo:d,hi:f}=gr(o[a],t,r,n);for(let u=d;u<=f;++u){const g=h[u];g.skip||s(g,c,u)}}}function pr(i){const t=i.indexOf("x")!==-1,e=i.indexOf("y")!==-1;return function(s,n){const o=t?Math.abs(s.x-n.x):0,r=e?Math.abs(s.y-n.y):0;return Math.sqrt(Math.pow(o,2)+Math.pow(r,2))}}function ze(i,t,e,s,n){const o=[];return!n&&!i.isPointInArea(t)||Ae(i,e,t,function(a,l,c){!n&&!Bs(a,i.chartArea,0)||a.inRange(t.x,t.y,s)&&o.push({element:a,datasetIndex:l,index:c})},!0),o}function mr(i,t,e,s){let n=[];function o(r,a,l){const{startAngle:c,endAngle:h}=r.getProps(["startAngle","endAngle"],s),{angle:d}=Ds(r,{x:t.x,y:t.y});Me(d,c,h)&&n.push({element:r,datasetIndex:a,index:l})}return Ae(i,e,t,o),n}function br(i,t,e,s,n,o){let r=[];const a=pr(e);let l=Number.POSITIVE_INFINITY;function c(h,d,f){const u=h.inRange(t.x,t.y,n);if(s&&!u)return;const g=h.getCenterPoint(n);if(!(!!o||i.isPointInArea(g))&&!u)return;const m=a(t,g);m<l?(r=[{element:h,datasetIndex:d,index:f}],l=m):m===l&&r.push({element:h,datasetIndex:d,index:f})}return Ae(i,e,t,c),r}function Be(i,t,e,s,n,o){return!o&&!i.isPointInArea(t)?[]:e==="r"&&!s?mr(i,t,e,n):br(i,t,e,s,n,o)}function Vi(i,t,e,s,n){const o=[],r=e==="x"?"inXRange":"inYRange";let a=!1;return Ae(i,e,t,(l,c,h)=>{l[r]&&l[r](t[e],n)&&(o.push({element:l,datasetIndex:c,index:h}),a=a||l.inRange(t.x,t.y,n))}),s&&!a?[]:o}var _r={modes:{index(i,t,e,s){const n=vt(t,i),o=e.axis||"x",r=e.includeInvisible||!1,a=e.intersect?ze(i,n,o,s,r):Be(i,n,o,!1,s,r),l=[];return a.length?(i.getSortedVisibleDatasetMetas().forEach(c=>{const h=a[0].index,d=c.data[h];d&&!d.skip&&l.push({element:d,datasetIndex:c.index,index:h})}),l):[]},dataset(i,t,e,s){const n=vt(t,i),o=e.axis||"xy",r=e.includeInvisible||!1;let a=e.intersect?ze(i,n,o,s,r):Be(i,n,o,!1,s,r);if(a.length>0){const l=a[0].datasetIndex,c=i.getDatasetMeta(l).data;a=[];for(let h=0;h<c.length;++h)a.push({element:c[h],datasetIndex:l,index:h})}return a},point(i,t,e,s){const n=vt(t,i),o=e.axis||"xy",r=e.includeInvisible||!1;return ze(i,n,o,s,r)},nearest(i,t,e,s){const n=vt(t,i),o=e.axis||"xy",r=e.includeInvisible||!1;return Be(i,n,o,e.intersect,s,r)},x(i,t,e,s){const n=vt(t,i);return Vi(i,n,"x",e.intersect,s)},y(i,t,e,s){const n=vt(t,i);return Vi(i,n,"y",e.intersect,s)}}};const qs=["left","top","right","bottom"];function Ht(i,t){return i.filter(e=>e.pos===t)}function ji(i,t){return i.filter(e=>qs.indexOf(e.pos)===-1&&e.box.axis===t)}function Wt(i,t){return i.sort((e,s)=>{const n=t?s:e,o=t?e:s;return n.weight===o.weight?n.index-o.index:n.weight-o.weight})}function xr(i){const t=[];let e,s,n,o,r,a;for(e=0,s=(i||[]).length;e<s;++e)n=i[e],{position:o,options:{stack:r,stackWeight:a=1}}=n,t.push({index:e,box:n,pos:o,horizontal:n.isHorizontal(),weight:n.weight,stack:r&&o+r,stackWeight:a});return t}function yr(i){const t={};for(const e of i){const{stack:s,pos:n,stackWeight:o}=e;if(!s||!qs.includes(n))continue;const r=t[s]||(t[s]={count:0,placed:0,weight:0,size:0});r.count++,r.weight+=o}return t}function vr(i,t){const e=yr(i),{vBoxMaxWidth:s,hBoxMaxHeight:n}=t;let o,r,a;for(o=0,r=i.length;o<r;++o){a=i[o];const{fullSize:l}=a.box,c=e[a.stack],h=c&&a.stackWeight/c.weight;a.horizontal?(a.width=h?h*s:l&&t.availableWidth,a.height=n):(a.width=s,a.height=h?h*n:l&&t.availableHeight)}return e}function Mr(i){const t=xr(i),e=Wt(t.filter(c=>c.box.fullSize),!0),s=Wt(Ht(t,"left"),!0),n=Wt(Ht(t,"right")),o=Wt(Ht(t,"top"),!0),r=Wt(Ht(t,"bottom")),a=ji(t,"x"),l=ji(t,"y");return{fullSize:e,leftAndTop:s.concat(o),rightAndBottom:n.concat(l).concat(r).concat(a),chartArea:Ht(t,"chartArea"),vertical:s.concat(n).concat(l),horizontal:o.concat(r).concat(a)}}function Ni(i,t,e,s){return Math.max(i[e],t[e])+Math.max(i[s],t[s])}function Gs(i,t){i.top=Math.max(i.top,t.top),i.left=Math.max(i.left,t.left),i.bottom=Math.max(i.bottom,t.bottom),i.right=Math.max(i.right,t.right)}function kr(i,t,e,s){const{pos:n,box:o}=e,r=i.maxPadding;if(!C(n)){e.size&&(i[n]-=e.size);const d=s[e.stack]||{size:0,count:1};d.size=Math.max(d.size,e.horizontal?o.height:o.width),e.size=d.size/d.count,i[n]+=e.size}o.getPadding&&Gs(r,o.getPadding());const a=Math.max(0,t.outerWidth-Ni(r,i,"left","right")),l=Math.max(0,t.outerHeight-Ni(r,i,"top","bottom")),c=a!==i.w,h=l!==i.h;return i.w=a,i.h=l,e.horizontal?{same:c,other:h}:{same:h,other:c}}function wr(i){const t=i.maxPadding;function e(s){const n=Math.max(t[s]-i[s],0);return i[s]+=n,n}i.y+=e("top"),i.x+=e("left"),e("right"),e("bottom")}function Sr(i,t){const e=t.maxPadding;function s(n){const o={left:0,top:0,right:0,bottom:0};return n.forEach(r=>{o[r]=Math.max(t[r],e[r])}),o}return s(i?["left","right"]:["top","bottom"])}function $t(i,t,e,s){const n=[];let o,r,a,l,c,h;for(o=0,r=i.length,c=0;o<r;++o){a=i[o],l=a.box,l.update(a.width||t.w,a.height||t.h,Sr(a.horizontal,t));const{same:d,other:f}=kr(t,e,a,s);c|=d&&n.length,h=h||f,l.fullSize||n.push(a)}return c&&$t(n,t,e,s)||h}function ce(i,t,e,s,n){i.top=e,i.left=t,i.right=t+s,i.bottom=e+n,i.width=s,i.height=n}function $i(i,t,e,s){const n=e.padding;let{x:o,y:r}=t;for(const a of i){const l=a.box,c=s[a.stack]||{placed:0,weight:1},h=a.stackWeight/c.weight||1;if(a.horizontal){const d=t.w*h,f=c.size||l.height;Zt(c.start)&&(r=c.start),l.fullSize?ce(l,n.left,r,e.outerWidth-n.right-n.left,f):ce(l,t.left+c.placed,r,d,f),c.start=r,c.placed+=d,r=l.bottom}else{const d=t.h*h,f=c.size||l.width;Zt(c.start)&&(o=c.start),l.fullSize?ce(l,o,n.top,f,e.outerHeight-n.bottom-n.top):ce(l,o,t.top+c.placed,f,d),c.start=o,c.placed+=d,o=l.right}}t.x=o,t.y=r}var Z={addBox(i,t){i.boxes||(i.boxes=[]),t.fullSize=t.fullSize||!1,t.position=t.position||"top",t.weight=t.weight||0,t._layers=t._layers||function(){return[{z:0,draw(e){t.draw(e)}}]},i.boxes.push(t)},removeBox(i,t){const e=i.boxes?i.boxes.indexOf(t):-1;e!==-1&&i.boxes.splice(e,1)},configure(i,t,e){t.fullSize=e.fullSize,t.position=e.position,t.weight=e.weight},update(i,t,e,s){if(!i)return;const n=J(i.options.layout.padding),o=Math.max(t-n.width,0),r=Math.max(e-n.height,0),a=Mr(i.boxes),l=a.vertical,c=a.horizontal;T(i.boxes,p=>{typeof p.beforeLayout=="function"&&p.beforeLayout()});const h=l.reduce((p,m)=>m.box.options&&m.box.options.display===!1?p:p+1,0)||1,d=Object.freeze({outerWidth:t,outerHeight:e,padding:n,availableWidth:o,availableHeight:r,vBoxMaxWidth:o/2/h,hBoxMaxHeight:r/2}),f=Object.assign({},n);Gs(f,J(s));const u=Object.assign({maxPadding:f,w:o,h:r,x:n.left,y:n.top},n),g=vr(l.concat(c),d);$t(a.fullSize,u,d,g),$t(l,u,d,g),$t(c,u,d,g)&&$t(l,u,d,g),wr(u),$i(a.leftAndTop,u,d,g),u.x+=u.w,u.y+=u.h,$i(a.rightAndBottom,u,d,g),i.chartArea={left:u.left,top:u.top,right:u.left+u.w,bottom:u.top+u.h,height:u.h,width:u.w},T(a.chartArea,p=>{const m=p.box;Object.assign(m,i.chartArea),m.update(u.w,u.h,{left:0,top:0,right:0,bottom:0})})}};class Zs{acquireContext(t,e){}releaseContext(t){return!1}addEventListener(t,e,s){}removeEventListener(t,e,s){}getDevicePixelRatio(){return 1}getMaximumSize(t,e,s,n){return e=Math.max(0,e||t.width),s=s||t.height,{width:e,height:Math.max(0,n?Math.floor(e/n):s)}}isAttached(t){return!0}updateConfig(t){}}class Or extends Zs{acquireContext(t){return t&&t.getContext&&t.getContext("2d")||null}updateConfig(t){t.options.animation=!1}}const me="$chartjs",Pr={touchstart:"mousedown",touchmove:"mousemove",touchend:"mouseup",pointerenter:"mouseenter",pointerdown:"mousedown",pointermove:"mousemove",pointerup:"mouseup",pointerleave:"mouseout",pointerout:"mouseout"},Yi=i=>i===null||i==="";function Ar(i,t){const e=i.style,s=i.getAttribute("height"),n=i.getAttribute("width");if(i[me]={initial:{height:s,width:n,style:{display:e.display,height:e.height,width:e.width}}},e.display=e.display||"block",e.boxSizing=e.boxSizing||"border-box",Yi(n)){const o=Ti(i,"width");o!==void 0&&(i.width=o)}if(Yi(s))if(i.style.height==="")i.height=i.width/(t||2);else{const o=Ti(i,"height");o!==void 0&&(i.height=o)}return i}const Qs=Fo?{passive:!0}:!1;function Cr(i,t,e){i&&i.addEventListener(t,e,Qs)}function Dr(i,t,e){i&&i.canvas&&i.canvas.removeEventListener(t,e,Qs)}function Tr(i,t){const e=Pr[i.type]||i.type,{x:s,y:n}=vt(i,t);return{type:e,chart:t,native:i,x:s!==void 0?s:null,y:n!==void 0?n:null}}function Se(i,t){for(const e of i)if(e===t||e.contains(t))return!0}function Rr(i,t,e){const s=i.canvas,n=new MutationObserver(o=>{let r=!1;for(const a of o)r=r||Se(a.addedNodes,s),r=r&&!Se(a.removedNodes,s);r&&e()});return n.observe(document,{childList:!0,subtree:!0}),n}function Lr(i,t,e){const s=i.canvas,n=new MutationObserver(o=>{let r=!1;for(const a of o)r=r||Se(a.removedNodes,s),r=r&&!Se(a.addedNodes,s);r&&e()});return n.observe(document,{childList:!0,subtree:!0}),n}const Jt=new Map;let Xi=0;function Js(){const i=window.devicePixelRatio;i!==Xi&&(Xi=i,Jt.forEach((t,e)=>{e.currentDevicePixelRatio!==i&&t()}))}function Er(i,t){Jt.size||window.addEventListener("resize",Js),Jt.set(i,t)}function Fr(i){Jt.delete(i),Jt.size||window.removeEventListener("resize",Js)}function Ir(i,t,e){const s=i.canvas,n=s&&ai(s);if(!n)return;const o=Es((a,l)=>{const c=n.clientWidth;e(a,l),c<n.clientWidth&&e()},window),r=new ResizeObserver(a=>{const l=a[0],c=l.contentRect.width,h=l.contentRect.height;c===0&&h===0||o(c,h)});return r.observe(n),Er(i,o),r}function He(i,t,e){e&&e.disconnect(),t==="resize"&&Fr(i)}function zr(i,t,e){const s=i.canvas,n=Es(o=>{i.ctx!==null&&e(Tr(o,i))},i);return Cr(s,t,n),n}class Br extends Zs{acquireContext(t,e){const s=t&&t.getContext&&t.getContext("2d");return s&&s.canvas===t?(Ar(t,e),s):null}releaseContext(t){const e=t.canvas;if(!e[me])return!1;const s=e[me].initial;["height","width"].forEach(o=>{const r=s[o];L(r)?e.removeAttribute(o):e.setAttribute(o,r)});const n=s.style||{};return Object.keys(n).forEach(o=>{e.style[o]=n[o]}),e.width=e.width,delete e[me],!0}addEventListener(t,e,s){this.removeEventListener(t,e);const n=t.$proxies||(t.$proxies={}),r={attach:Rr,detach:Lr,resize:Ir}[e]||zr;n[e]=r(t,e,s)}removeEventListener(t,e){const s=t.$proxies||(t.$proxies={}),n=s[e];if(!n)return;({attach:He,detach:He,resize:He}[e]||Dr)(t,e,n),s[e]=void 0}getDevicePixelRatio(){return window.devicePixelRatio}getMaximumSize(t,e,s,n){return Eo(t,e,s,n)}isAttached(t){const e=t&&ai(t);return!!(e&&e.isConnected)}}function Hr(i){return!ri()||typeof OffscreenCanvas<"u"&&i instanceof OffscreenCanvas?Or:Br}class Ot{static defaults={};static defaultRoutes=void 0;x;y;active=!1;options;$animations;tooltipPosition(t){const{x:e,y:s}=this.getProps(["x","y"],t);return{x:e,y:s}}hasValue(){return ve(this.x)&&ve(this.y)}getProps(t,e){const s=this.$animations;if(!e||!s)return this;const n={};return t.forEach(o=>{n[o]=s[o]&&s[o].active()?s[o]._to:this[o]}),n}}function Wr(i,t){const e=i.options.ticks,s=Vr(i),n=Math.min(e.maxTicksLimit||s,s),o=e.major.enabled?Nr(t):[],r=o.length,a=o[0],l=o[r-1],c=[];if(r>n)return $r(t,c,o,r/n),c;const h=jr(o,t,n);if(r>0){let d,f;const u=r>1?Math.round((l-a)/(r-1)):null;for(he(t,c,h,L(u)?0:a-u,a),d=0,f=r-1;d<f;d++)he(t,c,h,o[d],o[d+1]);return he(t,c,h,l,L(u)?t.length:l+u),c}return he(t,c,h),c}function Vr(i){const t=i.options.offset,e=i._tickSize(),s=i._length/e+(t?0:1),n=i._maxLength/e;return Math.floor(Math.min(s,n))}function jr(i,t,e){const s=Yr(i),n=t.length/e;if(!s)return Math.max(n,1);const o=Nn(s);for(let r=0,a=o.length-1;r<a;r++){const l=o[r];if(l>n)return l}return Math.max(n,1)}function Nr(i){const t=[];let e,s;for(e=0,s=i.length;e<s;e++)i[e].major&&t.push(e);return t}function $r(i,t,e,s){let n=0,o=e[0],r;for(s=Math.ceil(s),r=0;r<i.length;r++)r===o&&(t.push(i[r]),n++,o=e[n*s])}function he(i,t,e,s,n){const o=D(s,0),r=Math.min(D(n,i.length),i.length);let a=0,l,c,h;for(e=Math.ceil(e),n&&(l=n-s,e=l/Math.floor(l/e)),h=o;h<0;)a++,h=Math.round(o+a*e);for(c=Math.max(o,0);c<r;c++)c===h&&(t.push(i[c]),a++,h=Math.round(o+a*e))}function Yr(i){const t=i.length;let e,s;if(t<2)return!1;for(s=i[0],e=1;e<t;++e)if(i[e]-i[e-1]!==s)return!1;return s}const Xr=i=>i==="left"?"right":i==="right"?"left":i,Ui=(i,t,e)=>t==="top"||t==="left"?i[t]+e:i[t]-e,Ki=(i,t)=>Math.min(t||i,i);function qi(i,t){const e=[],s=i.length/t,n=i.length;let o=0;for(;o<n;o+=s)e.push(i[Math.floor(o)]);return e}function Ur(i,t,e){const s=i.ticks.length,n=Math.min(t,s-1),o=i._startPixel,r=i._endPixel,a=1e-6;let l=i.getPixelForTick(n),c;if(!(e&&(s===1?c=Math.max(l-o,r-l):t===0?c=(i.getPixelForTick(1)-l)/2:c=(l-i.getPixelForTick(n-1))/2,l+=n<t?c:-c,l<o-a||l>r+a)))return l}function Kr(i,t){T(i,e=>{const s=e.gc,n=s.length/2;let o;if(n>t){for(o=0;o<n;++o)delete e.data[s[o]];s.splice(0,n)}})}function Vt(i){return i.drawTicks?i.tickLength:0}function Gi(i,t){if(!i.display)return 0;const e=Y(i.font,t),s=J(i.padding);return(W(i.text)?i.text.length:1)*e.lineHeight+s.height}function qr(i,t){return Et(i,{scale:t,type:"scale"})}function Gr(i,t,e){return Et(i,{tick:e,index:t,type:"tick"})}function Zr(i,t,e){let s=Qe(i);return(e&&t!=="right"||!e&&t==="right")&&(s=Xr(s)),s}function Qr(i,t,e,s){const{top:n,left:o,bottom:r,right:a,chart:l}=i,{chartArea:c,scales:h}=l;let d=0,f,u,g;const p=r-n,m=a-o;if(i.isHorizontal()){if(u=$(s,o,a),C(e)){const b=Object.keys(e)[0],_=e[b];g=h[b].getPixelForValue(_)+p-t}else e==="center"?g=(c.bottom+c.top)/2+p-t:g=Ui(i,e,t);f=a-o}else{if(C(e)){const b=Object.keys(e)[0],_=e[b];u=h[b].getPixelForValue(_)-m+t}else e==="center"?u=(c.left+c.right)/2-m+t:u=Ui(i,e,t);g=$(s,r,n),d=e==="left"?-V:V}return{titleX:u,titleY:g,maxWidth:f,rotation:d}}class Ft extends Ot{constructor(t){super(),this.id=t.id,this.type=t.type,this.options=void 0,this.ctx=t.ctx,this.chart=t.chart,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this._margins={left:0,right:0,top:0,bottom:0},this.maxWidth=void 0,this.maxHeight=void 0,this.paddingTop=void 0,this.paddingBottom=void 0,this.paddingLeft=void 0,this.paddingRight=void 0,this.axis=void 0,this.labelRotation=void 0,this.min=void 0,this.max=void 0,this._range=void 0,this.ticks=[],this._gridLineItems=null,this._labelItems=null,this._labelSizes=null,this._length=0,this._maxLength=0,this._longestTextCache={},this._startPixel=void 0,this._endPixel=void 0,this._reversePixels=!1,this._userMax=void 0,this._userMin=void 0,this._suggestedMax=void 0,this._suggestedMin=void 0,this._ticksLength=0,this._borderValue=0,this._cache={},this._dataLimitsCached=!1,this.$context=void 0}init(t){this.options=t.setContext(this.getContext()),this.axis=t.axis,this._userMin=this.parse(t.min),this._userMax=this.parse(t.max),this._suggestedMin=this.parse(t.suggestedMin),this._suggestedMax=this.parse(t.suggestedMax)}parse(t,e){return t}getUserBounds(){let{_userMin:t,_userMax:e,_suggestedMin:s,_suggestedMax:n}=this;return t=et(t,Number.POSITIVE_INFINITY),e=et(e,Number.NEGATIVE_INFINITY),s=et(s,Number.POSITIVE_INFINITY),n=et(n,Number.NEGATIVE_INFINITY),{min:et(t,s),max:et(e,n),minDefined:Q(t),maxDefined:Q(e)}}getMinMax(t){let{min:e,max:s,minDefined:n,maxDefined:o}=this.getUserBounds(),r;if(n&&o)return{min:e,max:s};const a=this.getMatchingVisibleMetas();for(let l=0,c=a.length;l<c;++l)r=a[l].controller.getMinMax(this,t),n||(e=Math.min(e,r.min)),o||(s=Math.max(s,r.max));return e=o&&e>s?s:e,s=n&&e>s?e:s,{min:et(e,et(s,e)),max:et(s,et(e,s))}}getPadding(){return{left:this.paddingLeft||0,top:this.paddingTop||0,right:this.paddingRight||0,bottom:this.paddingBottom||0}}getTicks(){return this.ticks}getLabels(){const t=this.chart.data;return this.options.labels||(this.isHorizontal()?t.xLabels:t.yLabels)||t.labels||[]}getLabelItems(t=this.chart.chartArea){return this._labelItems||(this._labelItems=this._computeLabelItems(t))}beforeLayout(){this._cache={},this._dataLimitsCached=!1}beforeUpdate(){E(this.options.beforeUpdate,[this])}update(t,e,s){const{beginAtZero:n,grace:o,ticks:r}=this.options,a=r.sampleSize;this.beforeUpdate(),this.maxWidth=t,this.maxHeight=e,this._margins=s=Object.assign({left:0,right:0,top:0,bottom:0},s),this.ticks=null,this._labelSizes=null,this._gridLineItems=null,this._labelItems=null,this.beforeSetDimensions(),this.setDimensions(),this.afterSetDimensions(),this._maxLength=this.isHorizontal()?this.width+s.left+s.right:this.height+s.top+s.bottom,this._dataLimitsCached||(this.beforeDataLimits(),this.determineDataLimits(),this.afterDataLimits(),this._range=xo(this,o,n),this._dataLimitsCached=!0),this.beforeBuildTicks(),this.ticks=this.buildTicks()||[],this.afterBuildTicks();const l=a<this.ticks.length;this._convertTicksToLabels(l?qi(this.ticks,a):this.ticks),this.configure(),this.beforeCalculateLabelRotation(),this.calculateLabelRotation(),this.afterCalculateLabelRotation(),r.display&&(r.autoSkip||r.source==="auto")&&(this.ticks=Wr(this,this.ticks),this._labelSizes=null,this.afterAutoSkip()),l&&this._convertTicksToLabels(this.ticks),this.beforeFit(),this.fit(),this.afterFit(),this.afterUpdate()}configure(){let t=this.options.reverse,e,s;this.isHorizontal()?(e=this.left,s=this.right):(e=this.top,s=this.bottom,t=!t),this._startPixel=e,this._endPixel=s,this._reversePixels=t,this._length=s-e,this._alignToPixels=this.options.alignToPixels}afterUpdate(){E(this.options.afterUpdate,[this])}beforeSetDimensions(){E(this.options.beforeSetDimensions,[this])}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=0,this.right=this.width):(this.height=this.maxHeight,this.top=0,this.bottom=this.height),this.paddingLeft=0,this.paddingTop=0,this.paddingRight=0,this.paddingBottom=0}afterSetDimensions(){E(this.options.afterSetDimensions,[this])}_callHooks(t){this.chart.notifyPlugins(t,this.getContext()),E(this.options[t],[this])}beforeDataLimits(){this._callHooks("beforeDataLimits")}determineDataLimits(){}afterDataLimits(){this._callHooks("afterDataLimits")}beforeBuildTicks(){this._callHooks("beforeBuildTicks")}buildTicks(){return[]}afterBuildTicks(){this._callHooks("afterBuildTicks")}beforeTickToLabelConversion(){E(this.options.beforeTickToLabelConversion,[this])}generateTickLabels(t){const e=this.options.ticks;let s,n,o;for(s=0,n=t.length;s<n;s++)o=t[s],o.label=E(e.callback,[o.value,s,t],this)}afterTickToLabelConversion(){E(this.options.afterTickToLabelConversion,[this])}beforeCalculateLabelRotation(){E(this.options.beforeCalculateLabelRotation,[this])}calculateLabelRotation(){const t=this.options,e=t.ticks,s=Ki(this.ticks.length,t.ticks.maxTicksLimit),n=e.minRotation||0,o=e.maxRotation;let r=n,a,l,c;if(!this._isVisible()||!e.display||n>=o||s<=1||!this.isHorizontal()){this.labelRotation=n;return}const h=this._getLabelSizes(),d=h.widest.width,f=h.highest.height,u=K(this.chart.width-d,0,this.maxWidth);a=t.offset?this.maxWidth/s:u/(s-1),d+6>a&&(a=u/(s-(t.offset?.5:1)),l=this.maxHeight-Vt(t.grid)-e.padding-Gi(t.title,this.chart.options.font),c=Math.sqrt(d*d+f*f),r=Un(Math.min(Math.asin(K((h.highest.height+6)/a,-1,1)),Math.asin(K(l/c,-1,1))-Math.asin(K(f/c,-1,1)))),r=Math.max(n,Math.min(o,r))),this.labelRotation=r}afterCalculateLabelRotation(){E(this.options.afterCalculateLabelRotation,[this])}afterAutoSkip(){}beforeFit(){E(this.options.beforeFit,[this])}fit(){const t={width:0,height:0},{chart:e,options:{ticks:s,title:n,grid:o}}=this,r=this._isVisible(),a=this.isHorizontal();if(r){const l=Gi(n,e.options.font);if(a?(t.width=this.maxWidth,t.height=Vt(o)+l):(t.height=this.maxHeight,t.width=Vt(o)+l),s.display&&this.ticks.length){const{first:c,last:h,widest:d,highest:f}=this._getLabelSizes(),u=s.padding*2,g=ct(this.labelRotation),p=Math.cos(g),m=Math.sin(g);if(a){const b=s.mirror?0:m*d.width+p*f.height;t.height=Math.min(this.maxHeight,t.height+b+u)}else{const b=s.mirror?0:p*d.width+m*f.height;t.width=Math.min(this.maxWidth,t.width+b+u)}this._calculatePadding(c,h,m,p)}}this._handleMargins(),a?(this.width=this._length=e.width-this._margins.left-this._margins.right,this.height=t.height):(this.width=t.width,this.height=this._length=e.height-this._margins.top-this._margins.bottom)}_calculatePadding(t,e,s,n){const{ticks:{align:o,padding:r},position:a}=this.options,l=this.labelRotation!==0,c=a!=="top"&&this.axis==="x";if(this.isHorizontal()){const h=this.getPixelForTick(0)-this.left,d=this.right-this.getPixelForTick(this.ticks.length-1);let f=0,u=0;l?c?(f=n*t.width,u=s*e.height):(f=s*t.height,u=n*e.width):o==="start"?u=e.width:o==="end"?f=t.width:o!=="inner"&&(f=t.width/2,u=e.width/2),this.paddingLeft=Math.max((f-h+r)*this.width/(this.width-h),0),this.paddingRight=Math.max((u-d+r)*this.width/(this.width-d),0)}else{let h=e.height/2,d=t.height/2;o==="start"?(h=0,d=t.height):o==="end"&&(h=e.height,d=0),this.paddingTop=h+r,this.paddingBottom=d+r}}_handleMargins(){this._margins&&(this._margins.left=Math.max(this.paddingLeft,this._margins.left),this._margins.top=Math.max(this.paddingTop,this._margins.top),this._margins.right=Math.max(this.paddingRight,this._margins.right),this._margins.bottom=Math.max(this.paddingBottom,this._margins.bottom))}afterFit(){E(this.options.afterFit,[this])}isHorizontal(){const{axis:t,position:e}=this.options;return e==="top"||e==="bottom"||t==="x"}isFullSize(){return this.options.fullSize}_convertTicksToLabels(t){this.beforeTickToLabelConversion(),this.generateTickLabels(t);let e,s;for(e=0,s=t.length;e<s;e++)L(t[e].label)&&(t.splice(e,1),s--,e--);this.afterTickToLabelConversion()}_getLabelSizes(){let t=this._labelSizes;if(!t){const e=this.options.ticks.sampleSize;let s=this.ticks;e<s.length&&(s=qi(s,e)),this._labelSizes=t=this._computeLabelSizes(s,s.length,this.options.ticks.maxTicksLimit)}return t}_computeLabelSizes(t,e,s){const{ctx:n,_longestTextCache:o}=this,r=[],a=[],l=Math.floor(e/Ki(e,s));let c=0,h=0,d,f,u,g,p,m,b,_,y,v,x;for(d=0;d<e;d+=l){if(g=t[d].label,p=this._resolveTickFontOptions(d),n.font=m=p.string,b=o[m]=o[m]||{data:{},gc:[]},_=p.lineHeight,y=v=0,!L(g)&&!W(g))y=Si(n,b.data,b.gc,y,g),v=_;else if(W(g))for(f=0,u=g.length;f<u;++f)x=g[f],!L(x)&&!W(x)&&(y=Si(n,b.data,b.gc,y,x),v+=_);r.push(y),a.push(v),c=Math.max(y,c),h=Math.max(v,h)}Kr(o,e);const M=r.indexOf(c),k=a.indexOf(h),w=S=>({width:r[S]||0,height:a[S]||0});return{first:w(0),last:w(e-1),widest:w(M),highest:w(k),widths:r,heights:a}}getLabelForValue(t){return t}getPixelForValue(t,e){return NaN}getValueForPixel(t){}getPixelForTick(t){const e=this.ticks;return t<0||t>e.length-1?null:this.getPixelForValue(e[t].value)}getPixelForDecimal(t){this._reversePixels&&(t=1-t);const e=this._startPixel+t*this._length;return qn(this._alignToPixels?xt(this.chart,e,0):e)}getDecimalForPixel(t){const e=(t-this._startPixel)/this._length;return this._reversePixels?1-e:e}getBasePixel(){return this.getPixelForValue(this.getBaseValue())}getBaseValue(){const{min:t,max:e}=this;return t<0&&e<0?e:t>0&&e>0?t:0}getContext(t){const e=this.ticks||[];if(t>=0&&t<e.length){const s=e[t];return s.$context||(s.$context=Gr(this.getContext(),t,s))}return this.$context||(this.$context=qr(this.chart.getContext(),this))}_tickSize(){const t=this.options.ticks,e=ct(this.labelRotation),s=Math.abs(Math.cos(e)),n=Math.abs(Math.sin(e)),o=this._getLabelSizes(),r=t.autoSkipPadding||0,a=o?o.widest.width+r:0,l=o?o.highest.height+r:0;return this.isHorizontal()?l*s>a*n?a/s:l/n:l*n<a*s?l/s:a/n}_isVisible(){const t=this.options.display;return t!=="auto"?!!t:this.getMatchingVisibleMetas().length>0}_computeGridLineItems(t){const e=this.axis,s=this.chart,n=this.options,{grid:o,position:r,border:a}=n,l=o.offset,c=this.isHorizontal(),d=this.ticks.length+(l?1:0),f=Vt(o),u=[],g=a.setContext(this.getContext()),p=g.display?g.width:0,m=p/2,b=function(F){return xt(s,F,p)};let _,y,v,x,M,k,w,S,P,O,A,j;if(r==="top")_=b(this.bottom),k=this.bottom-f,S=_-m,O=b(t.top)+m,j=t.bottom;else if(r==="bottom")_=b(this.top),O=t.top,j=b(t.bottom)-m,k=_+m,S=this.top+f;else if(r==="left")_=b(this.right),M=this.right-f,w=_-m,P=b(t.left)+m,A=t.right;else if(r==="right")_=b(this.left),P=t.left,A=b(t.right)-m,M=_+m,w=this.left+f;else if(e==="x"){if(r==="center")_=b((t.top+t.bottom)/2+.5);else if(C(r)){const F=Object.keys(r)[0],I=r[F];_=b(this.chart.scales[F].getPixelForValue(I))}O=t.top,j=t.bottom,k=_+m,S=k+f}else if(e==="y"){if(r==="center")_=b((t.left+t.right)/2);else if(C(r)){const F=Object.keys(r)[0],I=r[F];_=b(this.chart.scales[F].getPixelForValue(I))}M=_-m,w=M-f,P=t.left,A=t.right}const q=D(n.ticks.maxTicksLimit,d),R=Math.max(1,Math.ceil(d/q));for(y=0;y<d;y+=R){const F=this.getContext(y),I=o.setContext(F),tt=a.setContext(F),N=I.lineWidth,Pt=I.color,ee=tt.dash||[],At=tt.dashOffset,It=I.tickWidth,pt=I.tickColor,zt=I.tickBorderDash||[],mt=I.tickBorderDashOffset;v=Ur(this,y,l),v!==void 0&&(x=xt(s,v,N),c?M=w=P=A=x:k=S=O=j=x,u.push({tx1:M,ty1:k,tx2:w,ty2:S,x1:P,y1:O,x2:A,y2:j,width:N,color:Pt,borderDash:ee,borderDashOffset:At,tickWidth:It,tickColor:pt,tickBorderDash:zt,tickBorderDashOffset:mt}))}return this._ticksLength=d,this._borderValue=_,u}_computeLabelItems(t){const e=this.axis,s=this.options,{position:n,ticks:o}=s,r=this.isHorizontal(),a=this.ticks,{align:l,crossAlign:c,padding:h,mirror:d}=o,f=Vt(s.grid),u=f+h,g=d?-h:u,p=-ct(this.labelRotation),m=[];let b,_,y,v,x,M,k,w,S,P,O,A,j="middle";if(n==="top")M=this.bottom-g,k=this._getXAxisLabelAlignment();else if(n==="bottom")M=this.top+g,k=this._getXAxisLabelAlignment();else if(n==="left"){const R=this._getYAxisLabelAlignment(f);k=R.textAlign,x=R.x}else if(n==="right"){const R=this._getYAxisLabelAlignment(f);k=R.textAlign,x=R.x}else if(e==="x"){if(n==="center")M=(t.top+t.bottom)/2+u;else if(C(n)){const R=Object.keys(n)[0],F=n[R];M=this.chart.scales[R].getPixelForValue(F)+u}k=this._getXAxisLabelAlignment()}else if(e==="y"){if(n==="center")x=(t.left+t.right)/2-u;else if(C(n)){const R=Object.keys(n)[0],F=n[R];x=this.chart.scales[R].getPixelForValue(F)}k=this._getYAxisLabelAlignment(f).textAlign}e==="y"&&(l==="start"?j="top":l==="end"&&(j="bottom"));const q=this._getLabelSizes();for(b=0,_=a.length;b<_;++b){y=a[b],v=y.label;const R=o.setContext(this.getContext(b));w=this.getPixelForTick(b)+o.labelOffset,S=this._resolveTickFontOptions(b),P=S.lineHeight,O=W(v)?v.length:1;const F=O/2,I=R.color,tt=R.textStrokeColor,N=R.textStrokeWidth;let Pt=k;r?(x=w,k==="inner"&&(b===_-1?Pt=this.options.reverse?"left":"right":b===0?Pt=this.options.reverse?"right":"left":Pt="center"),n==="top"?c==="near"||p!==0?A=-O*P+P/2:c==="center"?A=-q.highest.height/2-F*P+P:A=-q.highest.height+P/2:c==="near"||p!==0?A=P/2:c==="center"?A=q.highest.height/2-F*P:A=q.highest.height-O*P,d&&(A*=-1),p!==0&&!R.showLabelBackdrop&&(x+=P/2*Math.sin(p))):(M=w,A=(1-O)*P/2);let ee;if(R.showLabelBackdrop){const At=J(R.backdropPadding),It=q.heights[b],pt=q.widths[b];let zt=A-At.top,mt=0-At.left;switch(j){case"middle":zt-=It/2;break;case"bottom":zt-=It;break}switch(k){case"center":mt-=pt/2;break;case"right":mt-=pt;break;case"inner":b===_-1?mt-=pt:b>0&&(mt-=pt/2);break}ee={left:mt,top:zt,width:pt+At.width,height:It+At.height,color:R.backdropColor}}m.push({label:v,font:S,textOffset:A,options:{rotation:p,color:I,strokeColor:tt,strokeWidth:N,textAlign:Pt,textBaseline:j,translation:[x,M],backdrop:ee}})}return m}_getXAxisLabelAlignment(){const{position:t,ticks:e}=this.options;if(-ct(this.labelRotation))return t==="top"?"left":"right";let n="center";return e.align==="start"?n="left":e.align==="end"?n="right":e.align==="inner"&&(n="inner"),n}_getYAxisLabelAlignment(t){const{position:e,ticks:{crossAlign:s,mirror:n,padding:o}}=this.options,r=this._getLabelSizes(),a=t+o,l=r.widest.width;let c,h;return e==="left"?n?(h=this.right+o,s==="near"?c="left":s==="center"?(c="center",h+=l/2):(c="right",h+=l)):(h=this.right-a,s==="near"?c="right":s==="center"?(c="center",h-=l/2):(c="left",h=this.left)):e==="right"?n?(h=this.left+o,s==="near"?c="right":s==="center"?(c="center",h-=l/2):(c="left",h-=l)):(h=this.left+a,s==="near"?c="left":s==="center"?(c="center",h+=l/2):(c="right",h=this.right)):c="right",{textAlign:c,x:h}}_computeLabelArea(){if(this.options.ticks.mirror)return;const t=this.chart,e=this.options.position;if(e==="left"||e==="right")return{top:0,left:this.left,bottom:t.height,right:this.right};if(e==="top"||e==="bottom")return{top:this.top,left:0,bottom:this.bottom,right:t.width}}drawBackground(){const{ctx:t,options:{backgroundColor:e},left:s,top:n,width:o,height:r}=this;e&&(t.save(),t.fillStyle=e,t.fillRect(s,n,o,r),t.restore())}getLineWidthForValue(t){const e=this.options.grid;if(!this._isVisible()||!e.display)return 0;const n=this.ticks.findIndex(o=>o.value===t);return n>=0?e.setContext(this.getContext(n)).lineWidth:0}drawGrid(t){const e=this.options.grid,s=this.ctx,n=this._gridLineItems||(this._gridLineItems=this._computeGridLineItems(t));let o,r;const a=(l,c,h)=>{!h.width||!h.color||(s.save(),s.lineWidth=h.width,s.strokeStyle=h.color,s.setLineDash(h.borderDash||[]),s.lineDashOffset=h.borderDashOffset,s.beginPath(),s.moveTo(l.x,l.y),s.lineTo(c.x,c.y),s.stroke(),s.restore())};if(e.display)for(o=0,r=n.length;o<r;++o){const l=n[o];e.drawOnChartArea&&a({x:l.x1,y:l.y1},{x:l.x2,y:l.y2},l),e.drawTicks&&a({x:l.tx1,y:l.ty1},{x:l.tx2,y:l.ty2},{color:l.tickColor,width:l.tickWidth,borderDash:l.tickBorderDash,borderDashOffset:l.tickBorderDashOffset})}}drawBorder(){const{chart:t,ctx:e,options:{border:s,grid:n}}=this,o=s.setContext(this.getContext()),r=s.display?o.width:0;if(!r)return;const a=n.setContext(this.getContext(0)).lineWidth,l=this._borderValue;let c,h,d,f;this.isHorizontal()?(c=xt(t,this.left,r)-r/2,h=xt(t,this.right,a)+a/2,d=f=l):(d=xt(t,this.top,r)-r/2,f=xt(t,this.bottom,a)+a/2,c=h=l),e.save(),e.lineWidth=o.width,e.strokeStyle=o.color,e.beginPath(),e.moveTo(c,d),e.lineTo(h,f),e.stroke(),e.restore()}drawLabels(t){if(!this.options.ticks.display)return;const s=this.ctx,n=this._computeLabelArea();n&&ti(s,n);const o=this.getLabelItems(t);for(const r of o){const a=r.options,l=r.font,c=r.label,h=r.textOffset;Qt(s,c,0,h,l,a)}n&&ei(s)}drawTitle(){const{ctx:t,options:{position:e,title:s,reverse:n}}=this;if(!s.display)return;const o=Y(s.font),r=J(s.padding),a=s.align;let l=o.lineHeight/2;e==="bottom"||e==="center"||C(e)?(l+=r.bottom,W(s.text)&&(l+=o.lineHeight*(s.text.length-1))):l+=r.top;const{titleX:c,titleY:h,maxWidth:d,rotation:f}=Qr(this,l,e,a);Qt(t,s.text,0,0,o,{color:s.color,maxWidth:d,rotation:f,textAlign:Zr(a,e,n),textBaseline:"middle",translation:[c,h]})}draw(t){this._isVisible()&&(this.drawBackground(),this.drawGrid(t),this.drawBorder(),this.drawTitle(),this.drawLabels(t))}_layers(){const t=this.options,e=t.ticks&&t.ticks.z||0,s=D(t.grid&&t.grid.z,-1),n=D(t.border&&t.border.z,0);return!this._isVisible()||this.draw!==Ft.prototype.draw?[{z:e,draw:o=>{this.draw(o)}}]:[{z:s,draw:o=>{this.drawBackground(),this.drawGrid(o),this.drawTitle()}},{z:n,draw:()=>{this.drawBorder()}},{z:e,draw:o=>{this.drawLabels(o)}}]}getMatchingVisibleMetas(t){const e=this.chart.getSortedVisibleDatasetMetas(),s=this.axis+"AxisID",n=[];let o,r;for(o=0,r=e.length;o<r;++o){const a=e[o];a[s]===this.id&&(!t||a.type===t)&&n.push(a)}return n}_resolveTickFontOptions(t){const e=this.options.ticks.setContext(this.getContext(t));return Y(e.font)}_maxDigits(){const t=this._resolveTickFontOptions(0).lineHeight;return(this.isHorizontal()?this.width:this.height)/t}}class de{constructor(t,e,s){this.type=t,this.scope=e,this.override=s,this.items=Object.create(null)}isForType(t){return Object.prototype.isPrototypeOf.call(this.type.prototype,t.prototype)}register(t){const e=Object.getPrototypeOf(t);let s;ea(e)&&(s=this.register(e));const n=this.items,o=t.id,r=this.scope+"."+o;if(!o)throw new Error("class does not have id: "+t);return o in n||(n[o]=t,Jr(t,r,s),this.override&&z.override(t.id,t.overrides)),r}get(t){return this.items[t]}unregister(t){const e=this.items,s=t.id,n=this.scope;s in e&&delete e[s],n&&s in z[n]&&(delete z[n][s],this.override&&delete St[s])}}function Jr(i,t,e){const s=Gt(Object.create(null),[e?z.get(e):{},z.get(t),i.defaults]);z.set(t,s),i.defaultRoutes&&ta(t,i.defaultRoutes),i.descriptors&&z.describe(t,i.descriptors)}function ta(i,t){Object.keys(t).forEach(e=>{const s=e.split("."),n=s.pop(),o=[i].concat(s).join("."),r=t[e].split("."),a=r.pop(),l=r.join(".");z.route(o,n,l,a)})}function ea(i){return"id"in i&&"defaults"in i}class ia{constructor(){this.controllers=new de(li,"datasets",!0),this.elements=new de(Ot,"elements"),this.plugins=new de(Object,"plugins"),this.scales=new de(Ft,"scales"),this._typedRegistries=[this.controllers,this.scales,this.elements]}add(...t){this._each("register",t)}remove(...t){this._each("unregister",t)}addControllers(...t){this._each("register",t,this.controllers)}addElements(...t){this._each("register",t,this.elements)}addPlugins(...t){this._each("register",t,this.plugins)}addScales(...t){this._each("register",t,this.scales)}getController(t){return this._get(t,this.controllers,"controller")}getElement(t){return this._get(t,this.elements,"element")}getPlugin(t){return this._get(t,this.plugins,"plugin")}getScale(t){return this._get(t,this.scales,"scale")}removeControllers(...t){this._each("unregister",t,this.controllers)}removeElements(...t){this._each("unregister",t,this.elements)}removePlugins(...t){this._each("unregister",t,this.plugins)}removeScales(...t){this._each("unregister",t,this.scales)}_each(t,e,s){[...e].forEach(n=>{const o=s||this._getRegistryForType(n);s||o.isForType(n)||o===this.plugins&&n.id?this._exec(t,o,n):T(n,r=>{const a=s||this._getRegistryForType(r);this._exec(t,a,r)})})}_exec(t,e,s){const n=Ge(t);E(s["before"+n],[],s),e[t](s),E(s["after"+n],[],s)}_getRegistryForType(t){for(let e=0;e<this._typedRegistries.length;e++){const s=this._typedRegistries[e];if(s.isForType(t))return s}return this.plugins}_get(t,e,s){const n=e.get(t);if(n===void 0)throw new Error('"'+t+'" is not a registered '+s+".");return n}}var nt=new ia;class sa{constructor(){this._init=[]}notify(t,e,s,n){e==="beforeInit"&&(this._init=this._createDescriptors(t,!0),this._notify(this._init,t,"install"));const o=n?this._descriptors(t).filter(n):this._descriptors(t),r=this._notify(o,t,e,s);return e==="afterDestroy"&&(this._notify(o,t,"stop"),this._notify(this._init,t,"uninstall")),r}_notify(t,e,s,n){n=n||{};for(const o of t){const r=o.plugin,a=r[s],l=[e,n,o.options];if(E(a,l,r)===!1&&n.cancelable)return!1}return!0}invalidate(){L(this._cache)||(this._oldCache=this._cache,this._cache=void 0)}_descriptors(t){if(this._cache)return this._cache;const e=this._cache=this._createDescriptors(t);return this._notifyStateChanges(t),e}_createDescriptors(t,e){const s=t&&t.config,n=D(s.options&&s.options.plugins,{}),o=na(s);return n===!1&&!e?[]:ra(t,o,n,e)}_notifyStateChanges(t){const e=this._oldCache||[],s=this._cache,n=(o,r)=>o.filter(a=>!r.some(l=>a.plugin.id===l.plugin.id));this._notify(n(e,s),t,"stop"),this._notify(n(s,e),t,"start")}}function na(i){const t={},e=[],s=Object.keys(nt.plugins.items);for(let o=0;o<s.length;o++)e.push(nt.getPlugin(s[o]));const n=i.plugins||[];for(let o=0;o<n.length;o++){const r=n[o];e.indexOf(r)===-1&&(e.push(r),t[r.id]=!0)}return{plugins:e,localIds:t}}function oa(i,t){return!t&&i===!1?null:i===!0?{}:i}function ra(i,{plugins:t,localIds:e},s,n){const o=[],r=i.getContext();for(const a of t){const l=a.id,c=oa(s[l],n);c!==null&&o.push({plugin:a,options:aa(i.config,{plugin:a,local:e[l]},c,r)})}return o}function aa(i,{plugin:t,local:e},s,n){const o=i.pluginScopeKeys(t),r=i.getOptionScopes(s,o);return e&&t.defaults&&r.push(t.defaults),i.createResolver(r,n,[""],{scriptable:!1,indexable:!1,allKeys:!0})}function Ye(i,t){const e=z.datasets[i]||{};return((t.datasets||{})[i]||{}).indexAxis||t.indexAxis||e.indexAxis||"x"}function la(i,t){let e=i;return i==="_index_"?e=t:i==="_value_"&&(e=t==="x"?"y":"x"),e}function ca(i,t){return i===t?"_index_":"_value_"}function Zi(i){if(i==="x"||i==="y"||i==="r")return i}function ha(i){if(i==="top"||i==="bottom")return"x";if(i==="left"||i==="right")return"y"}function Xe(i,...t){if(Zi(i))return i;for(const e of t){const s=e.axis||ha(e.position)||i.length>1&&Zi(i[0].toLowerCase());if(s)return s}throw new Error(`Cannot determine type of '${i}' axis. Please provide 'axis' or 'position' option.`)}function Qi(i,t,e){if(e[t+"AxisID"]===i)return{axis:t}}function da(i,t){if(t.data&&t.data.datasets){const e=t.data.datasets.filter(s=>s.xAxisID===i||s.yAxisID===i);if(e.length)return Qi(i,"x",e[0])||Qi(i,"y",e[0])}return{}}function fa(i,t){const e=St[i.type]||{scales:{}},s=t.scales||{},n=Ye(i.type,t),o=Object.create(null);return Object.keys(s).forEach(r=>{const a=s[r];if(!C(a))return console.error(`Invalid scale configuration for scale: ${r}`);if(a._proxy)return console.warn(`Ignoring resolver passed as options for scale: ${r}`);const l=Xe(r,a,da(r,i),z.scales[a.type]),c=ca(l,n),h=e.scales||{};o[r]=Xt(Object.create(null),[{axis:l},a,h[l],h[c]])}),i.data.datasets.forEach(r=>{const a=r.type||i.type,l=r.indexAxis||Ye(a,t),h=(St[a]||{}).scales||{};Object.keys(h).forEach(d=>{const f=la(d,l),u=r[f+"AxisID"]||f;o[u]=o[u]||Object.create(null),Xt(o[u],[{axis:f},s[u],h[d]])})}),Object.keys(o).forEach(r=>{const a=o[r];Xt(a,[z.scales[a.type],z.scale])}),o}function tn(i){const t=i.options||(i.options={});t.plugins=D(t.plugins,{}),t.scales=fa(i,t)}function en(i){return i=i||{},i.datasets=i.datasets||[],i.labels=i.labels||[],i}function ua(i){return i=i||{},i.data=en(i.data),tn(i),i}const Ji=new Map,sn=new Set;function fe(i,t){let e=Ji.get(i);return e||(e=t(),Ji.set(i,e),sn.add(e)),e}const jt=(i,t,e)=>{const s=wt(t,e);s!==void 0&&i.add(s)};class ga{constructor(t){this._config=ua(t),this._scopeCache=new Map,this._resolverCache=new Map}get platform(){return this._config.platform}get type(){return this._config.type}set type(t){this._config.type=t}get data(){return this._config.data}set data(t){this._config.data=en(t)}get options(){return this._config.options}set options(t){this._config.options=t}get plugins(){return this._config.plugins}update(){const t=this._config;this.clearCache(),tn(t)}clearCache(){this._scopeCache.clear(),this._resolverCache.clear()}datasetScopeKeys(t){return fe(t,()=>[[`datasets.${t}`,""]])}datasetAnimationScopeKeys(t,e){return fe(`${t}.transition.${e}`,()=>[[`datasets.${t}.transitions.${e}`,`transitions.${e}`],[`datasets.${t}`,""]])}datasetElementScopeKeys(t,e){return fe(`${t}-${e}`,()=>[[`datasets.${t}.elements.${e}`,`datasets.${t}`,`elements.${e}`,""]])}pluginScopeKeys(t){const e=t.id,s=this.type;return fe(`${s}-plugin-${e}`,()=>[[`plugins.${e}`,...t.additionalOptionScopes||[]]])}_cachedScopes(t,e){const s=this._scopeCache;let n=s.get(t);return(!n||e)&&(n=new Map,s.set(t,n)),n}getOptionScopes(t,e,s){const{options:n,type:o}=this,r=this._cachedScopes(t,s),a=r.get(e);if(a)return a;const l=new Set;e.forEach(h=>{t&&(l.add(t),h.forEach(d=>jt(l,t,d))),h.forEach(d=>jt(l,n,d)),h.forEach(d=>jt(l,St[o]||{},d)),h.forEach(d=>jt(l,z,d)),h.forEach(d=>jt(l,$e,d))});const c=Array.from(l);return c.length===0&&c.push(Object.create(null)),sn.has(e)&&r.set(e,c),c}chartOptionScopes(){const{options:t,type:e}=this;return[t,St[e]||{},z.datasets[e]||{},{type:e},z,$e]}resolveNamedOptions(t,e,s,n=[""]){const o={$shared:!0},{resolver:r,subPrefixes:a}=ts(this._resolverCache,t,n);let l=r;if(ma(r,e)){o.$shared=!1,s=gt(s)?s():s;const c=this.createResolver(t,s,a);l=Lt(r,s,c)}for(const c of e)o[c]=l[c];return o}createResolver(t,e,s=[""],n){const{resolver:o}=ts(this._resolverCache,t,s);return C(e)?Lt(o,e,void 0,n):o}}function ts(i,t,e){let s=i.get(t);s||(s=new Map,i.set(t,s));const n=e.join();let o=s.get(n);return o||(o={resolver:si(t,e),subPrefixes:e.filter(a=>!a.toLowerCase().includes("hover"))},s.set(n,o)),o}const pa=i=>C(i)&&Object.getOwnPropertyNames(i).some(t=>gt(i[t]));function ma(i,t){const{isScriptable:e,isIndexable:s}=Ws(i);for(const n of t){const o=e(n),r=s(n),a=(r||o)&&i[n];if(o&&(gt(a)||pa(a))||r&&W(a))return!0}return!1}var ba="4.4.9";const _a=["top","bottom","left","right","chartArea"];function es(i,t){return i==="top"||i==="bottom"||_a.indexOf(i)===-1&&t==="x"}function is(i,t){return function(e,s){return e[i]===s[i]?e[t]-s[t]:e[i]-s[i]}}function ss(i){const t=i.chart,e=t.options.animation;t.notifyPlugins("afterRender"),E(e&&e.onComplete,[i],t)}function xa(i){const t=i.chart,e=t.options.animation;E(e&&e.onProgress,[i],t)}function nn(i){return ri()&&typeof i=="string"?i=document.getElementById(i):i&&i.length&&(i=i[0]),i&&i.canvas&&(i=i.canvas),i}const be={},ns=i=>{const t=nn(i);return Object.values(be).filter(e=>e.canvas===t).pop()};function ya(i,t,e){const s=Object.keys(i);for(const n of s){const o=+n;if(o>=t){const r=i[n];delete i[n],(e>0||o>t)&&(i[o+e]=r)}}}function va(i,t,e,s){return!e||i.type==="mouseout"?null:s?t:i}let hi=class{static defaults=z;static instances=be;static overrides=St;static registry=nt;static version=ba;static getChart=ns;static register(...t){nt.add(...t),os()}static unregister(...t){nt.remove(...t),os()}constructor(t,e){const s=this.config=new ga(e),n=nn(t),o=ns(n);if(o)throw new Error("Canvas is already in use. Chart with ID '"+o.id+"' must be destroyed before the canvas with ID '"+o.canvas.id+"' can be reused.");const r=s.createResolver(s.chartOptionScopes(),this.getContext());this.platform=new(s.platform||Hr(n)),this.platform.updateConfig(s);const a=this.platform.acquireContext(n,r.aspectRatio),l=a&&a.canvas,c=l&&l.height,h=l&&l.width;if(this.id=Fn(),this.ctx=a,this.canvas=l,this.width=h,this.height=c,this._options=r,this._aspectRatio=this.aspectRatio,this._layers=[],this._metasets=[],this._stacks=void 0,this.boxes=[],this.currentDevicePixelRatio=void 0,this.chartArea=void 0,this._active=[],this._lastEvent=void 0,this._listeners={},this._responsiveListeners=void 0,this._sortedMetasets=[],this.scales={},this._plugins=new sa,this.$proxies={},this._hiddenIndices={},this.attached=!1,this._animationsDisabled=void 0,this.$context=void 0,this._doResize=Jn(d=>this.update(d),r.resizeDelay||0),this._dataChanges=[],be[this.id]=this,!a||!l){console.error("Failed to create chart: can't acquire context from the given item");return}rt.listen(this,"complete",ss),rt.listen(this,"progress",xa),this._initialize(),this.attached&&this.update()}get aspectRatio(){const{options:{aspectRatio:t,maintainAspectRatio:e},width:s,height:n,_aspectRatio:o}=this;return L(t)?e&&o?o:n?s/n:null:t}get data(){return this.config.data}set data(t){this.config.data=t}get options(){return this._options}set options(t){this.config.options=t}get registry(){return nt}_initialize(){return this.notifyPlugins("beforeInit"),this.options.responsive?this.resize():Di(this,this.options.devicePixelRatio),this.bindEvents(),this.notifyPlugins("afterInit"),this}clear(){return Oi(this.canvas,this.ctx),this}stop(){return rt.stop(this),this}resize(t,e){rt.running(this)?this._resizeBeforeDraw={width:t,height:e}:this._resize(t,e)}_resize(t,e){const s=this.options,n=this.canvas,o=s.maintainAspectRatio&&this.aspectRatio,r=this.platform.getMaximumSize(n,t,e,o),a=s.devicePixelRatio||this.platform.getDevicePixelRatio(),l=this.width?"resize":"attach";this.width=r.width,this.height=r.height,this._aspectRatio=this.aspectRatio,Di(this,a,!0)&&(this.notifyPlugins("resize",{size:r}),E(s.onResize,[this,r],this),this.attached&&this._doResize(l)&&this.render())}ensureScalesHaveIDs(){const e=this.options.scales||{};T(e,(s,n)=>{s.id=n})}buildOrUpdateScales(){const t=this.options,e=t.scales,s=this.scales,n=Object.keys(s).reduce((r,a)=>(r[a]=!1,r),{});let o=[];e&&(o=o.concat(Object.keys(e).map(r=>{const a=e[r],l=Xe(r,a),c=l==="r",h=l==="x";return{options:a,dposition:c?"chartArea":h?"bottom":"left",dtype:c?"radialLinear":h?"category":"linear"}}))),T(o,r=>{const a=r.options,l=a.id,c=Xe(l,a),h=D(a.type,r.dtype);(a.position===void 0||es(a.position,c)!==es(r.dposition))&&(a.position=r.dposition),n[l]=!0;let d=null;if(l in s&&s[l].type===h)d=s[l];else{const f=nt.getScale(h);d=new f({id:l,type:h,ctx:this.ctx,chart:this}),s[d.id]=d}d.init(a,t)}),T(n,(r,a)=>{r||delete s[a]}),T(s,r=>{Z.configure(this,r,r.options),Z.addBox(this,r)})}_updateMetasets(){const t=this._metasets,e=this.data.datasets.length,s=t.length;if(t.sort((n,o)=>n.index-o.index),s>e){for(let n=e;n<s;++n)this._destroyDatasetMeta(n);t.splice(e,s-e)}this._sortedMetasets=t.slice(0).sort(is("order","index"))}_removeUnreferencedMetasets(){const{_metasets:t,data:{datasets:e}}=this;t.length>e.length&&delete this._stacks,t.forEach((s,n)=>{e.filter(o=>o===s._dataset).length===0&&this._destroyDatasetMeta(n)})}buildOrUpdateControllers(){const t=[],e=this.data.datasets;let s,n;for(this._removeUnreferencedMetasets(),s=0,n=e.length;s<n;s++){const o=e[s];let r=this.getDatasetMeta(s);const a=o.type||this.config.type;if(r.type&&r.type!==a&&(this._destroyDatasetMeta(s),r=this.getDatasetMeta(s)),r.type=a,r.indexAxis=o.indexAxis||Ye(a,this.options),r.order=o.order||0,r.index=s,r.label=""+o.label,r.visible=this.isDatasetVisible(s),r.controller)r.controller.updateIndex(s),r.controller.linkScales();else{const l=nt.getController(a),{datasetElementType:c,dataElementType:h}=z.datasets[a];Object.assign(l,{dataElementType:nt.getElement(h),datasetElementType:c&&nt.getElement(c)}),r.controller=new l(this,s),t.push(r.controller)}}return this._updateMetasets(),t}_resetElements(){T(this.data.datasets,(t,e)=>{this.getDatasetMeta(e).controller.reset()},this)}reset(){this._resetElements(),this.notifyPlugins("reset")}update(t){const e=this.config;e.update();const s=this._options=e.createResolver(e.chartOptionScopes(),this.getContext()),n=this._animationsDisabled=!s.animation;if(this._updateScales(),this._checkEventBindings(),this._updateHiddenIndices(),this._plugins.invalidate(),this.notifyPlugins("beforeUpdate",{mode:t,cancelable:!0})===!1)return;const o=this.buildOrUpdateControllers();this.notifyPlugins("beforeElementsUpdate");let r=0;for(let c=0,h=this.data.datasets.length;c<h;c++){const{controller:d}=this.getDatasetMeta(c),f=!n&&o.indexOf(d)===-1;d.buildOrUpdateElements(f),r=Math.max(+d.getMaxOverflow(),r)}r=this._minPadding=s.layout.autoPadding?r:0,this._updateLayout(r),n||T(o,c=>{c.reset()}),this._updateDatasets(t),this.notifyPlugins("afterUpdate",{mode:t}),this._layers.sort(is("z","_idx"));const{_active:a,_lastEvent:l}=this;l?this._eventHandler(l,!0):a.length&&this._updateHoverStyles(a,a,!0),this.render()}_updateScales(){T(this.scales,t=>{Z.removeBox(this,t)}),this.ensureScalesHaveIDs(),this.buildOrUpdateScales()}_checkEventBindings(){const t=this.options,e=new Set(Object.keys(this._listeners)),s=new Set(t.events);(!mi(e,s)||!!this._responsiveListeners!==t.responsive)&&(this.unbindEvents(),this.bindEvents())}_updateHiddenIndices(){const{_hiddenIndices:t}=this,e=this._getUniformDataChanges()||[];for(const{method:s,start:n,count:o}of e){const r=s==="_removeElements"?-o:o;ya(t,n,r)}}_getUniformDataChanges(){const t=this._dataChanges;if(!t||!t.length)return;this._dataChanges=[];const e=this.data.datasets.length,s=o=>new Set(t.filter(r=>r[0]===o).map((r,a)=>a+","+r.splice(1).join(","))),n=s(0);for(let o=1;o<e;o++)if(!mi(n,s(o)))return;return Array.from(n).map(o=>o.split(",")).map(o=>({method:o[1],start:+o[2],count:+o[3]}))}_updateLayout(t){if(this.notifyPlugins("beforeLayout",{cancelable:!0})===!1)return;Z.update(this,this.width,this.height,t);const e=this.chartArea,s=e.width<=0||e.height<=0;this._layers=[],T(this.boxes,n=>{s&&n.position==="chartArea"||(n.configure&&n.configure(),this._layers.push(...n._layers()))},this),this._layers.forEach((n,o)=>{n._idx=o}),this.notifyPlugins("afterLayout")}_updateDatasets(t){if(this.notifyPlugins("beforeDatasetsUpdate",{mode:t,cancelable:!0})!==!1){for(let e=0,s=this.data.datasets.length;e<s;++e)this.getDatasetMeta(e).controller.configure();for(let e=0,s=this.data.datasets.length;e<s;++e)this._updateDataset(e,gt(t)?t({datasetIndex:e}):t);this.notifyPlugins("afterDatasetsUpdate",{mode:t})}}_updateDataset(t,e){const s=this.getDatasetMeta(t),n={meta:s,index:t,mode:e,cancelable:!0};this.notifyPlugins("beforeDatasetUpdate",n)!==!1&&(s.controller._update(e),n.cancelable=!1,this.notifyPlugins("afterDatasetUpdate",n))}render(){this.notifyPlugins("beforeRender",{cancelable:!0})!==!1&&(rt.has(this)?this.attached&&!rt.running(this)&&rt.start(this):(this.draw(),ss({chart:this})))}draw(){let t;if(this._resizeBeforeDraw){const{width:s,height:n}=this._resizeBeforeDraw;this._resizeBeforeDraw=null,this._resize(s,n)}if(this.clear(),this.width<=0||this.height<=0||this.notifyPlugins("beforeDraw",{cancelable:!0})===!1)return;const e=this._layers;for(t=0;t<e.length&&e[t].z<=0;++t)e[t].draw(this.chartArea);for(this._drawDatasets();t<e.length;++t)e[t].draw(this.chartArea);this.notifyPlugins("afterDraw")}_getSortedDatasetMetas(t){const e=this._sortedMetasets,s=[];let n,o;for(n=0,o=e.length;n<o;++n){const r=e[n];(!t||r.visible)&&s.push(r)}return s}getSortedVisibleDatasetMetas(){return this._getSortedDatasetMetas(!0)}_drawDatasets(){if(this.notifyPlugins("beforeDatasetsDraw",{cancelable:!0})===!1)return;const t=this.getSortedVisibleDatasetMetas();for(let e=t.length-1;e>=0;--e)this._drawDataset(t[e]);this.notifyPlugins("afterDatasetsDraw")}_drawDataset(t){const e=this.ctx,s={meta:t,index:t.index,cancelable:!0},n=Ho(this,t);this.notifyPlugins("beforeDatasetDraw",s)!==!1&&(n&&ti(e,n),t.controller.draw(),n&&ei(e),s.cancelable=!1,this.notifyPlugins("afterDatasetDraw",s))}isPointInArea(t){return Bs(t,this.chartArea,this._minPadding)}getElementsAtEventForMode(t,e,s,n){const o=_r.modes[e];return typeof o=="function"?o(this,t,s,n):[]}getDatasetMeta(t){const e=this.data.datasets[t],s=this._metasets;let n=s.filter(o=>o&&o._dataset===e).pop();return n||(n={type:null,data:[],dataset:null,controller:null,hidden:null,xAxisID:null,yAxisID:null,order:e&&e.order||0,index:t,_dataset:e,_parsed:[],_sorted:!1},s.push(n)),n}getContext(){return this.$context||(this.$context=Et(null,{chart:this,type:"chart"}))}getVisibleDatasetCount(){return this.getSortedVisibleDatasetMetas().length}isDatasetVisible(t){const e=this.data.datasets[t];if(!e)return!1;const s=this.getDatasetMeta(t);return typeof s.hidden=="boolean"?!s.hidden:!e.hidden}setDatasetVisibility(t,e){const s=this.getDatasetMeta(t);s.hidden=!e}toggleDataVisibility(t){this._hiddenIndices[t]=!this._hiddenIndices[t]}getDataVisibility(t){return!this._hiddenIndices[t]}_updateVisibility(t,e,s){const n=s?"show":"hide",o=this.getDatasetMeta(t),r=o.controller._resolveAnimations(void 0,n);Zt(e)?(o.data[e].hidden=!s,this.update()):(this.setDatasetVisibility(t,s),r.update(o,{visible:s}),this.update(a=>a.datasetIndex===t?n:void 0))}hide(t,e){this._updateVisibility(t,e,!1)}show(t,e){this._updateVisibility(t,e,!0)}_destroyDatasetMeta(t){const e=this._metasets[t];e&&e.controller&&e.controller._destroy(),delete this._metasets[t]}_stop(){let t,e;for(this.stop(),rt.remove(this),t=0,e=this.data.datasets.length;t<e;++t)this._destroyDatasetMeta(t)}destroy(){this.notifyPlugins("beforeDestroy");const{canvas:t,ctx:e}=this;this._stop(),this.config.clearCache(),t&&(this.unbindEvents(),Oi(t,e),this.platform.releaseContext(e),this.canvas=null,this.ctx=null),delete be[this.id],this.notifyPlugins("afterDestroy")}toBase64Image(...t){return this.canvas.toDataURL(...t)}bindEvents(){this.bindUserEvents(),this.options.responsive?this.bindResponsiveEvents():this.attached=!0}bindUserEvents(){const t=this._listeners,e=this.platform,s=(o,r)=>{e.addEventListener(this,o,r),t[o]=r},n=(o,r,a)=>{o.offsetX=r,o.offsetY=a,this._eventHandler(o)};T(this.options.events,o=>s(o,n))}bindResponsiveEvents(){this._responsiveListeners||(this._responsiveListeners={});const t=this._responsiveListeners,e=this.platform,s=(l,c)=>{e.addEventListener(this,l,c),t[l]=c},n=(l,c)=>{t[l]&&(e.removeEventListener(this,l,c),delete t[l])},o=(l,c)=>{this.canvas&&this.resize(l,c)};let r;const a=()=>{n("attach",a),this.attached=!0,this.resize(),s("resize",o),s("detach",r)};r=()=>{this.attached=!1,n("resize",o),this._stop(),this._resize(0,0),s("attach",a)},e.isAttached(this.canvas)?a():r()}unbindEvents(){T(this._listeners,(t,e)=>{this.platform.removeEventListener(this,e,t)}),this._listeners={},T(this._responsiveListeners,(t,e)=>{this.platform.removeEventListener(this,e,t)}),this._responsiveListeners=void 0}updateHoverStyle(t,e,s){const n=s?"set":"remove";let o,r,a,l;for(e==="dataset"&&(o=this.getDatasetMeta(t[0].datasetIndex),o.controller["_"+n+"DatasetHoverStyle"]()),a=0,l=t.length;a<l;++a){r=t[a];const c=r&&this.getDatasetMeta(r.datasetIndex).controller;c&&c[n+"HoverStyle"](r.element,r.datasetIndex,r.index)}}getActiveElements(){return this._active||[]}setActiveElements(t){const e=this._active||[],s=t.map(({datasetIndex:o,index:r})=>{const a=this.getDatasetMeta(o);if(!a)throw new Error("No dataset found at index "+o);return{datasetIndex:o,element:a.data[r],index:r}});!_e(s,e)&&(this._active=s,this._lastEvent=null,this._updateHoverStyles(s,e))}notifyPlugins(t,e,s){return this._plugins.notify(this,t,e,s)}isPluginEnabled(t){return this._plugins._cache.filter(e=>e.plugin.id===t).length===1}_updateHoverStyles(t,e,s){const n=this.options.hover,o=(l,c)=>l.filter(h=>!c.some(d=>h.datasetIndex===d.datasetIndex&&h.index===d.index)),r=o(e,t),a=s?t:o(t,e);r.length&&this.updateHoverStyle(r,n.mode,!1),a.length&&n.mode&&this.updateHoverStyle(a,n.mode,!0)}_eventHandler(t,e){const s={event:t,replay:e,cancelable:!0,inChartArea:this.isPointInArea(t)},n=r=>(r.options.events||this.options.events).includes(t.native.type);if(this.notifyPlugins("beforeEvent",s,n)===!1)return;const o=this._handleEvent(t,e,s.inChartArea);return s.cancelable=!1,this.notifyPlugins("afterEvent",s,n),(o||s.changed)&&this.render(),this}_handleEvent(t,e,s){const{_active:n=[],options:o}=this,r=e,a=this._getActiveElements(t,n,s,r),l=Vn(t),c=va(t,this._lastEvent,s,l);s&&(this._lastEvent=null,E(o.onHover,[t,a,this],this),l&&E(o.onClick,[t,a,this],this));const h=!_e(a,n);return(h||e)&&(this._active=a,this._updateHoverStyles(a,n,e)),this._lastEvent=c,h}_getActiveElements(t,e,s,n){if(t.type==="mouseout")return[];if(!s)return e;const o=this.options.hover;return this.getElementsAtEventForMode(t,o.mode,o,n)}};function os(){return T(hi.instances,i=>i._plugins.invalidate())}function Ma(i,t,e){const{startAngle:s,pixelMargin:n,x:o,y:r,outerRadius:a,innerRadius:l}=t;let c=n/a;i.beginPath(),i.arc(o,r,a,s-c,e+c),l>n?(c=n/l,i.arc(o,r,l,e+c,s-c,!0)):i.arc(o,r,n,e+V,s-V),i.closePath(),i.clip()}function ka(i){return ii(i,["outerStart","outerEnd","innerStart","innerEnd"])}function wa(i,t,e,s){const n=ka(i.options.borderRadius),o=(e-t)/2,r=Math.min(o,s*t/2),a=l=>{const c=(e-Math.min(o,l))*s/2;return K(l,0,Math.min(o,c))};return{outerStart:a(n.outerStart),outerEnd:a(n.outerEnd),innerStart:K(n.innerStart,0,r),innerEnd:K(n.innerEnd,0,r)}}function Dt(i,t,e,s){return{x:e+i*Math.cos(t),y:s+i*Math.sin(t)}}function Oe(i,t,e,s,n,o){const{x:r,y:a,startAngle:l,pixelMargin:c,innerRadius:h}=t,d=Math.max(t.outerRadius+s+e-c,0),f=h>0?h+s+e+c:0;let u=0;const g=n-l;if(s){const R=h>0?h-s:0,F=d>0?d-s:0,I=(R+F)/2,tt=I!==0?g*I/(I+s):g;u=(g-tt)/2}const p=Math.max(.001,g*d-e/H)/d,m=(g-p)/2,b=l+m+u,_=n-m-u,{outerStart:y,outerEnd:v,innerStart:x,innerEnd:M}=wa(t,f,d,_-b),k=d-y,w=d-v,S=b+y/k,P=_-v/w,O=f+x,A=f+M,j=b+x/O,q=_-M/A;if(i.beginPath(),o){const R=(S+P)/2;if(i.arc(r,a,d,S,R),i.arc(r,a,d,R,P),v>0){const N=Dt(w,P,r,a);i.arc(N.x,N.y,v,P,_+V)}const F=Dt(A,_,r,a);if(i.lineTo(F.x,F.y),M>0){const N=Dt(A,q,r,a);i.arc(N.x,N.y,M,_+V,q+Math.PI)}const I=(_-M/f+(b+x/f))/2;if(i.arc(r,a,f,_-M/f,I,!0),i.arc(r,a,f,I,b+x/f,!0),x>0){const N=Dt(O,j,r,a);i.arc(N.x,N.y,x,j+Math.PI,b-V)}const tt=Dt(k,b,r,a);if(i.lineTo(tt.x,tt.y),y>0){const N=Dt(k,S,r,a);i.arc(N.x,N.y,y,b-V,S)}}else{i.moveTo(r,a);const R=Math.cos(S)*d+r,F=Math.sin(S)*d+a;i.lineTo(R,F);const I=Math.cos(P)*d+r,tt=Math.sin(P)*d+a;i.lineTo(I,tt)}i.closePath()}function Sa(i,t,e,s,n){const{fullCircles:o,startAngle:r,circumference:a}=t;let l=t.endAngle;if(o){Oe(i,t,e,s,l,n);for(let c=0;c<o;++c)i.fill();isNaN(a)||(l=r+(a%B||B))}return Oe(i,t,e,s,l,n),i.fill(),l}function Oa(i,t,e,s,n){const{fullCircles:o,startAngle:r,circumference:a,options:l}=t,{borderWidth:c,borderJoinStyle:h,borderDash:d,borderDashOffset:f}=l,u=l.borderAlign==="inner";if(!c)return;i.setLineDash(d||[]),i.lineDashOffset=f,u?(i.lineWidth=c*2,i.lineJoin=h||"round"):(i.lineWidth=c,i.lineJoin=h||"bevel");let g=t.endAngle;if(o){Oe(i,t,e,s,g,n);for(let p=0;p<o;++p)i.stroke();isNaN(a)||(g=r+(a%B||B))}u&&Ma(i,t,g),o||(Oe(i,t,e,s,g,n),i.stroke())}class ol extends Ot{static id="arc";static defaults={borderAlign:"center",borderColor:"#fff",borderDash:[],borderDashOffset:0,borderJoinStyle:void 0,borderRadius:0,borderWidth:2,offset:0,spacing:0,angle:void 0,circular:!0};static defaultRoutes={backgroundColor:"backgroundColor"};static descriptors={_scriptable:!0,_indexable:t=>t!=="borderDash"};circumference;endAngle;fullCircles;innerRadius;outerRadius;pixelMargin;startAngle;constructor(t){super(),this.options=void 0,this.circumference=void 0,this.startAngle=void 0,this.endAngle=void 0,this.innerRadius=void 0,this.outerRadius=void 0,this.pixelMargin=0,this.fullCircles=0,t&&Object.assign(this,t)}inRange(t,e,s){const n=this.getProps(["x","y"],s),{angle:o,distance:r}=Ds(n,{x:t,y:e}),{startAngle:a,endAngle:l,innerRadius:c,outerRadius:h,circumference:d}=this.getProps(["startAngle","endAngle","innerRadius","outerRadius","circumference"],s),f=(this.options.spacing+this.options.borderWidth)/2,u=D(d,l-a),g=Me(o,a,l)&&a!==l,p=u>=B||g,m=Mt(r,c+f,h+f);return p&&m}getCenterPoint(t){const{x:e,y:s,startAngle:n,endAngle:o,innerRadius:r,outerRadius:a}=this.getProps(["x","y","startAngle","endAngle","innerRadius","outerRadius"],t),{offset:l,spacing:c}=this.options,h=(n+o)/2,d=(r+a+c+l)/2;return{x:e+Math.cos(h)*d,y:s+Math.sin(h)*d}}tooltipPosition(t){return this.getCenterPoint(t)}draw(t){const{options:e,circumference:s}=this,n=(e.offset||0)/4,o=(e.spacing||0)/2,r=e.circular;if(this.pixelMargin=e.borderAlign==="inner"?.33:0,this.fullCircles=s>B?Math.floor(s/B):0,s===0||this.innerRadius<0||this.outerRadius<0)return;t.save();const a=(this.startAngle+this.endAngle)/2;t.translate(Math.cos(a)*n,Math.sin(a)*n);const l=1-Math.sin(Math.min(H,s||0)),c=n*l;t.fillStyle=e.backgroundColor,t.strokeStyle=e.borderColor,Sa(t,this,c,o,r),Oa(t,this,c,o,r),t.restore()}}function on(i,t){const{x:e,y:s,base:n,width:o,height:r}=i.getProps(["x","y","base","width","height"],t);let a,l,c,h,d;return i.horizontal?(d=r/2,a=Math.min(e,n),l=Math.max(e,n),c=s-d,h=s+d):(d=o/2,a=e-d,l=e+d,c=Math.min(s,n),h=Math.max(s,n)),{left:a,top:c,right:l,bottom:h}}function dt(i,t,e,s){return i?0:K(t,e,s)}function Pa(i,t,e){const s=i.options.borderWidth,n=i.borderSkipped,o=Hs(s);return{t:dt(n.top,o.top,0,e),r:dt(n.right,o.right,0,t),b:dt(n.bottom,o.bottom,0,e),l:dt(n.left,o.left,0,t)}}function Aa(i,t,e){const{enableBorderRadius:s}=i.getProps(["enableBorderRadius"]),n=i.options.borderRadius,o=Tt(n),r=Math.min(t,e),a=i.borderSkipped,l=s||C(n);return{topLeft:dt(!l||a.top||a.left,o.topLeft,0,r),topRight:dt(!l||a.top||a.right,o.topRight,0,r),bottomLeft:dt(!l||a.bottom||a.left,o.bottomLeft,0,r),bottomRight:dt(!l||a.bottom||a.right,o.bottomRight,0,r)}}function Ca(i){const t=on(i),e=t.right-t.left,s=t.bottom-t.top,n=Pa(i,e/2,s/2),o=Aa(i,e/2,s/2);return{outer:{x:t.left,y:t.top,w:e,h:s,radius:o},inner:{x:t.left+n.l,y:t.top+n.t,w:e-n.l-n.r,h:s-n.t-n.b,radius:{topLeft:Math.max(0,o.topLeft-Math.max(n.t,n.l)),topRight:Math.max(0,o.topRight-Math.max(n.t,n.r)),bottomLeft:Math.max(0,o.bottomLeft-Math.max(n.b,n.l)),bottomRight:Math.max(0,o.bottomRight-Math.max(n.b,n.r))}}}}function We(i,t,e,s){const n=t===null,o=e===null,a=i&&!(n&&o)&&on(i,s);return a&&(n||Mt(t,a.left,a.right))&&(o||Mt(e,a.top,a.bottom))}function Da(i){return i.topLeft||i.topRight||i.bottomLeft||i.bottomRight}function Ta(i,t){i.rect(t.x,t.y,t.w,t.h)}function Ve(i,t,e={}){const s=i.x!==e.x?-t:0,n=i.y!==e.y?-t:0,o=(i.x+i.w!==e.x+e.w?t:0)-s,r=(i.y+i.h!==e.y+e.h?t:0)-n;return{x:i.x+s,y:i.y+n,w:i.w+o,h:i.h+r,radius:i.radius}}class rl extends Ot{static id="bar";static defaults={borderSkipped:"start",borderWidth:0,borderRadius:0,inflateAmount:"auto",pointStyle:void 0};static defaultRoutes={backgroundColor:"backgroundColor",borderColor:"borderColor"};constructor(t){super(),this.options=void 0,this.horizontal=void 0,this.base=void 0,this.width=void 0,this.height=void 0,this.inflateAmount=void 0,t&&Object.assign(this,t)}draw(t){const{inflateAmount:e,options:{borderColor:s,backgroundColor:n}}=this,{inner:o,outer:r}=Ca(this),a=Da(r.radius)?ke:Ta;t.save(),(r.w!==o.w||r.h!==o.h)&&(t.beginPath(),a(t,Ve(r,e,o)),t.clip(),a(t,Ve(o,-e,r)),t.fillStyle=s,t.fill("evenodd")),t.beginPath(),a(t,Ve(o,e)),t.fillStyle=n,t.fill(),t.restore()}inRange(t,e,s){return We(this,t,e,s)}inXRange(t,e){return We(this,t,null,e)}inYRange(t,e){return We(this,null,t,e)}getCenterPoint(t){const{x:e,y:s,base:n,horizontal:o}=this.getProps(["x","y","base","horizontal"],t);return{x:o?(e+n)/2:e,y:o?s:(s+n)/2}}getRange(t){return t==="x"?this.width/2:this.height/2}}const rs=(i,t)=>{let{boxHeight:e=t,boxWidth:s=t}=i;return i.usePointStyle&&(e=Math.min(e,t),s=i.pointStyleWidth||Math.min(s,t)),{boxWidth:s,boxHeight:e,itemHeight:Math.max(t,e)}},Ra=(i,t)=>i!==null&&t!==null&&i.datasetIndex===t.datasetIndex&&i.index===t.index;class as extends Ot{constructor(t){super(),this._added=!1,this.legendHitBoxes=[],this._hoveredItem=null,this.doughnutMode=!1,this.chart=t.chart,this.options=t.options,this.ctx=t.ctx,this.legendItems=void 0,this.columnSizes=void 0,this.lineWidths=void 0,this.maxHeight=void 0,this.maxWidth=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.height=void 0,this.width=void 0,this._margins=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(t,e,s){this.maxWidth=t,this.maxHeight=e,this._margins=s,this.setDimensions(),this.buildLabels(),this.fit()}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=this._margins.left,this.right=this.width):(this.height=this.maxHeight,this.top=this._margins.top,this.bottom=this.height)}buildLabels(){const t=this.options.labels||{};let e=E(t.generateLabels,[this.chart],this)||[];t.filter&&(e=e.filter(s=>t.filter(s,this.chart.data))),t.sort&&(e=e.sort((s,n)=>t.sort(s,n,this.chart.data))),this.options.reverse&&e.reverse(),this.legendItems=e}fit(){const{options:t,ctx:e}=this;if(!t.display){this.width=this.height=0;return}const s=t.labels,n=Y(s.font),o=n.size,r=this._computeTitleHeight(),{boxWidth:a,itemHeight:l}=rs(s,o);let c,h;e.font=n.string,this.isHorizontal()?(c=this.maxWidth,h=this._fitRows(r,o,a,l)+10):(h=this.maxHeight,c=this._fitCols(r,n,a,l)+10),this.width=Math.min(c,t.maxWidth||this.maxWidth),this.height=Math.min(h,t.maxHeight||this.maxHeight)}_fitRows(t,e,s,n){const{ctx:o,maxWidth:r,options:{labels:{padding:a}}}=this,l=this.legendHitBoxes=[],c=this.lineWidths=[0],h=n+a;let d=t;o.textAlign="left",o.textBaseline="middle";let f=-1,u=-h;return this.legendItems.forEach((g,p)=>{const m=s+e/2+o.measureText(g.text).width;(p===0||c[c.length-1]+m+2*a>r)&&(d+=h,c[c.length-(p>0?0:1)]=0,u+=h,f++),l[p]={left:0,top:u,row:f,width:m,height:n},c[c.length-1]+=m+a}),d}_fitCols(t,e,s,n){const{ctx:o,maxHeight:r,options:{labels:{padding:a}}}=this,l=this.legendHitBoxes=[],c=this.columnSizes=[],h=r-t;let d=a,f=0,u=0,g=0,p=0;return this.legendItems.forEach((m,b)=>{const{itemWidth:_,itemHeight:y}=La(s,e,o,m,n);b>0&&u+y+2*a>h&&(d+=f+a,c.push({width:f,height:u}),g+=f+a,p++,f=u=0),l[b]={left:g,top:u,col:p,width:_,height:y},f=Math.max(f,_),u+=y+a}),d+=f,c.push({width:f,height:u}),d}adjustHitBoxes(){if(!this.options.display)return;const t=this._computeTitleHeight(),{legendHitBoxes:e,options:{align:s,labels:{padding:n},rtl:o}}=this,r=Rt(o,this.left,this.width);if(this.isHorizontal()){let a=0,l=$(s,this.left+n,this.right-this.lineWidths[a]);for(const c of e)a!==c.row&&(a=c.row,l=$(s,this.left+n,this.right-this.lineWidths[a])),c.top+=this.top+t+n,c.left=r.leftForLtr(r.x(l),c.width),l+=c.width+n}else{let a=0,l=$(s,this.top+t+n,this.bottom-this.columnSizes[a].height);for(const c of e)c.col!==a&&(a=c.col,l=$(s,this.top+t+n,this.bottom-this.columnSizes[a].height)),c.top=l,c.left+=this.left+n,c.left=r.leftForLtr(r.x(c.left),c.width),l+=c.height+n}}isHorizontal(){return this.options.position==="top"||this.options.position==="bottom"}draw(){if(this.options.display){const t=this.ctx;ti(t,this),this._draw(),ei(t)}}_draw(){const{options:t,columnSizes:e,lineWidths:s,ctx:n}=this,{align:o,labels:r}=t,a=z.color,l=Rt(t.rtl,this.left,this.width),c=Y(r.font),{padding:h}=r,d=c.size,f=d/2;let u;this.drawTitle(),n.textAlign=l.textAlign("left"),n.textBaseline="middle",n.lineWidth=.5,n.font=c.string;const{boxWidth:g,boxHeight:p,itemHeight:m}=rs(r,d),b=function(M,k,w){if(isNaN(g)||g<=0||isNaN(p)||p<0)return;n.save();const S=D(w.lineWidth,1);if(n.fillStyle=D(w.fillStyle,a),n.lineCap=D(w.lineCap,"butt"),n.lineDashOffset=D(w.lineDashOffset,0),n.lineJoin=D(w.lineJoin,"miter"),n.lineWidth=S,n.strokeStyle=D(w.strokeStyle,a),n.setLineDash(D(w.lineDash,[])),r.usePointStyle){const P={radius:p*Math.SQRT2/2,pointStyle:w.pointStyle,rotation:w.rotation,borderWidth:S},O=l.xPlus(M,g/2),A=k+f;zs(n,P,O,A,r.pointStyleWidth&&g)}else{const P=k+Math.max((d-p)/2,0),O=l.leftForLtr(M,g),A=Tt(w.borderRadius);n.beginPath(),Object.values(A).some(j=>j!==0)?ke(n,{x:O,y:P,w:g,h:p,radius:A}):n.rect(O,P,g,p),n.fill(),S!==0&&n.stroke()}n.restore()},_=function(M,k,w){Qt(n,w.text,M,k+m/2,c,{strikethrough:w.hidden,textAlign:l.textAlign(w.textAlign)})},y=this.isHorizontal(),v=this._computeTitleHeight();y?u={x:$(o,this.left+h,this.right-s[0]),y:this.top+h+v,line:0}:u={x:this.left+h,y:$(o,this.top+v+h,this.bottom-e[0].height),line:0},$s(this.ctx,t.textDirection);const x=m+h;this.legendItems.forEach((M,k)=>{n.strokeStyle=M.fontColor,n.fillStyle=M.fontColor;const w=n.measureText(M.text).width,S=l.textAlign(M.textAlign||(M.textAlign=r.textAlign)),P=g+f+w;let O=u.x,A=u.y;l.setWidth(this.width),y?k>0&&O+P+h>this.right&&(A=u.y+=x,u.line++,O=u.x=$(o,this.left+h,this.right-s[u.line])):k>0&&A+x>this.bottom&&(O=u.x=O+e[u.line].width+h,u.line++,A=u.y=$(o,this.top+v+h,this.bottom-e[u.line].height));const j=l.x(O);if(b(j,A,M),O=to(S,O+g+f,y?O+P:this.right,t.rtl),_(l.x(O),A,M),y)u.x+=P+h;else if(typeof M.text!="string"){const q=c.lineHeight;u.y+=rn(M,q)+h}else u.y+=x}),Ys(this.ctx,t.textDirection)}drawTitle(){const t=this.options,e=t.title,s=Y(e.font),n=J(e.padding);if(!e.display)return;const o=Rt(t.rtl,this.left,this.width),r=this.ctx,a=e.position,l=s.size/2,c=n.top+l;let h,d=this.left,f=this.width;if(this.isHorizontal())f=Math.max(...this.lineWidths),h=this.top+c,d=$(t.align,d,this.right-f);else{const g=this.columnSizes.reduce((p,m)=>Math.max(p,m.height),0);h=c+$(t.align,this.top,this.bottom-g-t.labels.padding-this._computeTitleHeight())}const u=$(a,d,d+f);r.textAlign=o.textAlign(Qe(a)),r.textBaseline="middle",r.strokeStyle=e.color,r.fillStyle=e.color,r.font=s.string,Qt(r,e.text,u,h,s)}_computeTitleHeight(){const t=this.options.title,e=Y(t.font),s=J(t.padding);return t.display?e.lineHeight+s.height:0}_getLegendItemAt(t,e){let s,n,o;if(Mt(t,this.left,this.right)&&Mt(e,this.top,this.bottom)){for(o=this.legendHitBoxes,s=0;s<o.length;++s)if(n=o[s],Mt(t,n.left,n.left+n.width)&&Mt(e,n.top,n.top+n.height))return this.legendItems[s]}return null}handleEvent(t){const e=this.options;if(!Ia(t.type,e))return;const s=this._getLegendItemAt(t.x,t.y);if(t.type==="mousemove"||t.type==="mouseout"){const n=this._hoveredItem,o=Ra(n,s);n&&!o&&E(e.onLeave,[t,n,this],this),this._hoveredItem=s,s&&!o&&E(e.onHover,[t,s,this],this)}else s&&E(e.onClick,[t,s,this],this)}}function La(i,t,e,s,n){const o=Ea(s,i,t,e),r=Fa(n,s,t.lineHeight);return{itemWidth:o,itemHeight:r}}function Ea(i,t,e,s){let n=i.text;return n&&typeof n!="string"&&(n=n.reduce((o,r)=>o.length>r.length?o:r)),t+e.size/2+s.measureText(n).width}function Fa(i,t,e){let s=i;return typeof t.text!="string"&&(s=rn(t,e)),s}function rn(i,t){const e=i.text?i.text.length:0;return t*e}function Ia(i,t){return!!((i==="mousemove"||i==="mouseout")&&(t.onHover||t.onLeave)||t.onClick&&(i==="click"||i==="mouseup"))}var al={id:"legend",_element:as,start(i,t,e){const s=i.legend=new as({ctx:i.ctx,options:e,chart:i});Z.configure(i,s,e),Z.addBox(i,s)},stop(i){Z.removeBox(i,i.legend),delete i.legend},beforeUpdate(i,t,e){const s=i.legend;Z.configure(i,s,e),s.options=e},afterUpdate(i){const t=i.legend;t.buildLabels(),t.adjustHitBoxes()},afterEvent(i,t){t.replay||i.legend.handleEvent(t.event)},defaults:{display:!0,position:"top",align:"center",fullSize:!0,reverse:!1,weight:1e3,onClick(i,t,e){const s=t.datasetIndex,n=e.chart;n.isDatasetVisible(s)?(n.hide(s),t.hidden=!0):(n.show(s),t.hidden=!1)},onHover:null,onLeave:null,labels:{color:i=>i.chart.options.color,boxWidth:40,padding:10,generateLabels(i){const t=i.data.datasets,{labels:{usePointStyle:e,pointStyle:s,textAlign:n,color:o,useBorderRadius:r,borderRadius:a}}=i.legend.options;return i._getSortedDatasetMetas().map(l=>{const c=l.controller.getStyle(e?0:void 0),h=J(c.borderWidth);return{text:t[l.index].label,fillStyle:c.backgroundColor,fontColor:o,hidden:!l.visible,lineCap:c.borderCapStyle,lineDash:c.borderDash,lineDashOffset:c.borderDashOffset,lineJoin:c.borderJoinStyle,lineWidth:(h.width+h.height)/4,strokeStyle:c.borderColor,pointStyle:s||c.pointStyle,rotation:c.rotation,textAlign:n||c.textAlign,borderRadius:r&&(a||c.borderRadius),datasetIndex:l.index}},this)}},title:{color:i=>i.chart.options.color,display:!1,position:"center",text:""}},descriptors:{_scriptable:i=>!i.startsWith("on"),labels:{_scriptable:i=>!["generateLabels","filter","sort"].includes(i)}}};class an extends Ot{constructor(t){super(),this.chart=t.chart,this.options=t.options,this.ctx=t.ctx,this._padding=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(t,e){const s=this.options;if(this.left=0,this.top=0,!s.display){this.width=this.height=this.right=this.bottom=0;return}this.width=this.right=t,this.height=this.bottom=e;const n=W(s.text)?s.text.length:1;this._padding=J(s.padding);const o=n*Y(s.font).lineHeight+this._padding.height;this.isHorizontal()?this.height=o:this.width=o}isHorizontal(){const t=this.options.position;return t==="top"||t==="bottom"}_drawArgs(t){const{top:e,left:s,bottom:n,right:o,options:r}=this,a=r.align;let l=0,c,h,d;return this.isHorizontal()?(h=$(a,s,o),d=e+t,c=o-s):(r.position==="left"?(h=s+t,d=$(a,n,e),l=H*-.5):(h=o-t,d=$(a,e,n),l=H*.5),c=n-e),{titleX:h,titleY:d,maxWidth:c,rotation:l}}draw(){const t=this.ctx,e=this.options;if(!e.display)return;const s=Y(e.font),o=s.lineHeight/2+this._padding.top,{titleX:r,titleY:a,maxWidth:l,rotation:c}=this._drawArgs(o);Qt(t,e.text,0,0,s,{color:e.color,maxWidth:l,rotation:c,textAlign:Qe(e.align),textBaseline:"middle",translation:[r,a]})}}function za(i,t){const e=new an({ctx:i.ctx,options:t,chart:i});Z.configure(i,e,t),Z.addBox(i,e),i.titleBlock=e}var ll={id:"title",_element:an,start(i,t,e){za(i,e)},stop(i){const t=i.titleBlock;Z.removeBox(i,t),delete i.titleBlock},beforeUpdate(i,t,e){const s=i.titleBlock;Z.configure(i,s,e),s.options=e},defaults:{align:"center",display:!1,font:{weight:"bold"},fullSize:!0,padding:10,position:"top",text:"",weight:2e3},defaultRoutes:{color:"color"},descriptors:{_scriptable:!0,_indexable:!1}};const Yt={average(i){if(!i.length)return!1;let t,e,s=new Set,n=0,o=0;for(t=0,e=i.length;t<e;++t){const a=i[t].element;if(a&&a.hasValue()){const l=a.tooltipPosition();s.add(l.x),n+=l.y,++o}}return o===0||s.size===0?!1:{x:[...s].reduce((a,l)=>a+l)/s.size,y:n/o}},nearest(i,t){if(!i.length)return!1;let e=t.x,s=t.y,n=Number.POSITIVE_INFINITY,o,r,a;for(o=0,r=i.length;o<r;++o){const l=i[o].element;if(l&&l.hasValue()){const c=l.getCenterPoint(),h=Kn(t,c);h<n&&(n=h,a=l)}}if(a){const l=a.tooltipPosition();e=l.x,s=l.y}return{x:e,y:s}}};function it(i,t){return t&&(W(t)?Array.prototype.push.apply(i,t):i.push(t)),i}function at(i){return(typeof i=="string"||i instanceof String)&&i.indexOf(`
`)>-1?i.split(`
`):i}function Ba(i,t){const{element:e,datasetIndex:s,index:n}=t,o=i.getDatasetMeta(s).controller,{label:r,value:a}=o.getLabelAndValue(n);return{chart:i,label:r,parsed:o.getParsed(n),raw:i.data.datasets[s].data[n],formattedValue:a,dataset:o.getDataset(),dataIndex:n,datasetIndex:s,element:e}}function ls(i,t){const e=i.chart.ctx,{body:s,footer:n,title:o}=i,{boxWidth:r,boxHeight:a}=t,l=Y(t.bodyFont),c=Y(t.titleFont),h=Y(t.footerFont),d=o.length,f=n.length,u=s.length,g=J(t.padding);let p=g.height,m=0,b=s.reduce((v,x)=>v+x.before.length+x.lines.length+x.after.length,0);if(b+=i.beforeBody.length+i.afterBody.length,d&&(p+=d*c.lineHeight+(d-1)*t.titleSpacing+t.titleMarginBottom),b){const v=t.displayColors?Math.max(a,l.lineHeight):l.lineHeight;p+=u*v+(b-u)*l.lineHeight+(b-1)*t.bodySpacing}f&&(p+=t.footerMarginTop+f*h.lineHeight+(f-1)*t.footerSpacing);let _=0;const y=function(v){m=Math.max(m,e.measureText(v).width+_)};return e.save(),e.font=c.string,T(i.title,y),e.font=l.string,T(i.beforeBody.concat(i.afterBody),y),_=t.displayColors?r+2+t.boxPadding:0,T(s,v=>{T(v.before,y),T(v.lines,y),T(v.after,y)}),_=0,e.font=h.string,T(i.footer,y),e.restore(),m+=g.width,{width:m,height:p}}function Ha(i,t){const{y:e,height:s}=t;return e<s/2?"top":e>i.height-s/2?"bottom":"center"}function Wa(i,t,e,s){const{x:n,width:o}=s,r=e.caretSize+e.caretPadding;if(i==="left"&&n+o+r>t.width||i==="right"&&n-o-r<0)return!0}function Va(i,t,e,s){const{x:n,width:o}=e,{width:r,chartArea:{left:a,right:l}}=i;let c="center";return s==="center"?c=n<=(a+l)/2?"left":"right":n<=o/2?c="left":n>=r-o/2&&(c="right"),Wa(c,i,t,e)&&(c="center"),c}function cs(i,t,e){const s=e.yAlign||t.yAlign||Ha(i,e);return{xAlign:e.xAlign||t.xAlign||Va(i,t,e,s),yAlign:s}}function ja(i,t){let{x:e,width:s}=i;return t==="right"?e-=s:t==="center"&&(e-=s/2),e}function Na(i,t,e){let{y:s,height:n}=i;return t==="top"?s+=e:t==="bottom"?s-=n+e:s-=n/2,s}function hs(i,t,e,s){const{caretSize:n,caretPadding:o,cornerRadius:r}=i,{xAlign:a,yAlign:l}=e,c=n+o,{topLeft:h,topRight:d,bottomLeft:f,bottomRight:u}=Tt(r);let g=ja(t,a);const p=Na(t,l,c);return l==="center"?a==="left"?g+=c:a==="right"&&(g-=c):a==="left"?g-=Math.max(h,f)+n:a==="right"&&(g+=Math.max(d,u)+n),{x:K(g,0,s.width-t.width),y:K(p,0,s.height-t.height)}}function ue(i,t,e){const s=J(e.padding);return t==="center"?i.x+i.width/2:t==="right"?i.x+i.width-s.right:i.x+s.left}function ds(i){return it([],at(i))}function $a(i,t,e){return Et(i,{tooltip:t,tooltipItems:e,type:"tooltip"})}function fs(i,t){const e=t&&t.dataset&&t.dataset.tooltip&&t.dataset.tooltip.callbacks;return e?i.override(e):i}const ln={beforeTitle:ot,title(i){if(i.length>0){const t=i[0],e=t.chart.data.labels,s=e?e.length:0;if(this&&this.options&&this.options.mode==="dataset")return t.dataset.label||"";if(t.label)return t.label;if(s>0&&t.dataIndex<s)return e[t.dataIndex]}return""},afterTitle:ot,beforeBody:ot,beforeLabel:ot,label(i){if(this&&this.options&&this.options.mode==="dataset")return i.label+": "+i.formattedValue||i.formattedValue;let t=i.dataset.label||"";t&&(t+=": ");const e=i.formattedValue;return L(e)||(t+=e),t},labelColor(i){const e=i.chart.getDatasetMeta(i.datasetIndex).controller.getStyle(i.dataIndex);return{borderColor:e.borderColor,backgroundColor:e.backgroundColor,borderWidth:e.borderWidth,borderDash:e.borderDash,borderDashOffset:e.borderDashOffset,borderRadius:0}},labelTextColor(){return this.options.bodyColor},labelPointStyle(i){const e=i.chart.getDatasetMeta(i.datasetIndex).controller.getStyle(i.dataIndex);return{pointStyle:e.pointStyle,rotation:e.rotation}},afterLabel:ot,afterBody:ot,beforeFooter:ot,footer:ot,afterFooter:ot};function X(i,t,e,s){const n=i[t].call(e,s);return typeof n>"u"?ln[t].call(e,s):n}class us extends Ot{static positioners=Yt;constructor(t){super(),this.opacity=0,this._active=[],this._eventPosition=void 0,this._size=void 0,this._cachedAnimations=void 0,this._tooltipItems=[],this.$animations=void 0,this.$context=void 0,this.chart=t.chart,this.options=t.options,this.dataPoints=void 0,this.title=void 0,this.beforeBody=void 0,this.body=void 0,this.afterBody=void 0,this.footer=void 0,this.xAlign=void 0,this.yAlign=void 0,this.x=void 0,this.y=void 0,this.height=void 0,this.width=void 0,this.caretX=void 0,this.caretY=void 0,this.labelColors=void 0,this.labelPointStyles=void 0,this.labelTextColors=void 0}initialize(t){this.options=t,this._cachedAnimations=void 0,this.$context=void 0}_resolveAnimations(){const t=this._cachedAnimations;if(t)return t;const e=this.chart,s=this.options.setContext(this.getContext()),n=s.enabled&&e.options.animation&&s.animations,o=new Xs(this.chart,n);return n._cacheable&&(this._cachedAnimations=Object.freeze(o)),o}getContext(){return this.$context||(this.$context=$a(this.chart.getContext(),this,this._tooltipItems))}getTitle(t,e){const{callbacks:s}=e,n=X(s,"beforeTitle",this,t),o=X(s,"title",this,t),r=X(s,"afterTitle",this,t);let a=[];return a=it(a,at(n)),a=it(a,at(o)),a=it(a,at(r)),a}getBeforeBody(t,e){return ds(X(e.callbacks,"beforeBody",this,t))}getBody(t,e){const{callbacks:s}=e,n=[];return T(t,o=>{const r={before:[],lines:[],after:[]},a=fs(s,o);it(r.before,at(X(a,"beforeLabel",this,o))),it(r.lines,X(a,"label",this,o)),it(r.after,at(X(a,"afterLabel",this,o))),n.push(r)}),n}getAfterBody(t,e){return ds(X(e.callbacks,"afterBody",this,t))}getFooter(t,e){const{callbacks:s}=e,n=X(s,"beforeFooter",this,t),o=X(s,"footer",this,t),r=X(s,"afterFooter",this,t);let a=[];return a=it(a,at(n)),a=it(a,at(o)),a=it(a,at(r)),a}_createItems(t){const e=this._active,s=this.chart.data,n=[],o=[],r=[];let a=[],l,c;for(l=0,c=e.length;l<c;++l)a.push(Ba(this.chart,e[l]));return t.filter&&(a=a.filter((h,d,f)=>t.filter(h,d,f,s))),t.itemSort&&(a=a.sort((h,d)=>t.itemSort(h,d,s))),T(a,h=>{const d=fs(t.callbacks,h);n.push(X(d,"labelColor",this,h)),o.push(X(d,"labelPointStyle",this,h)),r.push(X(d,"labelTextColor",this,h))}),this.labelColors=n,this.labelPointStyles=o,this.labelTextColors=r,this.dataPoints=a,a}update(t,e){const s=this.options.setContext(this.getContext()),n=this._active;let o,r=[];if(!n.length)this.opacity!==0&&(o={opacity:0});else{const a=Yt[s.position].call(this,n,this._eventPosition);r=this._createItems(s),this.title=this.getTitle(r,s),this.beforeBody=this.getBeforeBody(r,s),this.body=this.getBody(r,s),this.afterBody=this.getAfterBody(r,s),this.footer=this.getFooter(r,s);const l=this._size=ls(this,s),c=Object.assign({},a,l),h=cs(this.chart,s,c),d=hs(s,c,h,this.chart);this.xAlign=h.xAlign,this.yAlign=h.yAlign,o={opacity:1,x:d.x,y:d.y,width:l.width,height:l.height,caretX:a.x,caretY:a.y}}this._tooltipItems=r,this.$context=void 0,o&&this._resolveAnimations().update(this,o),t&&s.external&&s.external.call(this,{chart:this.chart,tooltip:this,replay:e})}drawCaret(t,e,s,n){const o=this.getCaretPosition(t,s,n);e.lineTo(o.x1,o.y1),e.lineTo(o.x2,o.y2),e.lineTo(o.x3,o.y3)}getCaretPosition(t,e,s){const{xAlign:n,yAlign:o}=this,{caretSize:r,cornerRadius:a}=s,{topLeft:l,topRight:c,bottomLeft:h,bottomRight:d}=Tt(a),{x:f,y:u}=t,{width:g,height:p}=e;let m,b,_,y,v,x;return o==="center"?(v=u+p/2,n==="left"?(m=f,b=m-r,y=v+r,x=v-r):(m=f+g,b=m+r,y=v-r,x=v+r),_=m):(n==="left"?b=f+Math.max(l,h)+r:n==="right"?b=f+g-Math.max(c,d)-r:b=this.caretX,o==="top"?(y=u,v=y-r,m=b-r,_=b+r):(y=u+p,v=y+r,m=b+r,_=b-r),x=y),{x1:m,x2:b,x3:_,y1:y,y2:v,y3:x}}drawTitle(t,e,s){const n=this.title,o=n.length;let r,a,l;if(o){const c=Rt(s.rtl,this.x,this.width);for(t.x=ue(this,s.titleAlign,s),e.textAlign=c.textAlign(s.titleAlign),e.textBaseline="middle",r=Y(s.titleFont),a=s.titleSpacing,e.fillStyle=s.titleColor,e.font=r.string,l=0;l<o;++l)e.fillText(n[l],c.x(t.x),t.y+r.lineHeight/2),t.y+=r.lineHeight+a,l+1===o&&(t.y+=s.titleMarginBottom-a)}}_drawColorBox(t,e,s,n,o){const r=this.labelColors[s],a=this.labelPointStyles[s],{boxHeight:l,boxWidth:c}=o,h=Y(o.bodyFont),d=ue(this,"left",o),f=n.x(d),u=l<h.lineHeight?(h.lineHeight-l)/2:0,g=e.y+u;if(o.usePointStyle){const p={radius:Math.min(c,l)/2,pointStyle:a.pointStyle,rotation:a.rotation,borderWidth:1},m=n.leftForLtr(f,c)+c/2,b=g+l/2;t.strokeStyle=o.multiKeyBackground,t.fillStyle=o.multiKeyBackground,Pi(t,p,m,b),t.strokeStyle=r.borderColor,t.fillStyle=r.backgroundColor,Pi(t,p,m,b)}else{t.lineWidth=C(r.borderWidth)?Math.max(...Object.values(r.borderWidth)):r.borderWidth||1,t.strokeStyle=r.borderColor,t.setLineDash(r.borderDash||[]),t.lineDashOffset=r.borderDashOffset||0;const p=n.leftForLtr(f,c),m=n.leftForLtr(n.xPlus(f,1),c-2),b=Tt(r.borderRadius);Object.values(b).some(_=>_!==0)?(t.beginPath(),t.fillStyle=o.multiKeyBackground,ke(t,{x:p,y:g,w:c,h:l,radius:b}),t.fill(),t.stroke(),t.fillStyle=r.backgroundColor,t.beginPath(),ke(t,{x:m,y:g+1,w:c-2,h:l-2,radius:b}),t.fill()):(t.fillStyle=o.multiKeyBackground,t.fillRect(p,g,c,l),t.strokeRect(p,g,c,l),t.fillStyle=r.backgroundColor,t.fillRect(m,g+1,c-2,l-2))}t.fillStyle=this.labelTextColors[s]}drawBody(t,e,s){const{body:n}=this,{bodySpacing:o,bodyAlign:r,displayColors:a,boxHeight:l,boxWidth:c,boxPadding:h}=s,d=Y(s.bodyFont);let f=d.lineHeight,u=0;const g=Rt(s.rtl,this.x,this.width),p=function(w){e.fillText(w,g.x(t.x+u),t.y+f/2),t.y+=f+o},m=g.textAlign(r);let b,_,y,v,x,M,k;for(e.textAlign=r,e.textBaseline="middle",e.font=d.string,t.x=ue(this,m,s),e.fillStyle=s.bodyColor,T(this.beforeBody,p),u=a&&m!=="right"?r==="center"?c/2+h:c+2+h:0,v=0,M=n.length;v<M;++v){for(b=n[v],_=this.labelTextColors[v],e.fillStyle=_,T(b.before,p),y=b.lines,a&&y.length&&(this._drawColorBox(e,t,v,g,s),f=Math.max(d.lineHeight,l)),x=0,k=y.length;x<k;++x)p(y[x]),f=d.lineHeight;T(b.after,p)}u=0,f=d.lineHeight,T(this.afterBody,p),t.y-=o}drawFooter(t,e,s){const n=this.footer,o=n.length;let r,a;if(o){const l=Rt(s.rtl,this.x,this.width);for(t.x=ue(this,s.footerAlign,s),t.y+=s.footerMarginTop,e.textAlign=l.textAlign(s.footerAlign),e.textBaseline="middle",r=Y(s.footerFont),e.fillStyle=s.footerColor,e.font=r.string,a=0;a<o;++a)e.fillText(n[a],l.x(t.x),t.y+r.lineHeight/2),t.y+=r.lineHeight+s.footerSpacing}}drawBackground(t,e,s,n){const{xAlign:o,yAlign:r}=this,{x:a,y:l}=t,{width:c,height:h}=s,{topLeft:d,topRight:f,bottomLeft:u,bottomRight:g}=Tt(n.cornerRadius);e.fillStyle=n.backgroundColor,e.strokeStyle=n.borderColor,e.lineWidth=n.borderWidth,e.beginPath(),e.moveTo(a+d,l),r==="top"&&this.drawCaret(t,e,s,n),e.lineTo(a+c-f,l),e.quadraticCurveTo(a+c,l,a+c,l+f),r==="center"&&o==="right"&&this.drawCaret(t,e,s,n),e.lineTo(a+c,l+h-g),e.quadraticCurveTo(a+c,l+h,a+c-g,l+h),r==="bottom"&&this.drawCaret(t,e,s,n),e.lineTo(a+u,l+h),e.quadraticCurveTo(a,l+h,a,l+h-u),r==="center"&&o==="left"&&this.drawCaret(t,e,s,n),e.lineTo(a,l+d),e.quadraticCurveTo(a,l,a+d,l),e.closePath(),e.fill(),n.borderWidth>0&&e.stroke()}_updateAnimationTarget(t){const e=this.chart,s=this.$animations,n=s&&s.x,o=s&&s.y;if(n||o){const r=Yt[t.position].call(this,this._active,this._eventPosition);if(!r)return;const a=this._size=ls(this,t),l=Object.assign({},r,this._size),c=cs(e,t,l),h=hs(t,l,c,e);(n._to!==h.x||o._to!==h.y)&&(this.xAlign=c.xAlign,this.yAlign=c.yAlign,this.width=a.width,this.height=a.height,this.caretX=r.x,this.caretY=r.y,this._resolveAnimations().update(this,h))}}_willRender(){return!!this.opacity}draw(t){const e=this.options.setContext(this.getContext());let s=this.opacity;if(!s)return;this._updateAnimationTarget(e);const n={width:this.width,height:this.height},o={x:this.x,y:this.y};s=Math.abs(s)<.001?0:s;const r=J(e.padding),a=this.title.length||this.beforeBody.length||this.body.length||this.afterBody.length||this.footer.length;e.enabled&&a&&(t.save(),t.globalAlpha=s,this.drawBackground(o,t,n,e),$s(t,e.textDirection),o.y+=r.top,this.drawTitle(o,t,e),this.drawBody(o,t,e),this.drawFooter(o,t,e),Ys(t,e.textDirection),t.restore())}getActiveElements(){return this._active||[]}setActiveElements(t,e){const s=this._active,n=t.map(({datasetIndex:a,index:l})=>{const c=this.chart.getDatasetMeta(a);if(!c)throw new Error("Cannot find a dataset at index "+a);return{datasetIndex:a,element:c.data[l],index:l}}),o=!_e(s,n),r=this._positionChanged(n,e);(o||r)&&(this._active=n,this._eventPosition=e,this._ignoreReplayEvents=!0,this.update(!0))}handleEvent(t,e,s=!0){if(e&&this._ignoreReplayEvents)return!1;this._ignoreReplayEvents=!1;const n=this.options,o=this._active||[],r=this._getActiveElements(t,o,e,s),a=this._positionChanged(r,t),l=e||!_e(r,o)||a;return l&&(this._active=r,(n.enabled||n.external)&&(this._eventPosition={x:t.x,y:t.y},this.update(!0,e))),l}_getActiveElements(t,e,s,n){const o=this.options;if(t.type==="mouseout")return[];if(!n)return e.filter(a=>this.chart.data.datasets[a.datasetIndex]&&this.chart.getDatasetMeta(a.datasetIndex).controller.getParsed(a.index)!==void 0);const r=this.chart.getElementsAtEventForMode(t,o.mode,o,s);return o.reverse&&r.reverse(),r}_positionChanged(t,e){const{caretX:s,caretY:n,options:o}=this,r=Yt[o.position].call(this,t,e);return r!==!1&&(s!==r.x||n!==r.y)}}var cl={id:"tooltip",_element:us,positioners:Yt,afterInit(i,t,e){e&&(i.tooltip=new us({chart:i,options:e}))},beforeUpdate(i,t,e){i.tooltip&&i.tooltip.initialize(e)},reset(i,t,e){i.tooltip&&i.tooltip.initialize(e)},afterDraw(i){const t=i.tooltip;if(t&&t._willRender()){const e={tooltip:t};if(i.notifyPlugins("beforeTooltipDraw",{...e,cancelable:!0})===!1)return;t.draw(i.ctx),i.notifyPlugins("afterTooltipDraw",e)}},afterEvent(i,t){if(i.tooltip){const e=t.replay;i.tooltip.handleEvent(t.event,e,t.inChartArea)&&(t.changed=!0)}},defaults:{enabled:!0,external:null,position:"average",backgroundColor:"rgba(0,0,0,0.8)",titleColor:"#fff",titleFont:{weight:"bold"},titleSpacing:2,titleMarginBottom:6,titleAlign:"left",bodyColor:"#fff",bodySpacing:2,bodyFont:{},bodyAlign:"left",footerColor:"#fff",footerSpacing:2,footerMarginTop:6,footerFont:{weight:"bold"},footerAlign:"left",padding:6,caretPadding:2,caretSize:5,cornerRadius:6,boxHeight:(i,t)=>t.bodyFont.size,boxWidth:(i,t)=>t.bodyFont.size,multiKeyBackground:"#fff",displayColors:!0,boxPadding:0,borderColor:"rgba(0,0,0,0)",borderWidth:0,animation:{duration:400,easing:"easeOutQuart"},animations:{numbers:{type:"number",properties:["x","y","width","height","caretX","caretY"]},opacity:{easing:"linear",duration:200}},callbacks:ln},defaultRoutes:{bodyFont:"font",footerFont:"font",titleFont:"font"},descriptors:{_scriptable:i=>i!=="filter"&&i!=="itemSort"&&i!=="external",_indexable:!1,callbacks:{_scriptable:!1,_indexable:!1},animation:{_fallback:!1},animations:{_fallback:"animation"}},additionalOptionScopes:["interaction"]};const Ya=(i,t,e,s)=>(typeof t=="string"?(e=i.push(t)-1,s.unshift({index:e,label:t})):isNaN(t)&&(e=null),e);function Xa(i,t,e,s){const n=i.indexOf(t);if(n===-1)return Ya(i,t,e,s);const o=i.lastIndexOf(t);return n!==o?e:n}const Ua=(i,t)=>i===null?null:K(Math.round(i),0,t);function gs(i){const t=this.getLabels();return i>=0&&i<t.length?t[i]:i}class hl extends Ft{static id="category";static defaults={ticks:{callback:gs}};constructor(t){super(t),this._startValue=void 0,this._valueRange=0,this._addedLabels=[]}init(t){const e=this._addedLabels;if(e.length){const s=this.getLabels();for(const{index:n,label:o}of e)s[n]===o&&s.splice(n,1);this._addedLabels=[]}super.init(t)}parse(t,e){if(L(t))return null;const s=this.getLabels();return e=isFinite(e)&&s[e]===t?e:Xa(s,t,D(e,t),this._addedLabels),Ua(e,s.length-1)}determineDataLimits(){const{minDefined:t,maxDefined:e}=this.getUserBounds();let{min:s,max:n}=this.getMinMax(!0);this.options.bounds==="ticks"&&(t||(s=0),e||(n=this.getLabels().length-1)),this.min=s,this.max=n}buildTicks(){const t=this.min,e=this.max,s=this.options.offset,n=[];let o=this.getLabels();o=t===0&&e===o.length-1?o:o.slice(t,e+1),this._valueRange=Math.max(o.length-(s?0:1),1),this._startValue=this.min-(s?.5:0);for(let r=t;r<=e;r++)n.push({value:r});return n}getLabelForValue(t){return gs.call(this,t)}configure(){super.configure(),this.isHorizontal()||(this._reversePixels=!this._reversePixels)}getPixelForValue(t){return typeof t!="number"&&(t=this.parse(t)),t===null?NaN:this.getPixelForDecimal((t-this._startValue)/this._valueRange)}getPixelForTick(t){const e=this.ticks;return t<0||t>e.length-1?null:this.getPixelForValue(e[t].value)}getValueForPixel(t){return Math.round(this._startValue+this.getDecimalForPixel(t)*this._valueRange)}getBasePixel(){return this.bottom}}function Ka(i,t){const e=[],{bounds:n,step:o,min:r,max:a,precision:l,count:c,maxTicks:h,maxDigits:d,includeBounds:f}=i,u=o||1,g=h-1,{min:p,max:m}=t,b=!L(r),_=!L(a),y=!L(c),v=(m-p)/(d+1);let x=_i((m-p)/g/u)*u,M,k,w,S;if(x<1e-14&&!b&&!_)return[{value:p},{value:m}];S=Math.ceil(m/x)-Math.floor(p/x),S>g&&(x=_i(S*x/g/u)*u),L(l)||(M=Math.pow(10,l),x=Math.ceil(x*M)/M),n==="ticks"?(k=Math.floor(p/x)*x,w=Math.ceil(m/x)*x):(k=p,w=m),b&&_&&o&&Yn((a-r)/o,x/1e3)?(S=Math.round(Math.min((a-r)/x,h)),x=(a-r)/S,k=r,w=a):y?(k=b?r:k,w=_?a:w,S=c-1,x=(w-k)/S):(S=(w-k)/x,pe(S,Math.round(S),x/1e3)?S=Math.round(S):S=Math.ceil(S));const P=Math.max(xi(x),xi(k));M=Math.pow(10,L(l)?P:l),k=Math.round(k*M)/M,w=Math.round(w*M)/M;let O=0;for(b&&(f&&k!==r?(e.push({value:r}),k<r&&O++,pe(Math.round((k+O*x)*M)/M,r,ps(r,v,i))&&O++):k<r&&O++);O<S;++O){const A=Math.round((k+O*x)*M)/M;if(_&&A>a)break;e.push({value:A})}return _&&f&&w!==a?e.length&&pe(e[e.length-1].value,a,ps(a,v,i))?e[e.length-1].value=a:e.push({value:a}):(!_||w===a)&&e.push({value:w}),e}function ps(i,t,{horizontal:e,minRotation:s}){const n=ct(s),o=(e?Math.sin(n):Math.cos(n))||.001,r=.75*t*(""+i).length;return Math.min(t/o,r)}class qa extends Ft{constructor(t){super(t),this.start=void 0,this.end=void 0,this._startValue=void 0,this._endValue=void 0,this._valueRange=0}parse(t,e){return L(t)||(typeof t=="number"||t instanceof Number)&&!isFinite(+t)?null:+t}handleTickRangeOptions(){const{beginAtZero:t}=this.options,{minDefined:e,maxDefined:s}=this.getUserBounds();let{min:n,max:o}=this;const r=l=>n=e?n:l,a=l=>o=s?o:l;if(t){const l=ut(n),c=ut(o);l<0&&c<0?a(0):l>0&&c>0&&r(0)}if(n===o){let l=o===0?1:Math.abs(o*.05);a(o+l),t||r(n-l)}this.min=n,this.max=o}getTickLimit(){const t=this.options.ticks;let{maxTicksLimit:e,stepSize:s}=t,n;return s?(n=Math.ceil(this.max/s)-Math.floor(this.min/s)+1,n>1e3&&(console.warn(`scales.${this.id}.ticks.stepSize: ${s} would result generating up to ${n} ticks. Limiting to 1000.`),n=1e3)):(n=this.computeTickLimit(),e=e||11),e&&(n=Math.min(e,n)),n}computeTickLimit(){return Number.POSITIVE_INFINITY}buildTicks(){const t=this.options,e=t.ticks;let s=this.getTickLimit();s=Math.max(2,s);const n={maxTicks:s,bounds:t.bounds,min:t.min,max:t.max,precision:e.precision,step:e.stepSize,count:e.count,maxDigits:this._maxDigits(),horizontal:this.isHorizontal(),minRotation:e.minRotation||0,includeBounds:e.includeBounds!==!1},o=this._range||this,r=Ka(n,o);return t.bounds==="ticks"&&Xn(r,this,"value"),t.reverse?(r.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),r}configure(){const t=this.ticks;let e=this.min,s=this.max;if(super.configure(),this.options.offset&&t.length){const n=(s-e)/Math.max(t.length-1,1)/2;e-=n,s+=n}this._startValue=e,this._endValue=s,this._valueRange=s-e}getLabelForValue(t){return Je(t,this.chart.options.locale,this.options.ticks.format)}}class dl extends qa{static id="linear";static defaults={ticks:{callback:Is.formatters.numeric}};determineDataLimits(){const{min:t,max:e}=this.getMinMax(!0);this.min=Q(t)?t:0,this.max=Q(e)?e:1,this.handleTickRangeOptions()}computeTickLimit(){const t=this.isHorizontal(),e=t?this.width:this.height,s=ct(this.options.ticks.minRotation),n=(t?Math.sin(s):Math.cos(s))||.001,o=this._resolveTickFontOptions(0);return Math.ceil(e/Math.min(40,o.lineHeight/n))}getPixelForValue(t){return t===null?NaN:this.getPixelForDecimal((t-this._startValue)/this._valueRange)}getValueForPixel(t){return this._startValue+this.getDecimalForPixel(t)*this._valueRange}}const Ce={millisecond:{common:!0,size:1,steps:1e3},second:{common:!0,size:1e3,steps:60},minute:{common:!0,size:6e4,steps:60},hour:{common:!0,size:36e5,steps:24},day:{common:!0,size:864e5,steps:30},week:{common:!1,size:6048e5,steps:4},month:{common:!0,size:2628e6,steps:12},quarter:{common:!1,size:7884e6,steps:4},year:{common:!0,size:3154e7}},U=Object.keys(Ce);function ms(i,t){return i-t}function bs(i,t){if(L(t))return null;const e=i._adapter,{parser:s,round:n,isoWeekday:o}=i._parseOpts;let r=t;return typeof s=="function"&&(r=s(r)),Q(r)||(r=typeof s=="string"?e.parse(r,s):e.parse(r)),r===null?null:(n&&(r=n==="week"&&(ve(o)||o===!0)?e.startOf(r,"isoWeek",o):e.startOf(r,n)),+r)}function _s(i,t,e,s){const n=U.length;for(let o=U.indexOf(i);o<n-1;++o){const r=Ce[U[o]],a=r.steps?r.steps:Number.MAX_SAFE_INTEGER;if(r.common&&Math.ceil((e-t)/(a*r.size))<=s)return U[o]}return U[n-1]}function Ga(i,t,e,s,n){for(let o=U.length-1;o>=U.indexOf(e);o--){const r=U[o];if(Ce[r].common&&i._adapter.diff(n,s,r)>=t-1)return r}return U[e?U.indexOf(e):0]}function Za(i){for(let t=U.indexOf(i)+1,e=U.length;t<e;++t)if(Ce[U[t]].common)return U[t]}function xs(i,t,e){if(!e)i[t]=!0;else if(e.length){const{lo:s,hi:n}=Ze(e,t),o=e[s]>=t?e[s]:e[n];i[o]=!0}}function Qa(i,t,e,s){const n=i._adapter,o=+n.startOf(t[0].value,s),r=t[t.length-1].value;let a,l;for(a=o;a<=r;a=+n.add(a,1,s))l=e[a],l>=0&&(t[l].major=!0);return t}function ys(i,t,e){const s=[],n={},o=t.length;let r,a;for(r=0;r<o;++r)a=t[r],n[a]=r,s.push({value:a,major:!1});return o===0||!e?s:Qa(i,s,n,e)}class vs extends Ft{static id="time";static defaults={bounds:"data",adapters:{},time:{parser:!1,unit:!1,round:!1,isoWeekday:!1,minUnit:"millisecond",displayFormats:{}},ticks:{source:"auto",callback:!1,major:{enabled:!1}}};constructor(t){super(t),this._cache={data:[],labels:[],all:[]},this._unit="day",this._majorUnit=void 0,this._offsets={},this._normalized=!1,this._parseOpts=void 0}init(t,e={}){const s=t.time||(t.time={}),n=this._adapter=new ur._date(t.adapters.date);n.init(e),Xt(s.displayFormats,n.formats()),this._parseOpts={parser:s.parser,round:s.round,isoWeekday:s.isoWeekday},super.init(t),this._normalized=e.normalized}parse(t,e){return t===void 0?null:bs(this,t)}beforeLayout(){super.beforeLayout(),this._cache={data:[],labels:[],all:[]}}determineDataLimits(){const t=this.options,e=this._adapter,s=t.time.unit||"day";let{min:n,max:o,minDefined:r,maxDefined:a}=this.getUserBounds();function l(c){!r&&!isNaN(c.min)&&(n=Math.min(n,c.min)),!a&&!isNaN(c.max)&&(o=Math.max(o,c.max))}(!r||!a)&&(l(this._getLabelBounds()),(t.bounds!=="ticks"||t.ticks.source!=="labels")&&l(this.getMinMax(!1))),n=Q(n)&&!isNaN(n)?n:+e.startOf(Date.now(),s),o=Q(o)&&!isNaN(o)?o:+e.endOf(Date.now(),s)+1,this.min=Math.min(n,o-1),this.max=Math.max(n+1,o)}_getLabelBounds(){const t=this.getLabelTimestamps();let e=Number.POSITIVE_INFINITY,s=Number.NEGATIVE_INFINITY;return t.length&&(e=t[0],s=t[t.length-1]),{min:e,max:s}}buildTicks(){const t=this.options,e=t.time,s=t.ticks,n=s.source==="labels"?this.getLabelTimestamps():this._generate();t.bounds==="ticks"&&n.length&&(this.min=this._userMin||n[0],this.max=this._userMax||n[n.length-1]);const o=this.min,r=this.max,a=Zn(n,o,r);return this._unit=e.unit||(s.autoSkip?_s(e.minUnit,this.min,this.max,this._getLabelCapacity(o)):Ga(this,a.length,e.minUnit,this.min,this.max)),this._majorUnit=!s.major.enabled||this._unit==="year"?void 0:Za(this._unit),this.initOffsets(n),t.reverse&&a.reverse(),ys(this,a,this._majorUnit)}afterAutoSkip(){this.options.offsetAfterAutoskip&&this.initOffsets(this.ticks.map(t=>+t.value))}initOffsets(t=[]){let e=0,s=0,n,o;this.options.offset&&t.length&&(n=this.getDecimalForValue(t[0]),t.length===1?e=1-n:e=(this.getDecimalForValue(t[1])-n)/2,o=this.getDecimalForValue(t[t.length-1]),t.length===1?s=o:s=(o-this.getDecimalForValue(t[t.length-2]))/2);const r=t.length<3?.5:.25;e=K(e,0,r),s=K(s,0,r),this._offsets={start:e,end:s,factor:1/(e+1+s)}}_generate(){const t=this._adapter,e=this.min,s=this.max,n=this.options,o=n.time,r=o.unit||_s(o.minUnit,e,s,this._getLabelCapacity(e)),a=D(n.ticks.stepSize,1),l=r==="week"?o.isoWeekday:!1,c=ve(l)||l===!0,h={};let d=e,f,u;if(c&&(d=+t.startOf(d,"isoWeek",l)),d=+t.startOf(d,c?"day":r),t.diff(s,e,r)>1e5*a)throw new Error(e+" and "+s+" are too far apart with stepSize of "+a+" "+r);const g=n.ticks.source==="data"&&this.getDataTimestamps();for(f=d,u=0;f<s;f=+t.add(f,a,r),u++)xs(h,f,g);return(f===s||n.bounds==="ticks"||u===1)&&xs(h,f,g),Object.keys(h).sort(ms).map(p=>+p)}getLabelForValue(t){const e=this._adapter,s=this.options.time;return s.tooltipFormat?e.format(t,s.tooltipFormat):e.format(t,s.displayFormats.datetime)}format(t,e){const n=this.options.time.displayFormats,o=this._unit,r=e||n[o];return this._adapter.format(t,r)}_tickFormatFunction(t,e,s,n){const o=this.options,r=o.ticks.callback;if(r)return E(r,[t,e,s],this);const a=o.time.displayFormats,l=this._unit,c=this._majorUnit,h=l&&a[l],d=c&&a[c],f=s[e],u=c&&d&&f&&f.major;return this._adapter.format(t,n||(u?d:h))}generateTickLabels(t){let e,s,n;for(e=0,s=t.length;e<s;++e)n=t[e],n.label=this._tickFormatFunction(n.value,e,t)}getDecimalForValue(t){return t===null?NaN:(t-this.min)/(this.max-this.min)}getPixelForValue(t){const e=this._offsets,s=this.getDecimalForValue(t);return this.getPixelForDecimal((e.start+s)*e.factor)}getValueForPixel(t){const e=this._offsets,s=this.getDecimalForPixel(t)/e.factor-e.end;return this.min+s*(this.max-this.min)}_getLabelSize(t){const e=this.options.ticks,s=this.ctx.measureText(t).width,n=ct(this.isHorizontal()?e.maxRotation:e.minRotation),o=Math.cos(n),r=Math.sin(n),a=this._resolveTickFontOptions(0).size;return{w:s*o+a*r,h:s*r+a*o}}_getLabelCapacity(t){const e=this.options.time,s=e.displayFormats,n=s[e.unit]||s.millisecond,o=this._tickFormatFunction(t,0,ys(this,[t],this._majorUnit),n),r=this._getLabelSize(o),a=Math.floor(this.isHorizontal()?this.width/r.w:this.height/r.h)-1;return a>0?a:1}getDataTimestamps(){let t=this._cache.data||[],e,s;if(t.length)return t;const n=this.getMatchingVisibleMetas();if(this._normalized&&n.length)return this._cache.data=n[0].controller.getAllParsedValues(this);for(e=0,s=n.length;e<s;++e)t=t.concat(n[e].controller.getAllParsedValues(this));return this._cache.data=this.normalize(t)}getLabelTimestamps(){const t=this._cache.labels||[];let e,s;if(t.length)return t;const n=this.getLabels();for(e=0,s=n.length;e<s;++e)t.push(bs(this,n[e]));return this._cache.labels=this._normalized?t:this.normalize(t)}normalize(t){return Rs(t.sort(ms))}}function ge(i,t,e){let s=0,n=i.length-1,o,r,a,l;e?(t>=i[s].pos&&t<=i[n].pos&&({lo:s,hi:n}=Ne(i,"pos",t)),{pos:o,time:a}=i[s],{pos:r,time:l}=i[n]):(t>=i[s].time&&t<=i[n].time&&({lo:s,hi:n}=Ne(i,"time",t)),{time:o,pos:a}=i[s],{time:r,pos:l}=i[n]);const c=r-o;return c?a+(l-a)*(t-o)/c:a}class fl extends vs{static id="timeseries";static defaults=vs.defaults;constructor(t){super(t),this._table=[],this._minPos=void 0,this._tableRange=void 0}initOffsets(){const t=this._getTimestampsForTable(),e=this._table=this.buildLookupTable(t);this._minPos=ge(e,this.min),this._tableRange=ge(e,this.max)-this._minPos,super.initOffsets(t)}buildLookupTable(t){const{min:e,max:s}=this,n=[],o=[];let r,a,l,c,h;for(r=0,a=t.length;r<a;++r)c=t[r],c>=e&&c<=s&&n.push(c);if(n.length<2)return[{time:e,pos:0},{time:s,pos:1}];for(r=0,a=n.length;r<a;++r)h=n[r+1],l=n[r-1],c=n[r],Math.round((h+l)/2)!==c&&o.push({time:c,pos:r/(a-1)});return o}_generate(){const t=this.min,e=this.max;let s=super.getDataTimestamps();return(!s.includes(t)||!s.length)&&s.splice(0,0,t),(!s.includes(e)||s.length===1)&&s.push(e),s.sort((n,o)=>n-o)}_getTimestampsForTable(){let t=this._cache.all||[];if(t.length)return t;const e=this.getDataTimestamps(),s=this.getLabelTimestamps();return e.length&&s.length?t=this.normalize(e.concat(s)):t=e.length?e:s,t=this._cache.all=t,t}getDecimalForValue(t){return(ge(this._table,t)-this._minPos)/this._tableRange}getValueForPixel(t){const e=this._offsets,s=this.getDecimalForPixel(t)/e.factor-e.end;return ge(this._table,s*this._tableRange+this._minPos,!0)}}const cn="label";function Ms(i,t){typeof i=="function"?i(t):i&&(i.current=t)}function Ja(i,t){const e=i.options;e&&t&&Object.assign(e,t)}function hn(i,t){i.labels=t}function dn(i,t){let e=arguments.length>2&&arguments[2]!==void 0?arguments[2]:cn;const s=[];i.datasets=t.map(n=>{const o=i.datasets.find(r=>r[e]===n[e]);return!o||!n.data||s.includes(o)?{...n}:(s.push(o),Object.assign(o,n),o)})}function tl(i){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:cn;const e={labels:[],datasets:[]};return hn(e,i.labels),dn(e,i.datasets,t),e}function el(i,t){const{height:e=150,width:s=300,redraw:n=!1,datasetIdKey:o,type:r,data:a,options:l,plugins:c=[],fallbackContent:h,updateMode:d,...f}=i,u=st.useRef(null),g=st.useRef(null),p=()=>{u.current&&(g.current=new hi(u.current,{type:r,data:tl(a,o),options:l&&{...l},plugins:c}),Ms(t,g.current))},m=()=>{Ms(t,null),g.current&&(g.current.destroy(),g.current=null)};return st.useEffect(()=>{!n&&g.current&&l&&Ja(g.current,l)},[n,l]),st.useEffect(()=>{!n&&g.current&&hn(g.current.config.data,a.labels)},[n,a.labels]),st.useEffect(()=>{!n&&g.current&&a.datasets&&dn(g.current.config.data,a.datasets,o)},[n,a.datasets]),st.useEffect(()=>{g.current&&(n?(m(),setTimeout(p)):g.current.update(d))},[n,l,a.labels,a.datasets,d]),st.useEffect(()=>{g.current&&(m(),setTimeout(p))},[r]),st.useEffect(()=>(p(),()=>m()),[]),ks.createElement("canvas",{ref:u,role:"img",height:e,width:s,...f},h)}const il=st.forwardRef(el);function fn(i,t){return hi.register(t),st.forwardRef((e,s)=>ks.createElement(il,{...e,ref:s,type:i}))}const ul=fn("bar",hr),gl=fn("doughnut",fr);export{ol as A,ul as B,hi as C,gl as D,dl as L,al as a,hl as b,rl as c,ll as d,cl as p};
