import { createContext, useContext, useState, useEffect } from 'react'

const ThemeContext = createContext()

export const themes = {
  electric: {
    name: 'Electric Blue',
    primary: 'bg-blue-700',
    secondary: 'bg-blue-50',
    accent: 'bg-blue-500',
    background: 'bg-blue-25',
    text: 'text-blue-900',
    textSecondary: 'text-blue-700',
    border: 'border-blue-200',
    card: 'bg-blue-50',
    gradient: 'from-blue-500 to-blue-700',
    // Additional shades for consistency
    light: 'bg-blue-100',
    lighter: 'bg-blue-50',
    dark: 'bg-blue-700',
    darker: 'bg-blue-800'
  },
  dark: {
    name: 'Dark Mode',
    primary: 'bg-gray-700',
    secondary: 'bg-gray-700',
    accent: 'bg-gray-500',
    background: 'bg-gray-900',
    text: 'text-white',
    textSecondary: 'text-gray-300',
    border: 'border-gray-600',
    card: 'bg-gray-800',
    gradient: 'from-gray-600 to-gray-800',
    // Additional shades for consistency
    light: 'bg-gray-700',
    lighter: 'bg-gray-600',
    dark: 'bg-gray-800',
    darker: 'bg-gray-900'
  },
  green: {
    name: 'Eco Green',
    primary: 'bg-green-700',
    secondary: 'bg-green-50',
    accent: 'bg-green-500',
    background: 'bg-green-25',
    text: 'text-green-900',
    textSecondary: 'text-green-700',
    border: 'border-green-200',
    card: 'bg-green-50',
    gradient: 'from-green-500 to-green-700',
    // Additional shades for consistency
    light: 'bg-green-100',
    lighter: 'bg-green-50',
    dark: 'bg-green-700',
    darker: 'bg-green-800'
  },
  teal: {
    name: 'Ocean Teal',
    primary: 'bg-teal-700',
    secondary: 'bg-teal-50',
    accent: 'bg-teal-500',
    background: 'bg-teal-25',
    text: 'text-teal-900',
    textSecondary: 'text-teal-700',
    border: 'border-teal-200',
    card: 'bg-teal-50',
    gradient: 'from-teal-500 to-teal-700',
    // Additional shades for consistency
    light: 'bg-teal-100',
    lighter: 'bg-teal-50',
    dark: 'bg-teal-700',
    darker: 'bg-teal-800'
  },
  pink: {
    name: 'Rose Pink',
    primary: 'bg-pink-700',
    secondary: 'bg-pink-50',
    accent: 'bg-pink-500',
    background: 'bg-pink-25',
    text: 'text-pink-900',
    textSecondary: 'text-pink-700',
    border: 'border-pink-200',
    card: 'bg-pink-50',
    gradient: 'from-pink-500 to-pink-700',
    // Additional shades for consistency
    light: 'bg-pink-100',
    lighter: 'bg-pink-50',
    dark: 'bg-pink-700',
    darker: 'bg-pink-800'
  }
}



// Font size options
export const fontSizes = {
  small: {
    name: 'Small',
    scale: 'text-sm',
    headingScale: 'text-xl',
    titleScale: 'text-2xl',
    size: '14px'
  },
  medium: {
    name: 'Medium',
    scale: 'text-base',
    headingScale: 'text-2xl',
    titleScale: 'text-3xl',
    size: '16px'
  },
  large: {
    name: 'Large',
    scale: 'text-lg',
    headingScale: 'text-3xl',
    titleScale: 'text-4xl',
    size: '18px'
  },
  extraLarge: {
    name: 'Extra Large',
    scale: 'text-xl',
    headingScale: 'text-4xl',
    titleScale: 'text-5xl',
    size: '20px'
  }
}

export function ThemeProvider({ children }) {
  const [currentTheme, setCurrentTheme] = useState('electric')

  // Load theme settings from localStorage
  useEffect(() => {
    // FORCE THEME RESET - Clear all theme storage
    console.log('FORCING THEME RESET - Clearing all theme data')

    const themeKeysToRemove = [
      'prepaid-meter-theme',
      'prepaid-meter-theme-v1.2',
      'prepaid-meter-theme-v1.3'
    ]

    themeKeysToRemove.forEach(key => {
      localStorage.removeItem(key)
      console.log(`Cleared theme key: ${key}`)
    })

    // Force default theme (electric)
    setCurrentTheme('electric')
    console.log('Theme reset to default: electric')
  }, [])

  // Save theme settings to localStorage
  useEffect(() => {
    localStorage.setItem('prepaid-meter-theme-v1.3', currentTheme)
  }, [currentTheme])

  const value = {
    currentTheme,
    setCurrentTheme,
    theme: themes[currentTheme],
    themes
  }

  return (
    <ThemeContext.Provider value={value}>
      <div
        className={`${themes[currentTheme].background} ${themes[currentTheme].text} text-base min-h-screen transition-all duration-300`}
        style={{
          fontFamily: 'Inter, system-ui, -apple-system, sans-serif',
          fontSize: '16px'
        }}
      >
        {children}
      </div>
    </ThemeContext.Provider>
  )
}

export function useTheme() {
  const context = useContext(ThemeContext)
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider')
  }
  return context
}
