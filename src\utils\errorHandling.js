/**
 * Utility functions for error handling and safe async operations
 */

/**
 * Safely execute an async function with error handling
 * @param {Function} asyncFn - The async function to execute
 * @param {any} fallbackValue - Value to return if function fails
 * @param {Function} onError - Optional error handler
 * @returns {Promise<any>} Result or fallback value
 */
export async function safeAsync(asyncFn, fallbackValue = null, onError = null) {
  try {
    return await asyncFn()
  } catch (error) {
    console.error('Safe async operation failed:', error)
    
    if (onError && typeof onError === 'function') {
      onError(error)
    }
    
    return fallbackValue
  }
}

/**
 * Safely access localStorage with error handling
 * @param {string} key - The localStorage key
 * @param {any} defaultValue - Default value if access fails
 * @returns {any} Parsed value or default
 */
export function safeLocalStorageGet(key, defaultValue = null) {
  try {
    const item = localStorage.getItem(key)
    return item ? JSON.parse(item) : defaultValue
  } catch (error) {
    console.error(`Error reading from localStorage key "${key}":`, error)
    return defaultValue
  }
}

/**
 * Safely set localStorage with error handling
 * @param {string} key - The localStorage key
 * @param {any} value - Value to store
 * @returns {boolean} Success status
 */
export function safeLocalStorageSet(key, value) {
  try {
    localStorage.setItem(key, JSON.stringify(value))
    return true
  } catch (error) {
    console.error(`Error writing to localStorage key "${key}":`, error)
    return false
  }
}

/**
 * Safely remove from localStorage
 * @param {string} key - The localStorage key
 * @returns {boolean} Success status
 */
export function safeLocalStorageRemove(key) {
  try {
    localStorage.removeItem(key)
    return true
  } catch (error) {
    console.error(`Error removing localStorage key "${key}":`, error)
    return false
  }
}

/**
 * Debounce function to prevent excessive calls
 * @param {Function} func - Function to debounce
 * @param {number} wait - Wait time in milliseconds
 * @returns {Function} Debounced function
 */
export function debounce(func, wait) {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

/**
 * Throttle function to limit execution frequency
 * @param {Function} func - Function to throttle
 * @param {number} limit - Time limit in milliseconds
 * @returns {Function} Throttled function
 */
export function throttle(func, limit) {
  let inThrottle
  return function executedFunction(...args) {
    if (!inThrottle) {
      func.apply(this, args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

/**
 * Retry an async operation with exponential backoff
 * @param {Function} operation - Async operation to retry
 * @param {number} maxRetries - Maximum number of retries
 * @param {number} baseDelay - Base delay in milliseconds
 * @returns {Promise<any>} Operation result
 */
export async function retryWithBackoff(operation, maxRetries = 3, baseDelay = 1000) {
  let lastError
  
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await operation()
    } catch (error) {
      lastError = error
      
      if (attempt === maxRetries) {
        throw lastError
      }
      
      const delay = baseDelay * Math.pow(2, attempt)
      console.warn(`Operation failed (attempt ${attempt + 1}/${maxRetries + 1}), retrying in ${delay}ms:`, error)
      
      await new Promise(resolve => setTimeout(resolve, delay))
    }
  }
}

/**
 * Create a safe event handler that won't crash the app
 * @param {Function} handler - Event handler function
 * @param {Function} onError - Optional error callback
 * @returns {Function} Safe event handler
 */
export function safeEventHandler(handler, onError = null) {
  return (...args) => {
    try {
      return handler(...args)
    } catch (error) {
      console.error('Event handler error:', error)
      
      if (onError && typeof onError === 'function') {
        onError(error)
      }
    }
  }
}

/**
 * Validate and sanitize user input
 * @param {any} input - Input to validate
 * @param {string} type - Expected type ('string', 'number', 'email', etc.)
 * @param {any} defaultValue - Default value if validation fails
 * @returns {any} Validated input or default value
 */
export function validateInput(input, type, defaultValue = '') {
  try {
    switch (type) {
      case 'string': {
        return typeof input === 'string' ? input.trim() : String(input || defaultValue)
      }

      case 'number': {
        const num = Number(input)
        return !isNaN(num) && isFinite(num) ? num : Number(defaultValue) || 0
      }

      case 'email': {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
        const email = String(input || '').trim()
        return emailRegex.test(email) ? email : defaultValue
      }

      case 'positive-number': {
        const posNum = Number(input)
        return !isNaN(posNum) && isFinite(posNum) && posNum >= 0 ? posNum : Number(defaultValue) || 0
      }
      
      default:
        return input !== undefined && input !== null ? input : defaultValue
    }
  } catch (error) {
    console.error('Input validation error:', error)
    return defaultValue
  }
}

/**
 * Format error messages for user display
 * @param {Error|string} error - Error object or message
 * @returns {string} User-friendly error message
 */
export function formatErrorMessage(error) {
  if (typeof error === 'string') {
    return error
  }
  
  if (error instanceof Error) {
    // Common error patterns
    if (error.message.includes('fetch')) {
      return 'Network error. Please check your connection and try again.'
    }
    
    if (error.message.includes('localStorage')) {
      return 'Storage error. Your browser may be in private mode or storage may be full.'
    }
    
    if (error.message.includes('permission')) {
      return 'Permission denied. Please check your browser settings.'
    }
    
    return error.message || 'An unexpected error occurred.'
  }
  
  return 'An unknown error occurred.'
}
