# ⚡ Prepaid Meter App

A comprehensive **Electricity Recording and Usage Web App** for tracking prepaid electricity consumption, purchases, and usage patterns.

![Prepaid Meter App](https://img.shields.io/badge/React-18.2.0-blue) ![Vite](https://img.shields.io/badge/Vite-5.4.19-green) ![Tailwind](https://img.shields.io/badge/TailwindCSS-3.3.5-blue)

## 🚀 Features

### 📊 **Five-Page Navigation**
- **Dashboard** - Current units, usage tracking, threshold warnings
- **Purchases** - Add purchases with real-time calculations
- **Usage** - Record meter readings and track consumption  
- **History** - Detailed transaction logs with filtering
- **Settings** - Configuration, themes, and reset options

### ⚡ **Smart Tracking**
- Configurable Rands per unit cost
- Real-time purchase calculations as you type
- Automatic usage calculations (Previous - Current = Usage)
- Timestamp logging for all transactions
- Monthly auto-reset functionality

### 🎨 **Beautiful Themes**
- **10 Stunning Themes**: Electric Blue, Dark Mode, Eco Green, Royal Purple, Sunset Orange, Ocean Teal, Fire Red, Deep Indigo, Rose Pink, Modern Slate
- Customizable font sizes
- Responsive design for all devices
- Animated lightning bolt logo

### 📈 **Visualizations**
- Interactive animated dial using Chart.js
- Usage statistics and trends
- Threshold warning alerts
- Real-time calculation previews

### 🔄 **Reset Options**
- **Factory Reset** - Complete data wipe with fresh setup
- **Dashboard Reset** - Clear current data, preserve history
- **Monthly Auto-Reset** - Automatic reset on 1st of each month

## 🛠 Technology Stack

- **Frontend**: React.js 18.2.0 with Vite
- **Styling**: Tailwind CSS 3.3.5
- **Charts**: Chart.js with react-chartjs-2
- **Icons**: React Icons
- **State Management**: React Context + localStorage
- **Routing**: React Router DOM
- **Date Handling**: date-fns

## 🚀 Quick Start

### Prerequisites
- Node.js (v16 or higher)
- npm or yarn

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/yourusername/prepaid-meter-app.git
   cd prepaid-meter-app
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start development server**
   ```bash
   npm run dev
   ```

4. **Open your browser**
   ```
   http://localhost:5173
   ```

### Build for Production
```bash
npm run build
```

## 📱 Usage

### Initial Setup
1. Enter your current meter reading when first launching the app
2. Configure your unit cost in Settings (default: R2.50 per unit)
3. Set your usage threshold limit for warnings

### Adding Purchases
1. Go to **Purchases** page
2. Choose calculation mode (Enter Amount or Enter Units)
3. Watch real-time calculations as you type
4. Submit to add to your account

### Recording Usage
1. Go to **Usage** page  
2. Enter your current meter reading
3. System automatically calculates usage difference
4. View cost breakdown and remaining units

### Viewing History
1. Go to **History** page
2. Filter by date or transaction type
3. View detailed logs of all purchases and usage

### Customizing Appearance
1. Go to **Settings** → **Appearance**
2. Choose from 10 beautiful themes
3. Adjust font size preferences
4. Preview changes in real-time

## 🎯 Key Calculations

### Usage Calculation
```javascript
Usage = Previous Units - Current Units
Cost = Usage × Unit Cost
```

### Purchase Calculation
```javascript
Units = Amount ÷ Unit Cost
Amount = Units × Unit Cost
```

## 📊 Data Persistence

All data is automatically saved to browser localStorage:
- Purchase history
- Usage records
- Settings and preferences
- Theme selections
- Current meter readings

## 🔧 Configuration

### Unit Cost
- Configurable in Settings
- Default: R2.50 per unit
- Used for all cost calculations

### Threshold Warnings
- Set custom usage limits
- Automatic alerts when exceeded
- Visual indicators on dashboard

### Monthly Reset
- Automatic reset on 1st of each month
- Preserves remaining units
- Clears usage history

## 🎨 Available Themes

1. **Electric Blue** (Default)
2. **Dark Mode**
3. **Eco Green**
4. **Royal Purple**
5. **Sunset Orange**
6. **Ocean Teal**
7. **Fire Red**
8. **Deep Indigo**
9. **Rose Pink**
10. **Modern Slate**

## 🤝 Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Built with React.js and Vite for optimal performance
- Styled with Tailwind CSS for beautiful, responsive design
- Charts powered by Chart.js for interactive visualizations
- Icons from React Icons library

---

**Made with ⚡ for efficient electricity tracking**
