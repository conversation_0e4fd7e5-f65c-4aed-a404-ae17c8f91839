/**
 * AdMob Configuration
 * Centralized configuration for AdMob ad unit IDs and settings
 */

// Environment detection
const isDevelopment = process.env.NODE_ENV === 'development'

// Test Ad Unit IDs (provided by Google for testing)
const TEST_AD_UNITS = {
  APP_ID: 'ca-app-pub-3940256099942544~3347511713',
  NATIVE_AD: 'ca-app-pub-3940256099942544/2247696110',
  BANNER_AD: 'ca-app-pub-3940256099942544/6300978111',
  INTERSTITIAL_AD: 'ca-app-pub-3940256099942544/1033173712',
  REWARDED_AD: 'ca-app-pub-3940256099942544/5224354917'
}

// Production Ad Unit IDs (replace with your actual IDs)
const PRODUCTION_AD_UNITS = {
  APP_ID: 'ca-app-pub-5519957327506488~1322700195',
  NATIVE_AD: 'ca-app-pub-5519957327506488/XXXXXXXXXX', // Replace with your native ad unit ID
  BANNER_AD: 'ca-app-pub-5519957327506488/XXXXXXXXXX', // Replace with your banner ad unit ID
  INTERSTITIAL_AD: 'ca-app-pub-5519957327506488/XXXXXXXXXX', // Replace with your interstitial ad unit ID
  REWARDED_AD: 'ca-app-pub-5519957327506488/XXXXXXXXXX' // Replace with your rewarded ad unit ID
}

// Current configuration based on environment
const CURRENT_AD_UNITS = isDevelopment ? TEST_AD_UNITS : PRODUCTION_AD_UNITS

// AdMob Settings
export const ADMOB_CONFIG = {
  // Ad Unit IDs
  APP_ID: CURRENT_AD_UNITS.APP_ID,
  NATIVE_AD_UNIT_ID: CURRENT_AD_UNITS.NATIVE_AD,
  BANNER_AD_UNIT_ID: CURRENT_AD_UNITS.BANNER_AD,
  INTERSTITIAL_AD_UNIT_ID: CURRENT_AD_UNITS.INTERSTITIAL_AD,
  REWARDED_AD_UNIT_ID: CURRENT_AD_UNITS.REWARDED_AD,

  // Testing Configuration
  IS_TESTING: isDevelopment,
  TESTING_DEVICES: ['8dcaa6eb-f1b7-4d55-96a9-841e2125351e'], // Your device ID for testing

  // Native Ad Options
  NATIVE_AD_OPTIONS: {
    adChoicesPlacement: 'TOP_RIGHT',
    mediaAspectRatio: 'LANDSCAPE',
    requestMultipleImages: false
  },

  // Ad Loading Settings
  AD_TIMEOUT: 10000, // 10 seconds
  MAX_RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 2000, // 2 seconds

  // Compliance Settings
  MIN_AD_SIZE: {
    width: 120, // dp
    height: 120 // dp
  },
  
  // Text Limits (AdMob requirements)
  TEXT_LIMITS: {
    HEADLINE: 25, // characters
    BODY: 90, // characters
    CTA: 15, // characters
    ADVERTISER: 25 // characters
  }
}

// Helper functions
export const getAdUnitId = (adType) => {
  switch (adType.toLowerCase()) {
    case 'native':
      return ADMOB_CONFIG.NATIVE_AD_UNIT_ID
    case 'banner':
      return ADMOB_CONFIG.BANNER_AD_UNIT_ID
    case 'interstitial':
      return ADMOB_CONFIG.INTERSTITIAL_AD_UNIT_ID
    case 'rewarded':
      return ADMOB_CONFIG.REWARDED_AD_UNIT_ID
    default:
      throw new Error(`Unknown ad type: ${adType}`)
  }
}

export const isTestingMode = () => {
  return ADMOB_CONFIG.IS_TESTING
}

export const getTestingDevices = () => {
  return ADMOB_CONFIG.TESTING_DEVICES
}

// Validation helpers
export const validateAdText = (text, type) => {
  const limit = ADMOB_CONFIG.TEXT_LIMITS[type.toUpperCase()]
  if (!limit) return text
  
  return text && text.length > limit 
    ? `${text.substring(0, limit)}...` 
    : text
}

// Default export
export default ADMOB_CONFIG
