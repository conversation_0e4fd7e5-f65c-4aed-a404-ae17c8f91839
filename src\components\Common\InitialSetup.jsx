import { useState } from 'react'
import { useApp } from '../../context/AppContext'
import { useTheme } from '../../context/ThemeContext'
import { preventNumberInputScroll } from '../../hooks/usePreventNumberInputScroll'

function InitialSetup() {
  const [initialUnits, setInitialUnits] = useState('')
  const [unitCost, setUnitCost] = useState('')
  const [error, setError] = useState('')
  const { initializeApp, state } = useApp()
  const { theme } = useTheme()

  const handleSubmit = (e) => {
    e.preventDefault()
    setError('')

    const units = parseFloat(initialUnits)
    const cost = parseFloat(unitCost)

    if (isNaN(units) || units < 0) {
      setError('Please enter a valid number of units (0 or greater)')
      return
    }

    if (isNaN(cost) || cost <= 0) {
      setError('Please enter a valid cost per unit (greater than 0)')
      return
    }

    initializeApp(units, cost)
  }

  return (
    <div className={`min-h-screen ${theme.background}`}>
      <div className="h-screen overflow-y-auto">
        <div className="container mx-auto px-4 py-4">
          <div className={`max-w-lg mx-auto ${theme.card} rounded-2xl shadow-2xl p-6 border ${theme.border}`}>
        {/* Logo and welcome */}
        <div className="text-center mb-6">
          {/* Your Custom Logo */}
          <div className="flex justify-center mb-4">
            <div className="h-16 w-16 flex items-center justify-center">
              <img
                src="/Logo Prepaid User.png"
                alt="Prepaid User Electricity Logo"
                className="h-16 w-16 object-contain"
                onError={(e) => {
                  // Fallback to lightning bolt if your logo fails to load
                  e.target.style.display = 'none';
                  e.target.nextElementSibling.style.display = 'flex';
                }}
              />
              <div
                className="h-16 w-16 rounded-full bg-blue-600 flex items-center justify-center shadow-2xl border-4 border-white"
                style={{ display: 'none' }}
              >
                <span className="text-white text-2xl font-bold">⚡</span>
              </div>
            </div>
          </div>
          <h1 className={`text-2xl font-bold ${theme.text} mb-3`}>
            Welcome to Prepaid Meter App
          </h1>
          <p className={`${theme.textSecondary} text-base mb-2`}>
            Let&apos;s get started by setting up your initial meter reading and cost settings
          </p>
        </div>

        {/* Setup form */}
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label
              htmlFor="initialUnits"
              className={`block text-sm font-medium ${theme.text} mb-2`}
            >
              Initial Unit Value
            </label>
            <input
              type="number"
              id="initialUnits"
              value={initialUnits}
              onChange={(e) => setInitialUnits(e.target.value)}
              onWheel={preventNumberInputScroll}
              step="0.01"
              min="0"
              placeholder="Enter your current meter reading"
              className={`w-full px-3 py-3 border-2 ${theme.border} rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${theme.card} ${theme.text} transition-all duration-200 hover:border-blue-300`}
              required
            />
          </div>

          <div>
            <label
              htmlFor="unitCost"
              className={`block text-sm font-medium ${theme.text} mb-2`}
            >
              Cost per Unit ({state.currencySymbol || 'R'})
            </label>
            <input
              type="number"
              id="unitCost"
              value={unitCost}
              onChange={(e) => setUnitCost(e.target.value)}
              onWheel={preventNumberInputScroll}
              step="0.01"
              min="0.01"
              placeholder="Enter cost per unit (e.g., 2.50)"
              className={`w-full px-3 py-3 border-2 ${theme.border} rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${theme.card} ${theme.text} transition-all duration-200 hover:border-blue-300`}
              required
            />
          </div>

          {error && (
            <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-sm text-red-600">{error}</p>
            </div>
          )}

          {/* Integrated Setup Summary - Compact preview cards */}
          {(initialUnits || unitCost) && (
            <div className="mt-4 space-y-3">
              {/* Initial Units Card */}
              <div className={`relative overflow-hidden rounded-xl ${theme.card} border ${theme.border} p-4 shadow-lg`}>
                <div className={`absolute inset-0 ${theme.secondary}`}></div>
                <div className="relative">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className={`text-xs font-semibold ${theme.textSecondary} tracking-wider uppercase mb-2`}>STARTING UNITS</div>
                      <p className={`text-xl font-bold ${theme.text} mb-1`}>
                        {parseFloat(initialUnits || 0).toFixed(2)}
                      </p>
                      <p className={`text-xs font-medium ${theme.textSecondary}`}>
                        Initial meter reading
                      </p>
                    </div>
                    <div className={`p-2 rounded-lg bg-gradient-to-br ${theme.gradient} shadow-lg ml-3`}>
                      <span className="text-white text-lg">⚡</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Unit Cost Card */}
              <div className={`relative overflow-hidden rounded-xl ${theme.card} border ${theme.border} p-4 shadow-lg`}>
                <div className={`absolute inset-0 ${theme.secondary}`}></div>
                <div className="relative">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className={`text-xs font-semibold ${theme.textSecondary} tracking-wider uppercase mb-2`}>UNIT COST</div>
                      <p className={`text-xl font-bold ${theme.text} mb-1`}>
                        {state.currencySymbol || 'R'}{parseFloat(unitCost || 0).toFixed(2)}
                      </p>
                      <p className={`text-xs font-medium ${theme.textSecondary}`}>
                        Per unit rate
                      </p>
                    </div>
                    <div className={`p-2 rounded-lg bg-gradient-to-br ${theme.gradient} shadow-lg ml-3`}>
                      <span className="text-white text-lg">💰</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          <button
            type="submit"
            className={`w-full bg-gradient-to-r ${theme.gradient} text-white py-3 px-6 rounded-xl font-semibold hover:opacity-90 transition-all duration-200 focus:ring-2 focus:ring-opacity-50 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5`}
          >
            🚀 Initialize App
          </button>
        </form>

        {/* Additional info */}
        <div className="mt-4 text-center">
          <p className={`text-xs ${theme.textSecondary}`}>
            You can always change these values later in Settings
          </p>
        </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default InitialSetup
