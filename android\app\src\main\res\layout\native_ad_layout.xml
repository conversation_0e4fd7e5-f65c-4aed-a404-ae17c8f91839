<?xml version="1.0" encoding="utf-8"?>
<com.google.android.gms.ads.nativead.NativeAdView
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:minWidth="32dp"
    android:minHeight="32dp"
    android:background="#FFFFFF"
    android:clickable="false"
    android:focusable="false">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="8dp"
        android:minWidth="32dp"
        android:minHeight="32dp">

        <!-- Header with ad attribution and spacer for AdChoices -->
        <LinearLayout
            android:id="@+id/ad_header"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <!-- Ad attribution (required) - CRITICAL: Must be minimum 15px -->
            <TextView
                android:id="@+id/ad_attribution"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Ad"
                android:textSize="12sp"
                android:textStyle="bold"
                android:textColor="#FFFFFF"
                android:background="#CC000000"
                android:paddingLeft="6dp"
                android:paddingTop="4dp"
                android:paddingRight="6dp"
                android:paddingBottom="4dp"
                android:minWidth="15px"
                android:minHeight="15px" />

            <!-- Spacer to push AdChoices to right -->
            <View
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1" />

        </LinearLayout>

        <!-- Headline (required) -->
        <TextView
            android:id="@+id/ad_headline"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="#000000"
            android:maxLines="2"
            android:ellipsize="end" />

        <!-- Body text (optional) -->
        <TextView
            android:id="@+id/ad_body"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:textSize="14sp"
            android:textColor="#666666"
            android:maxLines="3"
            android:ellipsize="end" />

        <!-- Media view (required for video ads, minimum 120x120dp per Google policy) -->
        <com.google.android.gms.ads.nativead.MediaView
            android:id="@+id/ad_media"
            android:layout_width="match_parent"
            android:layout_height="120dp"
            android:layout_marginTop="8dp"
            android:minWidth="120dp"
            android:minHeight="120dp" />

        <!-- App icon (for app install ads) -->
        <ImageView
            android:id="@+id/ad_app_icon"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_marginTop="8dp"
            android:scaleType="fitCenter" />

        <!-- Star rating (for app install ads) -->
        <TextView
            android:id="@+id/ad_stars"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:textSize="12sp"
            android:textColor="#FFA500" />

        <!-- Store name (for app install ads) -->
        <TextView
            android:id="@+id/ad_store"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:textSize="11sp"
            android:textColor="#666666" />

        <!-- Price (for app install ads) -->
        <TextView
            android:id="@+id/ad_price"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:textSize="12sp"
            android:textColor="#000000"
            android:textStyle="bold" />

        <!-- Call to action button (required) -->
        <Button
            android:id="@+id/ad_call_to_action"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:layout_gravity="end"
            android:textSize="14sp"
            android:textColor="#FFFFFF"
            android:background="#4285F4"
            android:paddingLeft="16dp"
            android:paddingTop="8dp"
            android:paddingRight="16dp"
            android:paddingBottom="8dp" />

        <!-- Advertiser name (optional) -->
        <TextView
            android:id="@+id/ad_advertiser"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:textSize="12sp"
            android:textColor="#666666" />

    </LinearLayout>

</com.google.android.gms.ads.nativead.NativeAdView>
