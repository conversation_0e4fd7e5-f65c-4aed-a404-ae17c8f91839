import { useEffect } from 'react'

/**
 * Custom hook to prevent scroll wheel from changing number input values
 * This provides an additional layer of protection beyond the global event listener
 */
export const usePreventNumberInputScroll = () => {
  useEffect(() => {
    // Function to prevent scroll on number inputs
    const preventScroll = (e) => {
      e.preventDefault()
    }

    // Get all number inputs
    const numberInputs = document.querySelectorAll('input[type="number"]')
    
    // Add wheel event listener to each number input
    numberInputs.forEach(input => {
      input.addEventListener('wheel', preventScroll, { passive: false })
    })

    // Cleanup function
    return () => {
      numberInputs.forEach(input => {
        input.removeEventListener('wheel', preventScroll)
      })
    }
  }, [])
}

/**
 * Function to add scroll prevention to a specific input element
 * Can be used as an onWheel handler
 */
export const preventNumberInputScroll = (e) => {
  if (e.target.type === 'number') {
    e.preventDefault()
  }
}

export default usePreventNumberInputScroll
