import{u as y,a as v,j as e,p as S}from"../assets/index-DGp_uodO.js";import{r as j}from"./vendor-C67cHu0f.js";import{l as c,H as w,p as f}from"./icons-Bhz3yUky.js";import{S as R}from"./SwipeableLayout-CLJYmTbc.js";import{N as P}from"./NativeAd-1Q8PL9Dc.js";import"./utils-CgIdLkdF.js";function C(){const[d,m]=j.useState(""),[s,b]=j.useState(!1),{state:l,addPurchase:p,getDisplayUnitName:u}=y(),{theme:t}=v(),g=[{code:"ZAR",name:"South African Rand",symbol:"R"},{code:"USD",name:"US Dollar",symbol:"$"},{code:"EUR",name:"Euro",symbol:"€"},{code:"GBP",name:"British Pound",symbol:"£"},{code:"JPY",name:"Japanese Yen",symbol:"¥"},{code:"CAD",name:"Canadian Dollar",symbol:"C$"},{code:"AUD",name:"Australian Dollar",symbol:"A$"},{code:"CHF",name:"Swiss Franc",symbol:"CHF"},{code:"CNY",name:"Chinese Yuan",symbol:"¥"},{code:"INR",name:"Indian Rupee",symbol:"₹"},{code:"BRL",name:"Brazilian Real",symbol:"R$"},{code:"KRW",name:"South Korean Won",symbol:"₩"},{code:"MXN",name:"Mexican Peso",symbol:"$"},{code:"SGD",name:"Singapore Dollar",symbol:"S$"},{code:"NZD",name:"New Zealand Dollar",symbol:"NZ$"}],x=i=>Math.round((i+Number.EPSILON)*100)/100,a=i=>{const n=x(i);return n%1===0?n.toString():n.toFixed(2)},r=parseFloat(d)||0,o=l.unitCost||0,h=o>0?x(r/o):0,$=async i=>{i.preventDefault(),b(!0);try{const n=x(parseFloat(d)),N=h;if(isNaN(n)||n<=0){alert("Please enter a valid positive amount");return}if(o<=0){alert("Please set a valid unit cost in Settings before making a purchase");return}if(N<=0){alert("The calculated units must be greater than 0");return}p(n,N),m(""),alert(`Purchase added successfully! Added ${a(N)} ${u()} for ${l.currencySymbol||"R"}${a(n)}`)}catch(n){console.error("Error adding purchase:",n),alert("Error adding purchase. Please try again.")}finally{b(!1)}};return e.jsxs("form",{onSubmit:$,className:"space-y-6",children:[e.jsxs("div",{children:[e.jsxs("label",{htmlFor:"currency",className:`block text-sm font-semibold ${t.text} mb-3`,children:["💰 Amount (",g.find(i=>i.code===(l.currency||"ZAR"))?.name||"South African Rand",")"]}),e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none",children:e.jsx("div",{className:`p-1 rounded-lg bg-gradient-to-br ${t.gradient}`,children:e.jsx(c,{className:"h-4 w-4 text-white"})})}),e.jsx("input",{type:"number",id:"currency",value:d,onChange:i=>m(i.target.value),onWheel:S,step:"0.01",min:"0",placeholder:"Enter amount to spend",className:`w-full pl-12 pr-4 py-4 md:py-4 border-4 ${t.border} rounded-xl focus:ring-4 focus:ring-opacity-50 focus:${t.border} ${t.card} ${t.text} ${t.textSecondary} font-bold shadow-lg hover:shadow-xl transition-all duration-200 min-w-0`,required:!0})]}),e.jsx("p",{className:`mt-2 text-xs ${t.textSecondary} opacity-80 font-medium`,children:"💡 Enter the amount you want to spend"})]}),r>0&&e.jsxs("div",{className:"mt-4 space-y-4",children:[e.jsxs("div",{className:`relative overflow-hidden rounded-2xl ${t.card} border ${t.border} p-4 md:p-5 shadow-lg mobile-card-auto`,children:[e.jsx("div",{className:`absolute inset-0 ${t.secondary}`}),e.jsx("div",{className:"relative",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("div",{className:`text-xs font-semibold ${t.textSecondary} tracking-wider uppercase mb-1`,children:"PURCHASE AMOUNT"}),e.jsxs("p",{className:`text-lg md:text-xl font-bold ${t.text} mb-1`,children:[l.currencySymbol||"R",a(r)]}),e.jsxs("p",{className:`text-xs ${t.textSecondary}`,children:["@ ",l.currencySymbol||"R",a(o)," per ",u()]})]}),e.jsx("div",{className:`p-2 md:p-3 rounded-xl bg-gradient-to-br ${t.gradient} shadow-lg ml-3`,children:e.jsx(c,{className:"h-5 md:h-6 w-5 md:w-6 text-white"})})]})}),e.jsx("div",{className:`absolute top-2 right-2 w-4 h-4 bg-gradient-to-r ${t.gradient} rounded-full opacity-20 blur-sm`}),e.jsx("div",{className:`absolute bottom-2 left-2 w-3 h-3 bg-gradient-to-r ${t.gradient} rounded-full opacity-15 blur-sm`})]}),e.jsxs("div",{className:`relative overflow-hidden rounded-2xl ${t.card} border ${t.border} p-4 md:p-5 shadow-lg mobile-card-auto`,children:[e.jsx("div",{className:`absolute inset-0 ${t.secondary}`}),e.jsx("div",{className:"relative",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("div",{className:`text-xs font-semibold ${t.textSecondary} tracking-wider uppercase mb-1`,children:"UNITS TO RECEIVE"}),e.jsx("p",{className:`text-lg md:text-xl font-bold ${t.text} mb-1`,children:a(h)}),e.jsxs("p",{className:`text-xs ${t.textSecondary}`,children:["New Total: ",a(x(l.currentUnits+h))," ",u()]})]}),e.jsx("div",{className:`p-2 md:p-3 rounded-xl bg-gradient-to-br ${t.gradient} shadow-lg ml-3`,children:e.jsx(w,{className:"h-5 md:h-6 w-5 md:w-6 text-white"})})]})}),e.jsx("div",{className:`absolute top-2 right-2 w-4 h-4 bg-gradient-to-r ${t.gradient} rounded-full opacity-20 blur-sm`}),e.jsx("div",{className:`absolute bottom-2 left-2 w-3 h-3 bg-gradient-to-r ${t.gradient} rounded-full opacity-15 blur-sm`})]})]}),e.jsx("button",{type:"submit",disabled:s||r<=0||h<=0||o<=0,className:`w-full ${t.primary} text-white py-4 px-6 rounded-xl font-semibold hover:opacity-90 transition-all duration-300 focus:ring-4 focus:ring-opacity-50 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]`,children:e.jsx("div",{className:"flex items-center justify-center gap-2 text-white",children:s?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent"}),e.jsx("span",{className:"text-white",children:"Adding Purchase..."})]}):e.jsxs(e.Fragment,{children:[e.jsx(c,{className:"h-5 w-5 text-white"}),e.jsx("span",{className:"text-white",children:"Add Purchase"})]})})})]})}function B(){const{state:d,getDisplayUnitName:m}=y(),{theme:s,currentTheme:b}=v(),l=(a,r="bg-gray-800/50")=>b==="dark"?r:a,p=d.purchases.reduce((a,r)=>a+r.currency,0),u=d.purchases.reduce((a,r)=>a+r.units,0),t=a=>{const r=new Date(a),o=r.toLocaleDateString("en-GB"),h=r.toLocaleTimeString("en-GB",{hour:"2-digit",minute:"2-digit",hour12:!1});return{date:o,time:h}},g=e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:`${s.card} rounded-2xl shadow-lg p-4 md:p-8 border ${s.border} ${l("bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50","bg-gray-800/50")} h-fit w-full`,children:[e.jsxs("h2",{className:`text-2xl font-semibold ${s.text} mb-6 flex items-center gap-3`,children:[e.jsx("div",{className:"p-3 rounded-xl bg-gradient-to-br from-blue-500 to-indigo-600 shadow-md",children:e.jsx(c,{className:"h-6 w-6 text-white"})}),"Add New Purchase"]}),e.jsx("div",{className:`${l("bg-white/60","bg-gray-700/50")} backdrop-blur-sm rounded-xl p-4 md:p-6 w-full`,children:e.jsx(C,{})})]}),e.jsx(P,{className:"my-4"}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:`${s.card} rounded-xl shadow-lg p-4 border ${s.border} hover:shadow-xl transition-all duration-300`,children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:`p-2 rounded-lg bg-gradient-to-br ${s.gradient} shadow-lg`,children:e.jsx(c,{className:"h-5 w-5 text-white"})}),e.jsxs("div",{className:"flex-1",children:[e.jsx("p",{className:`text-sm font-medium ${s.textSecondary} opacity-80 mb-2`,children:"Total Spent"}),e.jsxs("p",{className:`text-lg font-bold ${s.text} mb-2`,children:[d.currencySymbol||"R",p.toFixed(2)]})]})]}),e.jsx("div",{className:"text-right",children:e.jsx("p",{className:"text-xs text-emerald-500 font-medium",children:"All Purchases"})})]})}),e.jsx("div",{className:`${s.card} rounded-xl shadow-lg p-4 border ${s.border} hover:shadow-xl transition-all duration-300`,children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:`p-2 rounded-lg bg-gradient-to-br ${s.gradient} shadow-lg`,children:e.jsx(w,{className:"h-5 w-5 text-white"})}),e.jsxs("div",{className:"flex-1",children:[e.jsxs("p",{className:`text-sm font-medium ${s.textSecondary} opacity-80 mb-1`,children:["Total ",m()," Purchased"]}),e.jsx("p",{className:`text-lg font-bold ${s.text} mb-1`,children:u.toFixed(2)})]})]}),e.jsx("div",{className:"text-right",children:e.jsx("p",{className:`text-xs ${s.textSecondary} font-medium`,children:m()})})]})}),e.jsx("div",{className:`${s.card} rounded-xl shadow-lg p-4 border ${s.border} hover:shadow-xl transition-all duration-300`,children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:`p-2 rounded-lg bg-gradient-to-br ${s.gradient} shadow-lg`,children:e.jsx(f,{className:"h-5 w-5 text-white"})}),e.jsxs("div",{className:"flex-1",children:[e.jsx("p",{className:`text-sm font-medium ${s.textSecondary} opacity-80 mb-1`,children:"Total Purchases"}),e.jsx("p",{className:`text-lg font-bold ${s.text} mb-1`,children:d.purchases.length})]})]}),e.jsx("div",{className:"text-right",children:e.jsx("p",{className:"text-xs text-violet-500 font-medium",children:"Transactions"})})]})})]})]}),x=e.jsxs("div",{className:`${s.card} rounded-2xl shadow-lg p-6 border ${s.border} ${l("bg-gradient-to-br from-emerald-50 via-green-50 to-teal-50","bg-gray-800/50")} flex flex-col`,children:[e.jsxs("h2",{className:`text-xl font-semibold ${s.text} mb-4 flex items-center gap-3`,children:[e.jsx("div",{className:`p-2 rounded-xl bg-gradient-to-br ${s.gradient} shadow-md`,children:e.jsx(f,{className:"h-5 w-5 text-white"})}),"Recent Purchases"]}),e.jsxs("div",{className:"space-y-3 flex-1 overflow-y-auto",children:[d.purchases.slice(0,10).map(a=>e.jsx("div",{className:`p-4 ${s.secondary} border ${s.border} rounded-xl shadow-sm hover:shadow-md transition-all duration-200`,children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:`p-2 rounded-lg bg-gradient-to-br ${s.gradient} shadow-sm`,children:e.jsx(c,{className:"h-4 w-4 text-white"})}),e.jsxs("div",{className:"ml-3",children:[e.jsxs("p",{className:`text-sm font-semibold ${s.text}`,children:[d.currencySymbol||"R",a.currency.toFixed(2)]}),e.jsxs("div",{className:`text-xs ${s.textSecondary}`,children:[e.jsx("p",{children:t(a.timestamp).date}),e.jsx("p",{children:t(a.timestamp).time})]})]})]}),e.jsxs("span",{className:`text-sm font-semibold ${s.text}`,children:["+",a.units.toFixed(2)," ",m()]})]})},a.id)),d.purchases.length===0&&e.jsxs("div",{className:`text-center py-8 ${s.secondary} border ${s.border} rounded-xl`,children:[e.jsx("div",{className:`p-3 rounded-2xl bg-gradient-to-br ${s.gradient} w-fit mx-auto mb-3`,children:e.jsx(c,{className:"h-8 w-8 text-white"})}),e.jsx("p",{className:`text-sm ${s.textSecondary} font-medium`,children:"No purchases yet"}),e.jsx("p",{className:`text-xs ${s.textSecondary} mt-1`,children:"Add your first purchase to get started"})]})]})]});return e.jsx("div",{className:"w-full space-y-6 pb-6",children:e.jsx(R,{leftContent:g,rightContent:x,rightContentTitle:"Recent Purchases",className:""})})}export{B as default};
