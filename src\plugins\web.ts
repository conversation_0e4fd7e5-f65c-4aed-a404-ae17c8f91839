import { WebPlugin } from '@capacitor/core'
import type { AdMobNativePlugin } from './admob-native'

export class AdMobNativeWeb extends WebPlugin implements AdMobNativePlugin {
  async initialize(): Promise<{ success: boolean; error?: string }> {
    console.log('AdMobNative Web: initialize called')
    return { success: true }
  }

  async loadNativeAd(options: { adUnitId: string }): Promise<{
    success: boolean
    error?: string
    adData?: any
  }> {
    console.log('AdMobNative Web: loadNativeAd called with', options)
    // Return mock data for web development
    return {
      success: true,
      adData: {
        headline: 'Sample Ad Headline',
        body: 'This is a sample ad body for web development.',
        callToAction: 'Learn More',
        advertiser: 'Sample Advertiser'
      }
    }
  }
}
