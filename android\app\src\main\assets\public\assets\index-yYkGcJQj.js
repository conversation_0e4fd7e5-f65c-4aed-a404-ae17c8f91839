const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["js/web-DyfutkOC.js","js/vendor-C67cHu0f.js","js/utils-CgIdLkdF.js","js/icons-Bhz3yUky.js","js/Dashboard-Bb25e2n7.js","js/charts-B70j_Dbf.js","js/NativeAd-Bvxt7ydf.js","css/NativeAd-C3JNXfbY.css","js/SwipeableLayout-DZXQaYh-.js","js/Purchases-D7OohU2F.js","js/Usage-OzkL3q8n.js","js/ScrollHint-CEiMHnNb.js","js/History-DMxJ8V99.js","js/Settings-kYCO9Y8I.js","js/web-DrpM_nhU.js","js/web-LmdFvVZH.js"])))=>i.map(i=>d[i]);
import{r as h,a as je,u as W,N as B,b as ge,c as Se,i as Ee,d as ke,e as I,B as Pe}from"../js/vendor-C67cHu0f.js";import{f as X}from"../js/utils-CgIdLkdF.js";import{H as Te,a as Ce,b as he,c as $e,d as Le,e as pe,f as Y,g as Q,h as ee,i as De,j as Re,k as Ue,l as Ae,m as Ie}from"../js/icons-Bhz3yUky.js";(function(){const e=document.createElement("link").relList;if(e&&e.supports&&e.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))a(o);new MutationObserver(o=>{for(const s of o)if(s.type==="childList")for(const c of s.addedNodes)c.tagName==="LINK"&&c.rel==="modulepreload"&&a(c)}).observe(document,{childList:!0,subtree:!0});function n(o){const s={};return o.integrity&&(s.integrity=o.integrity),o.referrerPolicy&&(s.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?s.credentials="include":o.crossOrigin==="anonymous"?s.credentials="omit":s.credentials="same-origin",s}function a(o){if(o.ep)return;o.ep=!0;const s=n(o);fetch(o.href,s)}})();var be={exports:{}},G={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Oe=h,_e=Symbol.for("react.element"),He=Symbol.for("react.fragment"),Fe=Object.prototype.hasOwnProperty,Me=Oe.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Be={key:!0,ref:!0,__self:!0,__source:!0};function xe(r,e,n){var a,o={},s=null,c=null;n!==void 0&&(s=""+n),e.key!==void 0&&(s=""+e.key),e.ref!==void 0&&(c=e.ref);for(a in e)Fe.call(e,a)&&!Be.hasOwnProperty(a)&&(o[a]=e[a]);if(r&&r.defaultProps)for(a in e=r.defaultProps,e)o[a]===void 0&&(o[a]=e[a]);return{$$typeof:_e,type:r,key:s,ref:c,props:o,_owner:Me.current}}G.Fragment=He;G.jsx=xe;G.jsxs=xe;be.exports=G;var t=be.exports,ye,te=je;ye=te.createRoot,te.hydrateRoot;const qe="modulepreload",ze=function(r){return"/"+r},re={},U=function(e,n,a){let o=Promise.resolve();if(n&&n.length>0){document.getElementsByTagName("link");const c=document.querySelector("meta[property=csp-nonce]"),u=c?.nonce||c?.getAttribute("nonce");o=Promise.allSettled(n.map(i=>{if(i=ze(i),i in re)return;re[i]=!0;const f=i.endsWith(".css"),p=f?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${i}"]${p}`))return;const l=document.createElement("link");if(l.rel=f?"stylesheet":qe,f||(l.as="script"),l.crossOrigin="",l.href=i,u&&l.setAttribute("nonce",u),document.head.appendChild(l),f)return new Promise((g,b)=>{l.addEventListener("load",g),l.addEventListener("error",()=>b(new Error(`Unable to preload CSS for ${i}`)))})}))}function s(c){const u=new Event("vite:preloadError",{cancelable:!0});if(u.payload=c,window.dispatchEvent(u),!u.defaultPrevented)throw c}return o.then(c=>{for(const u of c||[])u.status==="rejected"&&s(u.reason);return e().catch(s)})};/*! Capacitor: https://capacitorjs.com/ - MIT License */var _;(function(r){r.Unimplemented="UNIMPLEMENTED",r.Unavailable="UNAVAILABLE"})(_||(_={}));class K extends Error{constructor(e,n,a){super(e),this.message=e,this.code=n,this.data=a}}const We=r=>{var e,n;return r?.androidBridge?"android":!((n=(e=r?.webkit)===null||e===void 0?void 0:e.messageHandlers)===null||n===void 0)&&n.bridge?"ios":"web"},Ge=r=>{const e=r.CapacitorCustomPlatform||null,n=r.Capacitor||{},a=n.Plugins=n.Plugins||{},o=()=>e!==null?e.name:We(r),s=()=>o()!=="web",c=l=>{const g=f.get(l);return!!(g?.platforms.has(o())||u(l))},u=l=>{var g;return(g=n.PluginHeaders)===null||g===void 0?void 0:g.find(b=>b.name===l)},i=l=>r.console.error(l),f=new Map,p=(l,g={})=>{const b=f.get(l);if(b)return console.warn(`Capacitor plugin "${l}" already registered. Cannot register plugins twice.`),b.proxy;const w=o(),y=u(l);let x;const $=async()=>(!x&&w in g?x=typeof g[w]=="function"?x=await g[w]():x=g[w]:e!==null&&!x&&"web"in g&&(x=typeof g.web=="function"?x=await g.web():x=g.web),x),T=(d,m)=>{var N,E;if(y){const C=y?.methods.find(v=>m===v.name);if(C)return C.rtype==="promise"?v=>n.nativePromise(l,m.toString(),v):(v,F)=>n.nativeCallback(l,m.toString(),v,F);if(d)return(N=d[m])===null||N===void 0?void 0:N.bind(d)}else{if(d)return(E=d[m])===null||E===void 0?void 0:E.bind(d);throw new K(`"${l}" plugin is not implemented on ${w}`,_.Unimplemented)}},j=d=>{let m;const N=(...E)=>{const C=$().then(v=>{const F=T(v,d);if(F){const M=F(...E);return m=M?.remove,M}else throw new K(`"${l}.${d}()" is not implemented on ${w}`,_.Unimplemented)});return d==="addListener"&&(C.remove=async()=>m()),C};return N.toString=()=>`${d.toString()}() { [capacitor code] }`,Object.defineProperty(N,"name",{value:d,writable:!1,configurable:!1}),N},D=j("addListener"),R=j("removeListener"),A=(d,m)=>{const N=D({eventName:d},m),E=async()=>{const v=await N;R({eventName:d,callbackId:v},m)},C=new Promise(v=>N.then(()=>v({remove:E})));return C.remove=async()=>{console.warn("Using addListener() without 'await' is deprecated."),await E()},C},S=new Proxy({},{get(d,m){switch(m){case"$$typeof":return;case"toJSON":return()=>({});case"addListener":return y?A:D;case"removeListener":return R;default:return j(m)}}});return a[l]=S,f.set(l,{name:l,proxy:S,platforms:new Set([...Object.keys(g),...y?[w]:[]])}),S};return n.convertFileSrc||(n.convertFileSrc=l=>l),n.getPlatform=o,n.handleError=i,n.isNativePlatform=s,n.isPluginAvailable=c,n.registerPlugin=p,n.Exception=K,n.DEBUG=!!n.DEBUG,n.isLoggingEnabled=!!n.isLoggingEnabled,n},Ke=r=>r.Capacitor=Ge(r),P=Ke(typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{}),H=P.registerPlugin;class we{constructor(){this.listeners={},this.retainedEventArguments={},this.windowListeners={}}addListener(e,n){let a=!1;this.listeners[e]||(this.listeners[e]=[],a=!0),this.listeners[e].push(n);const s=this.windowListeners[e];s&&!s.registered&&this.addWindowListener(s),a&&this.sendRetainedArgumentsForEvent(e);const c=async()=>this.removeListener(e,n);return Promise.resolve({remove:c})}async removeAllListeners(){this.listeners={};for(const e in this.windowListeners)this.removeWindowListener(this.windowListeners[e]);this.windowListeners={}}notifyListeners(e,n,a){const o=this.listeners[e];if(!o){if(a){let s=this.retainedEventArguments[e];s||(s=[]),s.push(n),this.retainedEventArguments[e]=s}return}o.forEach(s=>s(n))}hasListeners(e){return!!this.listeners[e].length}registerWindowListener(e,n){this.windowListeners[n]={registered:!1,windowEventName:e,pluginEventName:n,handler:a=>{this.notifyListeners(n,a)}}}unimplemented(e="not implemented"){return new P.Exception(e,_.Unimplemented)}unavailable(e="not available"){return new P.Exception(e,_.Unavailable)}async removeListener(e,n){const a=this.listeners[e];if(!a)return;const o=a.indexOf(n);this.listeners[e].splice(o,1),this.listeners[e].length||this.removeWindowListener(this.windowListeners[e])}addWindowListener(e){window.addEventListener(e.windowEventName,e.handler),e.registered=!0}removeWindowListener(e){e&&(window.removeEventListener(e.windowEventName,e.handler),e.registered=!1)}sendRetainedArgumentsForEvent(e){const n=this.retainedEventArguments[e];n&&(delete this.retainedEventArguments[e],n.forEach(a=>{this.notifyListeners(e,a)}))}}const ne=r=>encodeURIComponent(r).replace(/%(2[346B]|5E|60|7C)/g,decodeURIComponent).replace(/[()]/g,escape),oe=r=>r.replace(/(%[\dA-F]{2})+/gi,decodeURIComponent);class Ve extends we{async getCookies(){const e=document.cookie,n={};return e.split(";").forEach(a=>{if(a.length<=0)return;let[o,s]=a.replace(/=/,"CAP_COOKIE").split("CAP_COOKIE");o=oe(o).trim(),s=oe(s).trim(),n[o]=s}),n}async setCookie(e){try{const n=ne(e.key),a=ne(e.value),o=`; expires=${(e.expires||"").replace("expires=","")}`,s=(e.path||"/").replace("path=",""),c=e.url!=null&&e.url.length>0?`domain=${e.url}`:"";document.cookie=`${n}=${a||""}${o}; path=${s}; ${c};`}catch(n){return Promise.reject(n)}}async deleteCookie(e){try{document.cookie=`${e.key}=; Max-Age=0`}catch(n){return Promise.reject(n)}}async clearCookies(){try{const e=document.cookie.split(";")||[];for(const n of e)document.cookie=n.replace(/^ +/,"").replace(/=.*/,`=;expires=${new Date().toUTCString()};path=/`)}catch(e){return Promise.reject(e)}}async clearAllCookies(){try{await this.clearCookies()}catch(e){return Promise.reject(e)}}}H("CapacitorCookies",{web:()=>new Ve});const Ye=async r=>new Promise((e,n)=>{const a=new FileReader;a.onload=()=>{const o=a.result;e(o.indexOf(",")>=0?o.split(",")[1]:o)},a.onerror=o=>n(o),a.readAsDataURL(r)}),Je=(r={})=>{const e=Object.keys(r);return Object.keys(r).map(o=>o.toLocaleLowerCase()).reduce((o,s,c)=>(o[s]=r[e[c]],o),{})},Ze=(r,e=!0)=>r?Object.entries(r).reduce((a,o)=>{const[s,c]=o;let u,i;return Array.isArray(c)?(i="",c.forEach(f=>{u=e?encodeURIComponent(f):f,i+=`${s}=${u}&`}),i.slice(0,-1)):(u=e?encodeURIComponent(c):c,i=`${s}=${u}`),`${a}&${i}`},"").substr(1):null,Xe=(r,e={})=>{const n=Object.assign({method:r.method||"GET",headers:r.headers},e),o=Je(r.headers)["content-type"]||"";if(typeof r.data=="string")n.body=r.data;else if(o.includes("application/x-www-form-urlencoded")){const s=new URLSearchParams;for(const[c,u]of Object.entries(r.data||{}))s.set(c,u);n.body=s.toString()}else if(o.includes("multipart/form-data")||r.data instanceof FormData){const s=new FormData;if(r.data instanceof FormData)r.data.forEach((u,i)=>{s.append(i,u)});else for(const u of Object.keys(r.data))s.append(u,r.data[u]);n.body=s;const c=new Headers(n.headers);c.delete("content-type"),n.headers=c}else(o.includes("application/json")||typeof r.data=="object")&&(n.body=JSON.stringify(r.data));return n};class Qe extends we{async request(e){const n=Xe(e,e.webFetchExtra),a=Ze(e.params,e.shouldEncodeUrlParams),o=a?`${e.url}?${a}`:e.url,s=await fetch(o,n),c=s.headers.get("content-type")||"";let{responseType:u="text"}=s.ok?e:{};c.includes("application/json")&&(u="json");let i,f;switch(u){case"arraybuffer":case"blob":f=await s.blob(),i=await Ye(f);break;case"json":i=await s.json();break;case"document":case"text":default:i=await s.text()}const p={};return s.headers.forEach((l,g)=>{p[g]=l}),{data:i,headers:p,status:s.status,url:s.url}}async get(e){return this.request(Object.assign(Object.assign({},e),{method:"GET"}))}async post(e){return this.request(Object.assign(Object.assign({},e),{method:"POST"}))}async put(e){return this.request(Object.assign(Object.assign({},e),{method:"PUT"}))}async patch(e){return this.request(Object.assign(Object.assign({},e),{method:"PATCH"}))}async delete(e){return this.request(Object.assign(Object.assign({},e),{method:"DELETE"}))}}H("CapacitorHttp",{web:()=>new Qe});var se;(function(r){r[r.Sunday=1]="Sunday",r[r.Monday=2]="Monday",r[r.Tuesday=3]="Tuesday",r[r.Wednesday=4]="Wednesday",r[r.Thursday=5]="Thursday",r[r.Friday=6]="Friday",r[r.Saturday=7]="Saturday"})(se||(se={}));const k=H("LocalNotifications",{web:()=>U(()=>import("../js/web-DyfutkOC.js"),__vite__mapDeps([0,1,2,3])).then(r=>new r.LocalNotificationsWeb)}),et=(r,e,n,a)=>{const o=h.useRef(null),s=h.useRef(!1);return console.log("🔔 useNotifications hook called with:",{enabled:r,time:e,lastDate:n,platform:typeof window<"u"?"web":"unknown"}),h.useEffect(()=>{if(console.log("🔔 useNotifications useEffect triggered with:",{enabled:r,time:e,lastDate:n}),o.current&&(console.log("🔔 Clearing existing interval:",o.current),clearInterval(o.current),o.current=null),!r){console.log("🔔 Notifications disabled, cleaning up and exiting");return}const i=async()=>{try{console.log("Notifications: Setting up for platform:",P.getPlatform()),P.isNativePlatform()?(console.log("Notifications: Native platform detected"),await f()):(console.log("Notifications: Web platform detected, using fallback"),l())}catch(b){console.error("Notifications: Setup error:",b),l()}},f=async()=>{try{console.log("Notifications: Requesting permissions...");const b=await k.requestPermissions();if(console.log("Notifications: Permission result:",b),b.display!=="granted"){console.log("Notifications: Permission denied");return}console.log("Notifications: Permission granted, setting up daily reminder"),await p(),s.current||(await g(),s.current=!0)}catch(b){throw console.error("Notifications: Native setup error:",b),b}},p=async()=>{try{console.log("Notifications: Scheduling daily notification...");try{await k.cancel({notifications:[{id:1}]}),console.log("Notifications: Cancelled existing notifications")}catch{console.log("Notifications: No existing notifications to cancel")}const[b,w]=e.split(":").map(Number);console.log("Notifications: Parsed time:",b,":",w);const y=new Date,x=new Date;x.setHours(b,w,0,0),x<=y&&(x.setDate(x.getDate()+1),console.log("Notifications: Time has passed today, scheduling for tomorrow")),console.log("Notifications: Scheduling for:",x.toLocaleString());const $={notifications:[{title:"⚡ Prepaid Meter Reminder",body:"Don't forget to record your electricity usage today!",id:1,schedule:{at:x,repeats:!0,every:"day"},sound:"default",attachments:null,actionTypeId:"",extra:{type:"daily_reminder"}}]};await k.schedule($),console.log("Notifications: Daily reminder scheduled successfully for",x.toLocaleString());const T=await k.getPending();console.log("Notifications: Pending notifications:",T.notifications.length)}catch(b){throw console.error("Notifications: Scheduling error:",b),b}},l=()=>{console.log("Notifications: Setting up web notifications"),(async()=>{if("Notification"in window){if(console.log("🔔 Current permission status:",Notification.permission),Notification.permission==="default"){console.log("🔔 Requesting notification permission...");try{const x=await Notification.requestPermission();return console.log("🔔 Permission request result:",x),x==="granted"}catch(x){return console.error("🔔 Permission request failed:",x),!1}}const y=Notification.permission==="granted";return console.log("🔔 Permission already set, granted:",y),y}return console.log("🔔 Notifications not supported in this browser"),!1})().then(y=>{console.log("🔔 Final permission status:",y?"GRANTED":"DENIED"),y||console.warn("🔔 ⚠️ Notifications will not work - permission denied or not supported")});const w=()=>{const y=new Date;let x,$;if(typeof e=="string")[x,$]=e.split(":").map(Number);else if(e&&typeof e=="object")x=e.hours,$=e.minutes;else{console.error("🔔 Invalid notificationTime format:",e);return}const T=new Date;T.setHours(x,$,0,0);const j=new Date(T);j.setMinutes(j.getMinutes()-2);const D=new Date(T);D.setMinutes(D.getMinutes()+3);const R=y.toDateString(),A=n?new Date(n).toDateString():null;console.log("🔔 DEBUG RAW VALUES:",{lastNotificationDate:n,lastNotificationDateType:typeof n,lastNotificationDateStr:A,today:R,comparison:A!==R});const S=y>=j&&y<=D,d=A!==R;if(console.log("🔔 WEB NOTIFICATION CHECK:",{currentTime:y.toLocaleTimeString(),targetTime:T.toLocaleTimeString(),windowStart:j.toLocaleTimeString(),windowEnd:D.toLocaleTimeString(),today:R,lastNotificationDate:A,inWindow:S,notSentToday:d,permission:typeof Notification<"u"?Notification.permission:"not-supported"}),console.log("🔔 CONDITION CHECK:",{inWindow:S,notSentToday:d,bothTrue:S&&d,willTrigger:S&&d}),S&&d)if(console.log("Notifications: Conditions met, attempting to send notification"),"Notification"in window&&Notification.permission==="granted"){console.log("Notifications: Sending web notification");const m=new Notification("⚡ Prepaid Meter Reminder",{body:"Don't forget to record your electricity usage today!",icon:"/favicon.ico",badge:"/favicon.ico",tag:"daily-reminder",requireInteraction:!1,silent:!1});setTimeout(()=>{m.close()},1e4),a({type:"UPDATE_SETTINGS",payload:{lastNotificationDate:y.toISOString()}}),console.log("Notifications: Web notification sent successfully")}else console.log("Notifications: Web notification permission not granted")};console.log("🔔 Starting web notification checker..."),w(),console.log("🔔 Creating interval..."),o.current=setInterval(()=>{console.log("🔔 *** INTERVAL TICK *** Running scheduled notification check at:",new Date().toLocaleTimeString()),w()},3e4),console.log("🔔 Web notification checker started with 30-second interval, intervalId:",o.current),setTimeout(()=>{console.log("🔔 5-second test: Interval should be running...")},5e3)},g=async()=>{if(P.isNativePlatform())try{console.log("Notifications: Setting up listeners..."),await k.removeAllListeners(),await k.addListener("localNotificationActionPerformed",b=>{console.log("Notification action performed:",b),a({type:"UPDATE_SETTINGS",payload:{lastNotificationDate:new Date().toISOString()}})}),await k.addListener("localNotificationReceived",b=>{console.log("Notification received in foreground:",b),a({type:"UPDATE_SETTINGS",payload:{lastNotificationDate:new Date().toISOString()}})}),console.log("Notifications: Listeners setup complete")}catch(b){console.error("Notifications: Listener setup error:",b)}};return i(),()=>{console.log("Notifications: Cleaning up..."),o.current&&(clearInterval(o.current),o.current=null),P.isNativePlatform()&&s.current&&(k.removeAllListeners(),s.current=!1)}},[r,e,n,a]),{testNotification:async()=>{try{if(console.log("🔔 TEST NOTIFICATION FUNCTION CALLED"),alert("Test notification function called! Check console for details."),!P.isNativePlatform()){if(console.log("🌐 Testing web notification on browser"),!("Notification"in window))return console.error("❌ Web notifications not supported in this browser"),alert("Web notifications not supported in this browser"),!1;if(console.log("🔔 Current notification permission:",Notification.permission),Notification.permission==="default"){console.log("🔔 Requesting notification permission...");const p=await Notification.requestPermission();console.log("🔔 Permission request result:",p)}if(Notification.permission==="granted"){console.log("✅ Permission granted, creating notification...");try{const p=new Notification("⚡ Test Notification",{body:"This is a test notification from your Prepaid Meter app!",icon:"/favicon.ico",tag:"test-notification",requireInteraction:!1});return console.log("✅ Notification created successfully"),setTimeout(()=>{p.close(),console.log("🔔 Notification auto-closed")},5e3),!0}catch(p){return console.error("❌ Error creating notification:",p),alert("Error creating notification: "+p.message),!1}}else return console.log("❌ Notification permission denied or blocked"),alert("Notification permission denied. Please enable notifications in your browser settings."),!1}console.log("Notifications: Testing native notification");const i=await k.requestPermissions();if(console.log("Notifications: Native permission result:",i),i.display!=="granted")return console.log("Test notification: Native permission denied"),!1;try{await k.cancel({notifications:[{id:999}]})}catch{}const f={notifications:[{title:"⚡ Test Notification",body:"This is a test notification from your Prepaid Meter app!",id:999,schedule:{at:new Date(Date.now()+2e3)},sound:"default",attachments:null,actionTypeId:"",extra:{type:"test_notification"}}]};return await k.schedule(f),console.log("Notifications: Test notification scheduled for 2 seconds from now"),!0}catch(i){return console.error("Test notification error:",i),!1}},forceNotification:async()=>{try{if(console.log("🔔 FORCE NOTIFICATION CALLED - ignoring time window"),alert("🔔 Force notification function started! Check console for details."),!("Notification"in window))return console.error("❌ Web notifications not supported"),alert("❌ Web notifications not supported in this browser"),!1;if(console.log("🔔 Current permission status:",Notification.permission),Notification.permission!=="granted"){console.log("🔔 Requesting permission..."),alert("🔔 Requesting notification permission...");const f=await Notification.requestPermission();if(console.log("🔔 Permission result:",f),f!=="granted")return console.log("❌ Permission denied"),alert("❌ Permission denied: "+f),!1}console.log("✅ Permission granted, creating forced notification..."),alert("✅ Permission OK, creating notification now...");const i=new Notification("⚡ FORCED Test Notification",{body:"This notification was forced regardless of time window! If you see this, notifications are working!",icon:"/favicon.ico",tag:"force-test-"+Date.now(),requireInteraction:!1,silent:!1});return console.log("✅ Notification object created:",i),i.onshow=()=>{console.log("✅ Notification shown successfully"),alert("✅ Notification shown! Check your notification area.")},i.onerror=f=>{console.error("❌ Notification error:",f),alert("❌ Notification error: "+f)},i.onclick=()=>{console.log("🔔 Notification clicked"),i.close()},setTimeout(()=>{i.close(),console.log("🔔 Notification auto-closed after 8 seconds")},8e3),a({type:"UPDATE_SETTINGS",payload:{lastNotificationDate:new Date().toISOString()}}),console.log("✅ Forced notification process completed"),!0}catch(i){return console.error("❌ Force notification error:",i),alert("❌ Force notification error: "+i.message),!1}}}};function tt(r,e=null){try{const n=localStorage.getItem(r);return n?JSON.parse(n):e}catch(n){return console.error(`Error reading from localStorage key "${r}":`,n),e}}function rt(r,e){try{return localStorage.setItem(r,JSON.stringify(e)),!0}catch(n){return console.error(`Error writing to localStorage key "${r}":`,n),!1}}function nt(r){try{return localStorage.removeItem(r),!0}catch(e){return console.error(`Error removing localStorage key "${r}":`,e),!1}}const ve=h.createContext(),J={version:"1.3.0",currentUnits:0,previousUnits:0,unitCost:0,thresholdLimit:0,currency:"ZAR",currencySymbol:"R",customCurrencyName:"",customCurrencySymbol:"",unitName:"kWh",customUnitName:"",purchases:[],usageHistory:[],isInitialized:!1,lastResetDate:null,lastMonthlyReset:null,notificationsEnabled:!1,notificationTime:"18:00",lastNotificationDate:null};function ot(r,e){switch(e.type){case"INITIALIZE_APP":return{...r,currentUnits:e.payload.initialUnits,previousUnits:e.payload.initialUnits,unitCost:e.payload.unitCost,isInitialized:!0,lastResetDate:new Date().toISOString()};case"ADD_PURCHASE":{const n={id:Date.now(),date:new Date().toISOString(),currency:e.payload.currency,units:e.payload.units,unitCost:r.unitCost,timestamp:X(new Date,"yyyy-MM-dd HH:mm:ss")};return{...r,purchases:[n,...r.purchases],currentUnits:r.currentUnits+e.payload.units}}case"UPDATE_USAGE":{const n={id:Date.now(),date:new Date().toISOString(),previousUnits:r.currentUnits,currentUnits:e.payload.currentUnits,usage:r.currentUnits-e.payload.currentUnits,timestamp:X(new Date,"yyyy-MM-dd HH:mm:ss")};return{...r,previousUnits:r.currentUnits,currentUnits:e.payload.currentUnits,usageHistory:[n,...r.usageHistory]}}case"UPDATE_SETTINGS":return{...r,...e.payload};case"FACTORY_RESET":return{...J};case"DASHBOARD_RESET":return{...r,currentUnits:0,previousUnits:0,lastResetDate:new Date().toISOString()};case"MONTHLY_RESET":return{...r,usageHistory:[],lastMonthlyReset:new Date().toISOString()};case"LOAD_STATE":return{...r,...e.payload};default:return console.warn(`Unknown action type: ${e.type}`),r}}function st({children:r}){const[e,n]=h.useReducer(ot,J);h.useEffect(()=>{const d="prepaid-meter-app-v1.3",m=tt(d);if(m){if(!m.version||m.version!==J.version){console.log("Version mismatch detected, clearing old data"),nt(d);return}n({type:"LOAD_STATE",payload:m})}},[]),h.useEffect(()=>{rt("prepaid-meter-app-v1.3",e)||console.warn("Failed to save app state - data may not persist")},[e]),h.useEffect(()=>{const d=new Date,m=d.getMonth(),N=d.getFullYear();if(e.lastMonthlyReset){const E=new Date(e.lastMonthlyReset),C=E.getMonth(),v=E.getFullYear();(N>v||N===v&&m>C)&&n({type:"MONTHLY_RESET"})}else e.isInitialized&&n({type:"UPDATE_SETTINGS",payload:{lastMonthlyReset:d.toISOString()}})},[e.lastMonthlyReset,e.isInitialized]);const{testNotification:a,forceNotification:o}=et(e.notificationsEnabled&&e.isInitialized,e.notificationTime,e.lastNotificationDate,n),s=e.usageHistory.length>0?e.previousUnits-e.currentUnits:0,c=e.currentUnits<=e.thresholdLimit&&e.thresholdLimit>0,u=e.purchases.reduce((d,m)=>d+m.currency,0),i=e.usageHistory.reduce((d,m)=>d+m.usage,0),f=new Date,p=new Date(f.getFullYear(),f.getMonth(),f.getDate()-f.getDay()),l=new Date(f.getFullYear(),f.getMonth(),1),g=e.purchases.filter(d=>new Date(d.date)>=p),b=e.purchases.filter(d=>new Date(d.date)>=l),w=e.usageHistory.filter(d=>new Date(d.date)>=p),y=e.usageHistory.filter(d=>new Date(d.date)>=l),x=g.reduce((d,m)=>d+m.currency,0),$=b.reduce((d,m)=>d+m.currency,0),T=w.reduce((d,m)=>d+m.usage,0),j=y.reduce((d,m)=>d+m.usage,0),S={state:e,dispatch:n,initializeApp:(d,m)=>{n({type:"INITIALIZE_APP",payload:{initialUnits:d,unitCost:m}})},addPurchase:(d,m)=>{n({type:"ADD_PURCHASE",payload:{currency:d,units:m}})},updateUsage:d=>{n({type:"UPDATE_USAGE",payload:{currentUnits:d}})},updateSettings:d=>{n({type:"UPDATE_SETTINGS",payload:d})},factoryReset:()=>{n({type:"FACTORY_RESET"})},dashboardReset:()=>{n({type:"DASHBOARD_RESET"})},usageSinceLastRecording:s,isThresholdExceeded:c,totalPurchases:u,totalUnitsUsed:i,getDisplayUnitName:()=>e.unitName==="custom"?e.customUnitName||"Units":e.unitName,getDisplayCurrencySymbol:()=>e.currency==="CUSTOM"?e.customCurrencySymbol||"C":e.currencySymbol||"R",getDisplayCurrencyName:()=>e.currency==="CUSTOM"?e.customCurrencyName||"Custom Currency":[{code:"ZAR",name:"South African Rand"},{code:"USD",name:"US Dollar"},{code:"EUR",name:"Euro"},{code:"GBP",name:"British Pound"},{code:"JPY",name:"Japanese Yen"}].find(m=>m.code===e.currency)?.name||"Unknown Currency",testNotification:a,forceNotification:o,weeklyPurchaseTotal:x,monthlyPurchaseTotal:$,weeklyUsageTotal:T,monthlyUsageTotal:j,weeklyPurchases:g,monthlyPurchases:b,weeklyUsage:w,monthlyUsage:y};return t.jsx(ve.Provider,{value:S,children:r})}function Z(){const r=h.useContext(ve);if(!r)throw new Error("useApp must be used within an AppProvider");return r}const Ne=h.createContext(),q={electric:{name:"Electric Blue",primary:"bg-blue-700",secondary:"bg-blue-50",accent:"bg-blue-500",background:"bg-blue-25",text:"text-blue-900",textSecondary:"text-blue-700",border:"border-blue-200",card:"bg-blue-50",gradient:"from-blue-500 to-blue-700",light:"bg-blue-100",lighter:"bg-blue-50",dark:"bg-blue-700",darker:"bg-blue-800"},dark:{name:"Dark Mode",primary:"bg-gray-700",secondary:"bg-gray-700",accent:"bg-gray-500",background:"bg-gray-900",text:"text-white",textSecondary:"text-gray-300",border:"border-gray-600",card:"bg-gray-800",gradient:"from-gray-600 to-gray-800",light:"bg-gray-700",lighter:"bg-gray-600",dark:"bg-gray-800",darker:"bg-gray-900"},green:{name:"Eco Green",primary:"bg-green-700",secondary:"bg-green-50",accent:"bg-green-500",background:"bg-green-25",text:"text-green-900",textSecondary:"text-green-700",border:"border-green-200",card:"bg-green-50",gradient:"from-green-500 to-green-700",light:"bg-green-100",lighter:"bg-green-50",dark:"bg-green-700",darker:"bg-green-800"},teal:{name:"Ocean Teal",primary:"bg-teal-700",secondary:"bg-teal-50",accent:"bg-teal-500",background:"bg-teal-25",text:"text-teal-900",textSecondary:"text-teal-700",border:"border-teal-200",card:"bg-teal-50",gradient:"from-teal-500 to-teal-700",light:"bg-teal-100",lighter:"bg-teal-50",dark:"bg-teal-700",darker:"bg-teal-800"},pink:{name:"Rose Pink",primary:"bg-pink-700",secondary:"bg-pink-50",accent:"bg-pink-500",background:"bg-pink-25",text:"text-pink-900",textSecondary:"text-pink-700",border:"border-pink-200",card:"bg-pink-50",gradient:"from-pink-500 to-pink-700",light:"bg-pink-100",lighter:"bg-pink-50",dark:"bg-pink-700",darker:"bg-pink-800"}};function it({children:r}){const[e,n]=h.useState("electric");h.useEffect(()=>{console.log("FORCING THEME RESET - Clearing all theme data"),["prepaid-meter-theme","prepaid-meter-theme-v1.2","prepaid-meter-theme-v1.3"].forEach(s=>{localStorage.removeItem(s),console.log(`Cleared theme key: ${s}`)}),n("electric"),console.log("Theme reset to default: electric")},[]),h.useEffect(()=>{localStorage.setItem("prepaid-meter-theme-v1.3",e)},[e]);const a={currentTheme:e,setCurrentTheme:n,theme:q[e],themes:q};return t.jsx(Ne.Provider,{value:a,children:t.jsx("div",{className:`${q[e].background} ${q[e].text} text-base min-h-screen transition-all duration-300`,style:{fontFamily:"Inter, system-ui, -apple-system, sans-serif",fontSize:"16px"},children:r})})}function L(){const r=h.useContext(Ne);if(!r)throw new Error("useTheme must be used within a ThemeProvider");return r}function at({size:r="md",animated:e=!0,showText:n=!0}){const{theme:a}=L(),[o,s]=h.useState(!1),c={sm:{logo:"h-12 w-12",text:"text-sm",icon:"h-6 w-6"},md:{logo:"h-16 w-16",text:"text-lg",icon:"h-8 w-8"},lg:{logo:"h-20 w-20",text:"text-xl",icon:"h-10 w-10"},xl:{logo:"h-24 w-24",text:"text-2xl",icon:"h-12 w-12"}},u=c[r]||c.md;return t.jsxs("div",{className:"flex items-center gap-3",children:[t.jsx("div",{className:`${u.logo} flex items-center justify-center`,children:o?t.jsx("div",{className:`${u.logo} rounded-2xl bg-gradient-to-br ${a.gradient} shadow-lg flex items-center justify-center ${e?"animate-pulse":""}`,children:t.jsx(Te,{className:`${u.icon} text-white`})}):t.jsx("img",{src:"/Logo Prepaid User.png",alt:"Prepaid User Electricity Logo",className:`${u.logo} object-contain`,onError:()=>s(!0),onLoad:()=>s(!1)})}),n&&t.jsxs("div",{className:"flex flex-col",children:[t.jsx("h1",{className:`${u.text} font-black ${a.text} leading-tight`,children:"Prepaid User"}),t.jsx("p",{className:`text-base font-bold ${a.textSecondary} tracking-wider leading-tight`,children:"Electricity"})]})]})}function ct({onMenuClick:r}){const{theme:e,currentTheme:n}=L(),{state:a}=Z(),o=()=>n==="dark"?"bg-gray-900/95 backdrop-blur-xl border-gray-700/50":{electric:"bg-blue-550/95 backdrop-blur-xl border-white/30",green:"bg-green-550/95 backdrop-blur-xl border-white/30",teal:"bg-teal-550/95 backdrop-blur-xl border-white/30",pink:"bg-pink-550/95 backdrop-blur-xl border-white/30"}[n]||"bg-blue-550/95 backdrop-blur-xl border-white/30",s=()=>n==="dark"?e.text:"text-white drop-shadow-sm";return t.jsxs("header",{className:`${o()} border-b px-4 py-2 flex items-center justify-between shadow-xl`,style:{paddingTop:"calc(env(safe-area-inset-top, 0px) + 0.5rem)"},children:[t.jsxs("div",{className:"flex items-center space-x-2",children:[t.jsx("button",{onClick:r,className:"hamburger-menu lg:hidden p-1 rounded-lg bg-white/15 backdrop-blur-sm text-white hover:bg-white/25 transition-all duration-300 shadow-lg",children:t.jsx(Ce,{className:"h-5 w-5"})}),t.jsx("div",{className:"flex items-center",children:t.jsx(at,{size:"sm",animated:!0,showText:!1})})]}),t.jsxs("div",{className:"flex items-center space-x-2 lg:space-x-3 bg-black/30 backdrop-blur-md rounded-lg px-2 lg:px-3 py-0.5 lg:py-1 border border-white/30 shadow-lg",children:[t.jsxs("div",{className:"text-right",children:[t.jsx("p",{className:`text-sm lg:text-base ${s()}`,children:"Current Units"}),t.jsx("p",{className:`text-base lg:text-lg font-bold ${s()}`,children:a.currentUnits.toFixed(2)})]}),t.jsx("div",{className:"w-2 lg:w-3 h-2 lg:h-3 rounded-full bg-white/80 pulse-glow shadow-sm"})]})]})}const ie=[{name:"Dashboard",href:"/dashboard",icon:he},{name:"Purchases",href:"/purchases",icon:$e},{name:"Usage",href:"/usage",icon:Le},{name:"History",href:"/history",icon:pe}],ae=[{name:"General Settings",href:"/settings?section=general",icon:Y},{name:"Appearance",href:"/settings?section=appearance",icon:De},{name:"Reset Options",href:"/settings?section=reset",icon:Re}];function lt({isOpen:r,onClose:e}){const{theme:n,currentTheme:a}=L(),o=W(),[s,c]=h.useState(!1),u=()=>a==="dark"?"bg-gray-900/95 backdrop-blur-xl border-gray-700/50":{electric:"bg-blue-550/95 backdrop-blur-xl border-white/30",green:"bg-green-550/95 backdrop-blur-xl border-white/30",teal:"bg-teal-550/95 backdrop-blur-xl border-white/30",pink:"bg-pink-550/95 backdrop-blur-xl border-white/30"}[a]||"bg-blue-550/95 backdrop-blur-xl border-white/30",i=()=>a==="dark"?n.text:"text-white drop-shadow-sm",f=()=>a==="dark"?"bg-white/15 backdrop-blur-sm":"bg-white/25 backdrop-blur-sm",p=()=>a==="dark"?"bg-white/20 backdrop-blur-md":"bg-white/35 backdrop-blur-md";return t.jsxs(t.Fragment,{children:[t.jsx("div",{className:`hidden lg:flex lg:flex-shrink-0 lg:w-64 ${u()} border-r shadow-2xl`,children:t.jsxs("div",{className:"flex flex-col w-full",children:[t.jsx("div",{className:"flex items-center justify-center h-16 px-4 border-b border-white/15",children:t.jsx("h2",{className:`text-xl font-bold ${i()} tracking-wider drop-shadow-sm`,children:"MENU"})}),t.jsxs("nav",{className:"flex-1 px-4 py-6 space-y-2",children:[ie.map(l=>t.jsxs(B,{to:l.href,className:({isActive:g})=>`flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-300 ${g?`${p()} ${i()} shadow-lg border border-white/30`:`${i()} hover:${f()} hover:shadow-md hover:border hover:border-white/20`}`,children:[t.jsx(l.icon,{className:"mr-3 h-5 w-5"}),l.name]},l.name)),t.jsxs("div",{className:"space-y-2",children:[t.jsxs("button",{onClick:()=>c(!s),className:`w-full flex items-center justify-between px-4 py-3 text-sm font-medium rounded-xl transition-all duration-300 ${o.pathname.includes("/settings")?`${p()} ${i()} shadow-lg border border-white/30`:`${i()} hover:${f()} hover:shadow-md hover:border hover:border-white/20`}`,children:[t.jsxs("div",{className:"flex items-center",children:[t.jsx(Y,{className:"mr-3 h-5 w-5"}),"Settings"]}),s?t.jsx(Q,{className:"h-4 w-4"}):t.jsx(ee,{className:"h-4 w-4"})]}),s&&t.jsx("div",{className:"ml-4 space-y-2 mt-2",children:ae.map(l=>t.jsxs(B,{to:l.href,className:({isActive:g})=>`flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-300 ${g?`bg-white/30 backdrop-blur-sm ${i()} shadow-md border border-white/25`:`${i()} hover:bg-white/20 hover:backdrop-blur-sm hover:shadow-sm`}`,children:[t.jsx(l.icon,{className:"mr-3 h-4 w-4"}),l.name]},l.name))})]})]})]})}),r&&t.jsx("div",{className:`sidebar-container lg:hidden fixed inset-y-0 left-0 z-50 w-64 ${u()} transform transition-transform duration-300 ease-in-out shadow-2xl translate-x-0`,style:{paddingTop:"env(safe-area-inset-top, 0px)"},children:t.jsxs("div",{className:"flex flex-col h-full",children:[t.jsxs("div",{className:"flex items-center justify-between px-4 border-b border-white/15",style:{minHeight:"calc(4rem + env(safe-area-inset-top, 0px))",paddingTop:"calc(env(safe-area-inset-top, 0px) + 1rem)",paddingBottom:"1rem"},children:[t.jsx("div",{className:"flex items-center",children:t.jsx("h2",{className:`text-xl font-bold ${i()} tracking-wider drop-shadow-sm`,children:"MENU"})}),t.jsx("button",{onClick:e,className:`p-2 rounded-xl ${i()} hover:bg-white/15 hover:backdrop-blur-sm transition-all duration-300 shadow-sm`,children:t.jsx(Ue,{className:"h-6 w-6"})})]}),t.jsxs("nav",{className:"flex-1 px-4 py-6 space-y-2",children:[ie.map(l=>t.jsxs(B,{to:l.href,onClick:e,className:({isActive:g})=>`flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-300 ${g?`${p()} ${i()} shadow-lg border border-white/30`:`${i()} hover:${f()} hover:shadow-md hover:border hover:border-white/20`}`,children:[t.jsx(l.icon,{className:"mr-3 h-5 w-5"}),l.name]},l.name)),t.jsxs("div",{className:"space-y-2",children:[t.jsxs("button",{onClick:()=>c(!s),className:`w-full flex items-center justify-between px-4 py-3 text-sm font-medium rounded-xl transition-all duration-300 ${o.pathname.includes("/settings")?`${p()} ${i()} shadow-lg border border-white/30`:`${i()} hover:${f()} hover:shadow-md hover:border hover:border-white/20`}`,children:[t.jsxs("div",{className:"flex items-center",children:[t.jsx(Y,{className:"mr-3 h-5 w-5"}),"Settings"]}),s?t.jsx(Q,{className:"h-4 w-4"}):t.jsx(ee,{className:"h-4 w-4"})]}),s&&t.jsx("div",{className:"ml-4 space-y-2 mt-2",children:ae.map(l=>t.jsxs(B,{to:l.href,onClick:e,className:({isActive:g})=>`flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-300 ${g?`bg-white/30 backdrop-blur-sm ${i()} shadow-md border border-white/25`:`${i()} hover:bg-white/20 hover:backdrop-blur-sm hover:shadow-sm`}`,children:[t.jsx(l.icon,{className:"mr-3 h-4 w-4"}),l.name]},l.name))})]})]})]})})]})}function dt(){const r=W(),e=ge(),{currentTheme:n}=L(),a=()=>n==="dark"?"bg-gray-900/95 backdrop-blur-xl border-gray-700/50":{electric:"bg-blue-550/95 backdrop-blur-xl border-white/30",green:"bg-green-550/95 backdrop-blur-xl border-white/30",teal:"bg-teal-550/95 backdrop-blur-xl border-white/30",pink:"bg-pink-550/95 backdrop-blur-xl border-white/30"}[n]||"bg-blue-550/95 backdrop-blur-xl border-white/30",o=[{id:"dashboard",path:"/",icon:he,label:"Dashboard"},{id:"purchases",path:"/purchases",icon:Ae,label:"Purchases"},{id:"usage",path:"/usage",icon:Ie,label:"Usage"},{id:"history",path:"/history",icon:pe,label:"History"}],s=c=>c==="/"?r.pathname==="/"||r.pathname==="/dashboard":r.pathname===c;return t.jsx("div",{className:`md:hidden ${a()} border-t shadow-xl`,style:{paddingBottom:"calc(env(safe-area-inset-bottom, 0px) + 0.25rem)",position:"fixed",bottom:0,left:0,right:0,zIndex:50},children:t.jsx("div",{className:"flex items-center justify-around px-1 py-1",children:o.map(c=>{const u=c.icon,i=s(c.path);return t.jsxs("button",{onClick:()=>e(c.path),className:`flex flex-col items-center justify-center p-1.5 rounded-lg transition-all duration-200 min-w-0 flex-1 ${i?"bg-white/20 text-white shadow-lg transform scale-105 border border-white/30":"text-white/80 hover:bg-white/10 hover:text-white"}`,style:{minHeight:"36px"},children:[t.jsx(u,{className:`h-3.5 w-3.5 ${i?"text-white":"text-white/80"}`}),t.jsx("span",{className:`text-xs font-medium mt-0.5 truncate ${i?"text-white":"text-white/80"}`,children:c.label})]},c.id)})})})}const ce=r=>{r.target.type==="number"&&r.preventDefault()};function ut(){const[r,e]=h.useState(""),[n,a]=h.useState(""),[o,s]=h.useState(""),{initializeApp:c,state:u}=Z(),{theme:i}=L(),f=p=>{p.preventDefault(),s("");const l=parseFloat(r),g=parseFloat(n);if(isNaN(l)||l<0){s("Please enter a valid number of units (0 or greater)");return}if(isNaN(g)||g<=0){s("Please enter a valid cost per unit (greater than 0)");return}c(l,g)};return t.jsx("div",{className:`min-h-screen ${i.background}`,children:t.jsx("div",{className:"h-screen overflow-y-auto",children:t.jsx("div",{className:"container mx-auto px-4 py-4",children:t.jsxs("div",{className:`max-w-lg mx-auto ${i.card} rounded-2xl shadow-2xl p-6 border ${i.border}`,children:[t.jsxs("div",{className:"text-center mb-6",children:[t.jsx("div",{className:"flex justify-center mb-4",children:t.jsxs("div",{className:"h-16 w-16 flex items-center justify-center",children:[t.jsx("img",{src:"/Logo Prepaid User.png",alt:"Prepaid User Electricity Logo",className:"h-16 w-16 object-contain",onError:p=>{p.target.style.display="none",p.target.nextElementSibling.style.display="flex"}}),t.jsx("div",{className:"h-16 w-16 rounded-full bg-blue-600 flex items-center justify-center shadow-2xl border-4 border-white",style:{display:"none"},children:t.jsx("span",{className:"text-white text-2xl font-bold",children:"⚡"})})]})}),t.jsx("h1",{className:`text-2xl font-bold ${i.text} mb-3`,children:"Welcome to Prepaid Meter App"}),t.jsx("p",{className:`${i.textSecondary} text-base mb-2`,children:"Let's get started by setting up your initial meter reading and cost settings"})]}),t.jsxs("form",{onSubmit:f,className:"space-y-4",children:[t.jsxs("div",{children:[t.jsx("label",{htmlFor:"initialUnits",className:`block text-sm font-medium ${i.text} mb-2`,children:"Initial Unit Value"}),t.jsx("input",{type:"number",id:"initialUnits",value:r,onChange:p=>e(p.target.value),onWheel:ce,step:"0.01",min:"0",placeholder:"Enter your current meter reading",className:`w-full px-3 py-3 border-2 ${i.border} rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${i.card} ${i.text} transition-all duration-200 hover:border-blue-300`,required:!0})]}),t.jsxs("div",{children:[t.jsxs("label",{htmlFor:"unitCost",className:`block text-sm font-medium ${i.text} mb-2`,children:["Cost per Unit (",u.currencySymbol||"R",")"]}),t.jsx("input",{type:"number",id:"unitCost",value:n,onChange:p=>a(p.target.value),onWheel:ce,step:"0.01",min:"0.01",placeholder:"Enter cost per unit (e.g., 2.50)",className:`w-full px-3 py-3 border-2 ${i.border} rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${i.card} ${i.text} transition-all duration-200 hover:border-blue-300`,required:!0})]}),o&&t.jsx("div",{className:"p-3 bg-red-50 border border-red-200 rounded-lg",children:t.jsx("p",{className:"text-sm text-red-600",children:o})}),(r||n)&&t.jsxs("div",{className:"mt-4 space-y-3",children:[t.jsxs("div",{className:`relative overflow-hidden rounded-xl ${i.card} border ${i.border} p-4 shadow-lg`,children:[t.jsx("div",{className:`absolute inset-0 ${i.secondary}`}),t.jsx("div",{className:"relative",children:t.jsxs("div",{className:"flex items-center justify-between",children:[t.jsxs("div",{className:"flex-1",children:[t.jsx("div",{className:`text-xs font-semibold ${i.textSecondary} tracking-wider uppercase mb-2`,children:"STARTING UNITS"}),t.jsx("p",{className:`text-xl font-bold ${i.text} mb-1`,children:parseFloat(r||0).toFixed(2)}),t.jsx("p",{className:`text-xs font-medium ${i.textSecondary}`,children:"Initial meter reading"})]}),t.jsx("div",{className:`p-2 rounded-lg bg-gradient-to-br ${i.gradient} shadow-lg ml-3`,children:t.jsx("span",{className:"text-white text-lg",children:"⚡"})})]})})]}),t.jsxs("div",{className:`relative overflow-hidden rounded-xl ${i.card} border ${i.border} p-4 shadow-lg`,children:[t.jsx("div",{className:`absolute inset-0 ${i.secondary}`}),t.jsx("div",{className:"relative",children:t.jsxs("div",{className:"flex items-center justify-between",children:[t.jsxs("div",{className:"flex-1",children:[t.jsx("div",{className:`text-xs font-semibold ${i.textSecondary} tracking-wider uppercase mb-2`,children:"UNIT COST"}),t.jsxs("p",{className:`text-xl font-bold ${i.text} mb-1`,children:[u.currencySymbol||"R",parseFloat(n||0).toFixed(2)]}),t.jsx("p",{className:`text-xs font-medium ${i.textSecondary}`,children:"Per unit rate"})]}),t.jsx("div",{className:`p-2 rounded-lg bg-gradient-to-br ${i.gradient} shadow-lg ml-3`,children:t.jsx("span",{className:"text-white text-lg",children:"💰"})})]})})]})]}),t.jsx("button",{type:"submit",className:`w-full bg-gradient-to-r ${i.gradient} text-white py-3 px-6 rounded-xl font-semibold hover:opacity-90 transition-all duration-200 focus:ring-2 focus:ring-opacity-50 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5`,children:"🚀 Initialize App"})]}),t.jsx("div",{className:"mt-4 text-center",children:t.jsx("p",{className:`text-xs ${i.textSecondary}`,children:"You can always change these values later in Settings"})})]})})})})}function O(){const r=Se(),{theme:e}=L();return console.error("Route Error:",r),Ee(r)?t.jsx("div",{className:`min-h-screen flex items-center justify-center ${e.background}`,children:t.jsxs("div",{className:`max-w-md mx-auto text-center p-6 ${e.card} rounded-lg shadow-lg`,children:[t.jsxs("h1",{className:`text-2xl font-bold mb-4 ${e.text}`,children:[r.status," ",r.statusText]}),t.jsx("p",{className:`${e.textSecondary} mb-4`,children:r.status===404?"The page you're looking for doesn't exist.":"Something went wrong with your request."}),t.jsx("button",{onClick:()=>window.location.href="/",className:`px-4 py-2 ${e.primary} text-white rounded-lg hover:opacity-90 transition-opacity`,children:"Go Home"})]})}):r instanceof Error?t.jsx("div",{className:`min-h-screen flex items-center justify-center ${e.background}`,children:t.jsxs("div",{className:`max-w-md mx-auto text-center p-6 ${e.card} rounded-lg shadow-lg`,children:[t.jsx("h1",{className:`text-2xl font-bold mb-4 ${e.text}`,children:"Oops! Something went wrong"}),t.jsx("p",{className:`${e.textSecondary} mb-4`,children:r.message}),!1,t.jsx("button",{onClick:()=>window.location.reload(),className:`px-4 py-2 ${e.primary} text-white rounded-lg hover:opacity-90 transition-opacity mr-2`,children:"Reload Page"}),t.jsx("button",{onClick:()=>window.location.href="/",className:`px-4 py-2 ${e.secondary} ${e.text} rounded-lg hover:opacity-90 transition-opacity`,children:"Go Home"})]})}):t.jsx("div",{className:`min-h-screen flex items-center justify-center ${e.background}`,children:t.jsxs("div",{className:`max-w-md mx-auto text-center p-6 ${e.card} rounded-lg shadow-lg`,children:[t.jsx("h1",{className:`text-2xl font-bold mb-4 ${e.text}`,children:"Unknown Error"}),t.jsx("p",{className:`${e.textSecondary} mb-4`,children:"An unexpected error occurred. Please try refreshing the page."}),t.jsx("button",{onClick:()=>window.location.reload(),className:`px-4 py-2 ${e.primary} text-white rounded-lg hover:opacity-90 transition-opacity`,children:"Reload Page"})]})})}function mt({size:r="md",text:e="Loading..."}){const{theme:n}=L(),a={sm:"h-4 w-4",md:"h-8 w-8",lg:"h-12 w-12",xl:"h-16 w-16"};return t.jsxs("div",{className:`flex flex-col items-center justify-center p-8 ${n.background}`,children:[t.jsx("div",{className:"relative",children:t.jsx("div",{className:`${a[r]} border-4 border-gray-200 rounded-full animate-spin`,children:t.jsx("div",{className:"absolute inset-0 border-4 border-transparent border-t-blue-500 rounded-full animate-spin"})})}),e&&t.jsx("p",{className:`mt-4 text-sm font-medium ${n.textSecondary} animate-pulse`,children:e})]})}const le=h.lazy(()=>U(()=>import("../js/Dashboard-Bb25e2n7.js"),__vite__mapDeps([4,1,5,3,6,7,8,2]))),ft=h.lazy(()=>U(()=>import("../js/Purchases-D7OohU2F.js"),__vite__mapDeps([9,1,3,8,6,7,2]))),gt=h.lazy(()=>U(()=>import("../js/Usage-OzkL3q8n.js"),__vite__mapDeps([10,5,1,3,11,8,6,7,2]))),ht=h.lazy(()=>U(()=>import("../js/History-DMxJ8V99.js"),__vite__mapDeps([12,1,3,11,6,7,2]))),pt=h.lazy(()=>U(()=>import("../js/Settings-kYCO9Y8I.js"),__vite__mapDeps([13,1,3,2])));function bt(){const[r,e]=h.useState(!1),{state:n}=Z(),{theme:a,currentTheme:o}=L(),s=W();return h.useEffect(()=>{const c=u=>{u.target.type==="number"&&document.activeElement===u.target&&u.preventDefault()};return document.addEventListener("wheel",c,{passive:!1}),()=>{document.removeEventListener("wheel",c)}},[]),h.useEffect(()=>{const c=u=>{r&&!u.target.closest(".sidebar-container")&&!u.target.closest(".hamburger-menu")&&e(!1)};return document.addEventListener("mousedown",c),()=>{document.removeEventListener("mousedown",c)}},[r]),h.useEffect(()=>{e(!1)},[s.pathname]),h.useEffect(()=>{e(!1);const c=()=>{window.innerWidth<1024&&e(!1)};return window.addEventListener("resize",c),()=>window.removeEventListener("resize",c)},[]),n.isInitialized?t.jsxs("div",{className:`flex flex-col h-screen ${a.background}`,style:{height:"100vh"},children:[t.jsx("div",{className:"flex-shrink-0",style:{paddingTop:"env(safe-area-inset-top, 0px)"}}),t.jsxs("div",{className:"flex flex-1 overflow-hidden",children:[r&&t.jsx("div",{className:"fixed inset-0 bg-black/50 z-40 lg:hidden",onClick:()=>e(!1),style:{zIndex:40}}),t.jsx(lt,{isOpen:r,onClose:()=>e(!1)}),t.jsxs("div",{className:"flex flex-col flex-1 overflow-hidden",children:[t.jsx("div",{className:"flex-shrink-0",children:t.jsx(ct,{onMenuClick:()=>e(c=>!c)})}),t.jsx("main",{className:"flex-1 overflow-y-auto overflow-x-hidden px-4 lg:px-8 pt-2 mobile-content",style:{WebkitOverflowScrolling:"touch",touchAction:"pan-y",overscrollBehavior:"contain",paddingBottom:"calc(env(safe-area-inset-bottom, 0px) + 4rem)"},children:t.jsx("div",{className:"w-full",children:t.jsx(h.Suspense,{fallback:t.jsx(mt,{size:"lg",text:"Loading page..."}),children:t.jsxs(ke,{children:[t.jsx(I,{path:"/",element:t.jsx(le,{}),errorElement:t.jsx(O,{})}),t.jsx(I,{path:"/dashboard",element:t.jsx(le,{}),errorElement:t.jsx(O,{})}),t.jsx(I,{path:"/purchases",element:t.jsx(ft,{}),errorElement:t.jsx(O,{})}),t.jsx(I,{path:"/usage",element:t.jsx(gt,{}),errorElement:t.jsx(O,{})}),t.jsx(I,{path:"/history",element:t.jsx(ht,{}),errorElement:t.jsx(O,{})}),t.jsx(I,{path:"/settings",element:t.jsx(pt,{}),errorElement:t.jsx(O,{})}),t.jsx(I,{path:"*",element:t.jsx(O,{})})]})})})})]})]}),t.jsx(dt,{})]}):t.jsx(ut,{})}const de=H("App",{web:()=>U(()=>import("../js/web-DrpM_nhU.js"),__vite__mapDeps([14,1,2,3])).then(r=>new r.AppWeb)}),xt=()=>{const r=ge(),e=W();h.useEffect(()=>{if(!P.isNativePlatform()||P.getPlatform()!=="android")return;const n=()=>{const o=e.pathname,s={"/purchases":"/","/usage":"/","/history":"/","/settings":"/"};if(s[o]){r(s[o]);return}if(o==="/"){de.exitApp();return}r("/")},a=de.addListener("backButton",n);return()=>{a.remove()}},[r,e.pathname])};var ue;(function(r){r.Dark="DARK",r.Light="LIGHT",r.Default="DEFAULT"})(ue||(ue={}));var me;(function(r){r.None="NONE",r.Slide="SLIDE",r.Fade="FADE"})(me||(me={}));const z=H("StatusBar");var fe;(function(r){r.WHITE="#FFFFFF",r.BLACK="#000000",r.TRANSPARENT="transparent"})(fe||(fe={}));const V=H("NavigationBar",{web:()=>U(()=>import("../js/web-LmdFvVZH.js"),__vite__mapDeps([15,1,2,3])).then(r=>new r.NavigationBarWeb)});function yt(){const{currentTheme:r}=L();h.useEffect(()=>{if(!P.isNativePlatform())return;(async()=>{try{await z.setOverlaysWebView({overlay:!1}),await z.show();try{await V.show()}catch(o){console.log("NavigationBar.show() not available:",o)}const a={dark:"#374151",electric:"#60a5fa",green:"#34d399",teal:"#2dd4bf",pink:"#f472b6"}[r]||"#60a5fa";await z.setStyle({style:"LIGHT"}),await z.setBackgroundColor({color:a}),console.log("Setting navigation bar color to:",a),await V.setColor({color:a,darkButtons:!1});try{await V.setNavigationBarColor({color:a})}catch(o){console.log("Alternative navigation bar method not available:",o)}}catch(n){console.log("StatusBar or NavigationBar plugin not available:",n)}})()},[r])}function wt(){return xt(),yt(),t.jsx(bt,{})}function vt(){return t.jsx(Pe,{children:t.jsx(it,{children:t.jsx(st,{children:t.jsx(wt,{})})})})}ye(document.getElementById("root")).render(t.jsx(h.StrictMode,{children:t.jsx(vt,{})}));export{P as C,we as W,L as a,t as j,ce as p,q as t,Z as u};
