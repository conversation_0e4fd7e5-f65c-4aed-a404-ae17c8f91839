import{u as S,a as D,j as e}from"../assets/index-yYkGcJQj.js";import{b as H,r as x}from"./vendor-C67cHu0f.js";import{l as k,r as U,m as C,s as E,e as $,t as P}from"./icons-Bhz3yUky.js";import{S as L}from"./ScrollHint-CEiMHnNb.js";import{N as B}from"./NativeAd-Bvxt7ydf.js";import"./utils-CgIdLkdF.js";function G({history:h}){const{state:c,getDisplayUnitName:u}=S(),{theme:t,currentTheme:n}=D(),i=r=>{const l=new Date(r),m=l.toLocaleDateString("en-GB"),d=l.toLocaleTimeString("en-GB",{hour:"2-digit",minute:"2-digit",hour12:!1});return{date:m,time:d}};return h.length===0?null:e.jsx(L,{children:e.jsxs("table",{className:`min-w-full divide-y ${n==="dark"?"divide-gray-600":"divide-gray-200"}`,style:{minWidth:"600px"},children:[e.jsx("thead",{className:t.secondary,children:e.jsxs("tr",{children:[e.jsx("th",{className:`px-6 py-3 text-left text-xs font-medium ${t.textSecondary} uppercase tracking-wider`,children:"Type"}),e.jsx("th",{className:`px-6 py-3 text-left text-xs font-medium ${t.textSecondary} uppercase tracking-wider`,children:"Date & Time"}),e.jsx("th",{className:`px-6 py-3 text-left text-xs font-medium ${t.textSecondary} uppercase tracking-wider`,children:"Details"}),e.jsx("th",{className:`px-6 py-3 text-left text-xs font-medium ${t.textSecondary} uppercase tracking-wider`,children:u()}),e.jsx("th",{className:`px-6 py-3 text-left text-xs font-medium ${t.textSecondary} uppercase tracking-wider`,children:"Amount"})]})}),e.jsx("tbody",{className:`${t.card} divide-y ${n==="dark"?"divide-gray-600":"divide-gray-200"}`,children:h.map(r=>e.jsxs("tr",{className:`${n==="dark"?"hover:bg-gray-700":"hover:bg-gray-50"} transition-colors duration-200`,children:[e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("div",{className:"flex items-center",children:r.type==="purchase"?e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:`p-2 rounded-lg ${t.secondary}`,children:e.jsx(k,{className:`h-4 w-4 ${t.textSecondary}`})}),e.jsx("div",{className:"ml-3",children:e.jsxs("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${n==="dark"?"bg-green-900/30 text-green-400":"bg-green-100 text-green-800"}`,children:[e.jsx(U,{className:"mr-1 h-3 w-3"}),"Purchase"]})})]}):e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:`p-2 rounded-lg ${n==="dark"?"bg-red-900/30":"bg-red-100"}`,children:e.jsx(C,{className:"h-4 w-4 text-red-600"})}),e.jsx("div",{className:"ml-3",children:e.jsxs("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${n==="dark"?"bg-red-900/30 text-red-400":"bg-red-100 text-red-800"}`,children:[e.jsx(E,{className:"mr-1 h-3 w-3"}),"Usage"]})})]})})}),e.jsx("td",{className:`px-6 py-6 whitespace-nowrap text-sm ${t.text}`,children:e.jsxs("div",{children:[e.jsx("p",{children:i(r.timestamp).date}),e.jsx("p",{className:`text-xs ${t.textSecondary}`,children:i(r.timestamp).time})]})}),e.jsx("td",{className:`px-6 py-6 text-sm ${t.text}`,children:r.type==="purchase"?e.jsxs("div",{className:"space-y-2",children:[e.jsx("p",{className:"font-medium",children:"Electricity Purchase"}),e.jsxs("p",{className:`text-xs ${t.textSecondary}`,children:["@ ",c.currencySymbol||"R",r.unitCost.toFixed(2)," per ",u()]})]}):e.jsxs("div",{className:"space-y-2",children:[e.jsx("p",{className:"font-medium",children:"Usage Recording"}),e.jsxs("p",{className:`text-xs ${t.textSecondary}`,children:["From ",r.previousUnits.toFixed(2)," to ",r.currentUnits.toFixed(2)," ",u()]})]})}),e.jsx("td",{className:`px-6 py-6 whitespace-nowrap text-sm ${t.text}`,children:r.type==="purchase"?e.jsxs("span",{className:`${t.text} font-medium`,children:["+",r.units.toFixed(2)]}):e.jsxs("span",{className:`${t.text} font-medium`,children:["-",r.usage.toFixed(2)]})}),e.jsx("td",{className:`px-6 py-6 whitespace-nowrap text-sm ${t.text}`,children:r.type==="purchase"?e.jsxs("span",{className:`${t.text} font-medium`,children:["+",c.currencySymbol||"R",r.currency.toFixed(2)]}):e.jsxs("span",{className:`${t.text} font-medium`,children:["-",c.currencySymbol||"R",(r.usage*c.unitCost).toFixed(2)]})})]},`${r.type}-${r.id}`))})]})})}function M(){const h=H(),[c,u]=x.useState("all"),[t,n]=x.useState(""),[i,r]=x.useState(!1),[l,m]=x.useState(""),[d,g]=x.useState(""),y=x.useRef(null),{state:j}=S(),{theme:s,currentTheme:F}=D(),N=[...j.purchases.map(a=>({...a,type:"purchase"})),...j.usageHistory.map(a=>({...a,type:"usage"}))].sort((a,o)=>new Date(o.date)-new Date(a.date)).filter(a=>{const o=c==="all"||a.type===c;if(t&&!l&&!d)return o&&a.date.includes(t);if(l||d){const b=new Date(a.date),v=l?new Date(l):null,w=d?new Date(d):null;let p=!0;return v&&(p=p&&b>=v),w&&(p=p&&b<=w),o&&p}return o}),R=[{id:"all",name:"All Activity",icon:$},{id:"purchase",name:"Purchases",icon:k},{id:"usage",name:"Usage",icon:C}],T=()=>{n(""),r(!1)},A=()=>{n(""),m(""),g(""),r(!1)},f=t||l||d;return x.useEffect(()=>{const a=o=>{y.current&&!y.current.contains(o.target)&&r(!1)};return i&&document.addEventListener("mousedown",a),()=>{document.removeEventListener("mousedown",a)}},[i]),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:`${s.card} rounded-2xl shadow-lg border ${s.border}`,children:[e.jsx("div",{className:`p-4 md:p-8 ${s.secondary} rounded-t-2xl`,children:e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"flex flex-wrap gap-2",children:R.map(a=>e.jsxs("button",{onClick:()=>u(a.id),className:`flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-all duration-300 border backdrop-blur-md shadow-lg ${c===a.id?`${s.primary} text-white border-white/30 bg-opacity-90 shadow-xl`:F==="dark"?`${s.text} bg-white/10 border-white/20 hover:bg-white/20 hover:border-white/30 hover:shadow-xl`:`${s.text} bg-white/30 border-white/40 hover:bg-white/50 hover:border-white/60 hover:shadow-xl`}`,children:[e.jsx(a.icon,{className:"mr-2 h-4 w-4"}),a.name]},a.id))}),e.jsxs("div",{ref:y,className:"space-y-3",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[e.jsx(P,{className:`h-5 w-5 ${s.textSecondary}`}),e.jsx("span",{className:`text-sm font-medium ${s.text}`,children:"Filter by Date"})]}),e.jsxs("div",{className:"flex flex-col sm:flex-row gap-3",children:[e.jsx("input",{type:"date",value:t||new Date().toISOString().split("T")[0],onChange:a=>{n(a.target.value),a.target.value&&(m(""),g(""))},className:`px-3 py-2 border ${s.border} rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${s.card} ${s.text} text-sm w-full sm:w-40`,placeholder:"Filter by date"}),e.jsx("button",{onClick:()=>r(!i),className:`px-3 py-2 border ${s.border} rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${s.card} ${s.text} hover:${s.secondary} transition-colors text-sm ${l||d?"ring-2 ring-blue-500":""}`,children:"📅 Range"}),f&&e.jsx("button",{onClick:A,className:`px-3 py-2 text-sm ${s.textSecondary} hover:${s.text} transition-colors border ${s.border} rounded-lg`,children:"Clear All"})]}),i&&e.jsx("div",{className:`mt-3 p-4 ${s.card} border ${s.border} rounded-xl shadow-xl w-full max-w-md`,children:e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:`font-semibold ${s.text} text-sm`,children:"Select Date Range"}),e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:`block text-xs font-medium ${s.textSecondary} mb-1`,children:"From Date"}),e.jsx("input",{type:"date",value:l,onChange:a=>{m(a.target.value),n("")},className:`w-full px-3 py-2 border ${s.border} rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${s.card} ${s.text} text-sm`})]}),e.jsxs("div",{children:[e.jsx("label",{className:`block text-xs font-medium ${s.textSecondary} mb-1`,children:"To Date"}),e.jsx("input",{type:"date",value:d,onChange:a=>{g(a.target.value),n("")},className:`w-full px-3 py-2 border ${s.border} rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${s.card} ${s.text} text-sm`})]})]}),e.jsxs("div",{className:"flex justify-between items-center pt-2",children:[e.jsx("button",{onClick:()=>{m(""),g("")},className:`text-xs ${s.textSecondary} hover:${s.text} transition-colors`,children:"Clear Range"}),e.jsx("button",{onClick:T,className:`px-4 py-2 bg-gradient-to-r ${s.gradient} text-white rounded-lg hover:opacity-90 transition-colors text-sm font-medium`,children:"Apply"})]})]})})]})]})}),e.jsx("div",{className:`border-t ${s.border}`,children:e.jsx(G,{history:N})})]}),e.jsx(B,{className:"my-4"}),N.length===0&&e.jsxs("div",{className:`${s.card} rounded-2xl shadow-lg p-16 text-center border ${s.border}`,children:[e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:`absolute inset-0 ${s.secondary} rounded-full opacity-20 scale-110`}),e.jsx("div",{className:`relative p-6 rounded-2xl ${s.secondary} w-fit mx-auto`,children:e.jsx($,{className:`h-16 w-16 ${s.textSecondary}`})})]}),e.jsx("h3",{className:`mt-6 text-2xl font-bold ${s.text}`,children:"No history found"}),e.jsx("p",{className:`mt-3 ${s.textSecondary} opacity-80 text-lg leading-relaxed max-w-md mx-auto`,children:f?"No records found for the selected date range. Try adjusting your filters or clear them to see all records.":"Start by making purchases or recording usage to see your history here."}),!f&&e.jsxs("div",{className:"mt-8 flex flex-col sm:flex-row justify-center gap-4",children:[e.jsx("button",{onClick:()=>h("/purchases"),className:`bg-gradient-to-r ${s.gradient} text-white px-6 py-3 rounded-xl font-semibold hover:opacity-90 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]`,children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{children:"💰"}),"Add Purchase"]})}),e.jsx("button",{onClick:()=>h("/usage"),className:`bg-gradient-to-r ${s.gradient} text-white px-6 py-3 rounded-xl font-semibold hover:opacity-90 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]`,children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{children:"⚡"}),"Record Usage"]})})]})]})]})}export{M as default};
