import { useState, useEffect, useRef } from 'react'
import { useTheme, themes } from '../../context/ThemeContext'
import { HiColorSwatch, HiCheck, HiChevronDown } from 'react-icons/hi'

function ThemeSelector() {
  const [themeDropdownOpen, setThemeDropdownOpen] = useState(false)
  const themeDropdownRef = useRef(null)

  const {
    currentTheme,
    setCurrentTheme,
    theme
  } = useTheme()

  // Helper function to get appropriate border colors for dark mode
  const getBorderColor = (isSelected) => {
    if (isSelected) {
      return `${theme.border} ring-2 ring-opacity-50`
    }
    return currentTheme === 'dark' ? 'border-gray-600 hover:border-gray-500' : 'border-gray-200 hover:border-gray-300'
  }

  // Close dropdowns when clicking outside
  useEffect(() => {
    function handleClickOutside(event) {
      if (themeDropdownRef.current && !themeDropdownRef.current.contains(event.target)) {
        setThemeDropdownOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  return (
    <div className="space-y-8 pb-8 md:pb-6 theme-selector-mobile"
         style={{
           paddingBottom: 'calc(env(safe-area-inset-bottom, 0px) + 2rem)',
           minHeight: 'auto'
         }}>

      {/* Theme Selection */}
      <div>
        <h3 className={`text-lg font-semibold ${theme.text} mb-4 flex items-center`}>
          <HiColorSwatch className="mr-2 h-5 w-5" />
          Choose Theme
        </h3>
        <div className="relative" ref={themeDropdownRef}>
          <button
            onClick={() => setThemeDropdownOpen(!themeDropdownOpen)}
            className={`w-full p-4 ${theme.card} border ${theme.border} rounded-lg flex items-center justify-between hover:${theme.secondary} transition-colors min-w-0`}
          >
            <div className="flex items-center space-x-4">
              {/* Current theme preview */}
              <div className="space-y-2">
                <div className={`h-6 w-16 ${themes[currentTheme].gradient} bg-gradient-to-r rounded`} />
                <div className="flex space-x-1">
                  <div className={`h-2 w-2 ${themes[currentTheme].primary} rounded`} />
                  <div className={`h-2 w-2 ${themes[currentTheme].accent} rounded`} />
                  <div className={`h-2 w-2 ${themes[currentTheme].secondary} rounded`} />
                </div>
              </div>
              <span className={`text-lg font-medium ${theme.text}`}>
                {themes[currentTheme].name}
              </span>
            </div>
            <HiChevronDown
              className={`h-5 w-5 ${theme.textSecondary} transition-transform ${
                themeDropdownOpen ? 'rotate-180' : ''
              }`}
            />
          </button>

          {themeDropdownOpen && (
            <div className={`absolute top-full left-0 right-0 mt-2 ${theme.card} border ${theme.border} rounded-lg shadow-lg z-50 max-h-80 overflow-y-auto`}>
              <div className="grid grid-cols-1 gap-2 p-2 w-full">
                {Object.entries(themes).map(([themeKey, themeData]) => (
                  <button
                    key={themeKey}
                    onClick={() => {
                      setCurrentTheme(themeKey)
                      setThemeDropdownOpen(false)
                    }}
                    className={`relative p-4 rounded-lg border-2 transition-all hover:scale-105 text-left w-full ${getBorderColor(currentTheme === themeKey)}`}
                  >
                    <div className="flex items-center space-x-4">
                      {/* Theme preview */}
                      <div className="space-y-2 flex-shrink-0">
                        <div className={`h-8 w-20 ${themeData.gradient} bg-gradient-to-r rounded`} />
                        <div className="flex space-x-1">
                          <div className={`h-3 w-3 ${themeData.primary} rounded`} />
                          <div className={`h-3 w-3 ${themeData.accent} rounded`} />
                          <div className={`h-3 w-3 ${themeData.secondary} rounded`} />
                        </div>
                      </div>

                      {/* Theme name */}
                      <div className="flex-1">
                        <p className={`text-sm font-medium ${theme.text}`}>
                          {themeData.name}
                        </p>
                      </div>

                      {/* Selected indicator */}
                      {currentTheme === themeKey && (
                        <div className="flex-shrink-0">
                          <HiCheck className={`h-5 w-5 ${theme.text}`} />
                        </div>
                      )}
                    </div>
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>



      {/* Theme & Font Preview */}
      <div>
        <h3 className={`text-lg font-semibold ${theme.text} mb-4`}>
          Preview
        </h3>
        <div className={`p-4 ${theme.card} rounded-lg border ${theme.border} space-y-3 theme-preview-card`}>
          <div className="flex items-center justify-between">
            <h4 className={`text-lg font-bold ${theme.text}`}>
              Sample Dashboard
            </h4>
            <div className={`px-2 py-1 ${theme.primary} text-white rounded-full text-sm`}>
              Active
            </div>
          </div>

          <div className="grid grid-cols-1 gap-3">
            <div className={`p-3 ${theme.card} border ${theme.border} rounded-lg shadow-sm h-16`}>
              <p className={`text-xs ${theme.textSecondary}`}>Current Units</p>
              <p className={`text-lg font-bold ${theme.text}`}>125.50</p>
            </div>
          </div>

          <div className="flex space-x-2">
            <button className={`px-3 py-2 ${theme.primary} text-white rounded-lg text-sm h-10`}>
              Primary Button
            </button>
            <button className={`px-3 py-2 border ${theme.border} ${theme.text} rounded-lg text-sm h-10`}>
              Secondary Button
            </button>
          </div>
        </div>
      </div>

      {/* Reset to Default */}
      <div>
        <h3 className={`text-lg font-semibold ${theme.text} mb-4`}>
          Reset Appearance
        </h3>
        <div className="space-y-3">
          <button
            onClick={() => {
              setCurrentTheme('electric')
              setThemeDropdownOpen(false)
            }}
            className={`w-full px-6 py-3 bg-gradient-to-r ${theme.gradient} text-white rounded-lg hover:opacity-90 transition-all duration-300 shadow-lg hover:shadow-xl`}
          >
            Reset to Default Theme
          </button>
        </div>
      </div>
    </div>
  )
}

export default ThemeSelector
