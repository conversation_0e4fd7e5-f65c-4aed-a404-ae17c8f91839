package com.prepaidmeter.app;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.LinearLayout;
import android.widget.FrameLayout;
import android.graphics.Color;
import android.graphics.Typeface;
import android.util.TypedValue;
import android.util.Log;
import android.text.TextUtils;

import com.getcapacitor.JSObject;
import com.getcapacitor.Plugin;
import com.getcapacitor.PluginCall;
import com.getcapacitor.PluginMethod;
import com.getcapacitor.annotation.CapacitorPlugin;

import com.google.android.gms.ads.AdListener;
import com.google.android.gms.ads.AdLoader;
import com.google.android.gms.ads.AdRequest;
import com.google.android.gms.ads.LoadAdError;
import com.google.android.gms.ads.MobileAds;
import com.google.android.gms.ads.initialization.InitializationStatus;
import com.google.android.gms.ads.initialization.OnInitializationCompleteListener;
import com.google.android.gms.ads.nativead.NativeAd;
import com.google.android.gms.ads.nativead.NativeAdOptions;
import com.google.android.gms.ads.nativead.NativeAdView;
import com.google.android.gms.ads.nativead.MediaView;
import com.google.android.gms.ads.VideoOptions;

@CapacitorPlugin(name = "AdMobNative")
public class AdMobNativePlugin extends Plugin {

    private static final String TAG = "AdMobNativePlugin";
    private NativeAd currentNativeAd;
    private NativeAdView currentAdView;

    @PluginMethod
    public void initialize(PluginCall call) {
        getActivity().runOnUiThread(() -> {
            try {
                MobileAds.initialize(getContext(), new OnInitializationCompleteListener() {
                    @Override
                    public void onInitializationComplete(InitializationStatus initializationStatus) {
                        Log.d(TAG, "AdMob initialized successfully");
                        JSObject ret = new JSObject();
                        ret.put("success", true);
                        ret.put("message", "AdMob initialized successfully");
                        call.resolve(ret);
                    }
                });
            } catch (Exception e) {
                Log.e(TAG, "Failed to initialize AdMob", e);
                JSObject ret = new JSObject();
                ret.put("success", false);
                ret.put("error", e.getMessage());
                call.resolve(ret);
            }
        });
    }

    @PluginMethod
    public void loadNativeAd(PluginCall call) {
        String adUnitId = call.getString("adUnitId", "ca-app-pub-3940256099942544/2247696110");

        if (adUnitId == null || adUnitId.isEmpty()) {
            JSObject ret = new JSObject();
            ret.put("success", false);
            ret.put("error", "Ad unit ID is required");
            call.resolve(ret);
            return;
        }

        getActivity().runOnUiThread(() -> {
            try {
                // Clean up previous ad
                if (currentNativeAd != null) {
                    currentNativeAd.destroy();
                    currentNativeAd = null;
                }

                AdLoader.Builder builder = new AdLoader.Builder(getContext(), adUnitId);

                builder.forNativeAd(new NativeAd.OnNativeAdLoadedListener() {
                    @Override
                    public void onNativeAdLoaded(NativeAd nativeAd) {
                        Log.d(TAG, "Native ad loaded successfully");
                        currentNativeAd = nativeAd;

                        // Create AdMob compliant native ad view
                        createCompliantNativeAdView(nativeAd, call);
                    }
                });

                builder.withAdListener(new AdListener() {
                    @Override
                    public void onAdFailedToLoad(LoadAdError adError) {
                        Log.e(TAG, "Failed to load native ad: " + adError.getMessage());
                        JSObject ret = new JSObject();
                        ret.put("success", false);
                        ret.put("error", "Failed to load ad: " + adError.getMessage());
                        call.resolve(ret);
                    }
                });

                // Configure native ad options for STRICT COMPLIANCE with AdMob guidelines
                NativeAdOptions adOptions = new NativeAdOptions.Builder()
                        // STRICT COMPLIANCE: AdChoices must be easily visible
                        .setAdChoicesPlacement(NativeAdOptions.ADCHOICES_TOP_RIGHT)
                        // STRICT COMPLIANCE: Support multiple aspect ratios for video compliance
                        .setMediaAspectRatio(NativeAdOptions.NATIVE_MEDIA_ASPECT_RATIO_ANY)
                        // STRICT COMPLIANCE: Request single image for better performance
                        .setRequestMultipleImages(false)
                        // STRICT COMPLIANCE: Enable video options for proper MediaView handling
                        .setVideoOptions(new com.google.android.gms.ads.VideoOptions.Builder()
                                .setStartMuted(true) // Better user experience
                                .setCustomControlsRequested(false) // Let SDK handle controls
                                .build())
                        .build();

                builder.withNativeAdOptions(adOptions);

                AdLoader adLoader = builder.build();
                adLoader.loadAd(new AdRequest.Builder().build());

            } catch (Exception e) {
                Log.e(TAG, "Exception loading native ad", e);
                JSObject ret = new JSObject();
                ret.put("success", false);
                ret.put("error", e.getMessage());
                call.resolve(ret);
            }
        });
    }

    private void createCompliantNativeAdView(NativeAd nativeAd, PluginCall call) {
        try {
            Context context = getContext();

            // Use XML layout for proper NativeAdView structure (required for validator)
            LayoutInflater inflater = LayoutInflater.from(context);
            int layoutId = context.getResources().getIdentifier("native_ad_layout", "layout", context.getPackageName());
            if (layoutId == 0) {
                throw new RuntimeException("Layout native_ad_layout not found");
            }
            currentAdView = (NativeAdView) inflater.inflate(layoutId, null);

            // Get views from XML layout
            TextView advertiserView = currentAdView.findViewById(
                context.getResources().getIdentifier("ad_attribution", "id", context.getPackageName())
            );
            TextView headlineView = currentAdView.findViewById(
                context.getResources().getIdentifier("ad_headline", "id", context.getPackageName())
            );
            TextView bodyView = currentAdView.findViewById(
                context.getResources().getIdentifier("ad_body", "id", context.getPackageName())
            );
            MediaView mediaView = currentAdView.findViewById(
                context.getResources().getIdentifier("ad_media", "id", context.getPackageName())
            );
            ImageView iconView = currentAdView.findViewById(
                context.getResources().getIdentifier("ad_app_icon", "id", context.getPackageName())
            );
            TextView starRatingView = currentAdView.findViewById(
                context.getResources().getIdentifier("ad_stars", "id", context.getPackageName())
            );
            TextView storeView = currentAdView.findViewById(
                context.getResources().getIdentifier("ad_store", "id", context.getPackageName())
            );
            TextView priceView = currentAdView.findViewById(
                context.getResources().getIdentifier("ad_price", "id", context.getPackageName())
            );
            Button callToActionView = currentAdView.findViewById(
                context.getResources().getIdentifier("ad_call_to_action", "id", context.getPackageName())
            );
            TextView advertiserNameView = currentAdView.findViewById(
                context.getResources().getIdentifier("ad_advertiser", "id", context.getPackageName())
            );

            // Set ad attribution (CRITICAL: Required by validator - must be "Ad", "Advertisement", or "Sponsored")
            // Per Google policy: minimum 15px height and width
            advertiserView.setText("Ad"); // Always use "Ad" for compliance
            advertiserView.setMinWidth(pxToPx(15)); // Minimum 15px width
            advertiserView.setMinHeight(pxToPx(15)); // Minimum 15px height

            // Set headline (required)
            String headlineText = nativeAd.getHeadline();
            if (headlineText != null && !headlineText.isEmpty()) {
                headlineView.setText(headlineText);
            } else {
                headlineView.setText("Sponsored Content");
            }

            // Set body text if available
            String bodyText = nativeAd.getBody();
            if (bodyText != null && !bodyText.isEmpty()) {
                bodyView.setText(bodyText);
                bodyView.setVisibility(View.VISIBLE);
            } else {
                bodyView.setVisibility(View.GONE);
            }

            // Set media view (required for video ads)
            if (mediaView != null) {
                mediaView.setMediaContent(nativeAd.getMediaContent());
            }

            // Set app icon if available
            if (nativeAd.getIcon() != null && iconView != null) {
                iconView.setImageDrawable(nativeAd.getIcon().getDrawable());
                iconView.setVisibility(View.VISIBLE);
            } else if (iconView != null) {
                iconView.setVisibility(View.GONE);
            }

            // Set star rating if available
            if (nativeAd.getStarRating() != null && starRatingView != null) {
                starRatingView.setText("★ " + nativeAd.getStarRating());
                starRatingView.setVisibility(View.VISIBLE);
            } else if (starRatingView != null) {
                starRatingView.setVisibility(View.GONE);
            }

            // Set store name if available
            if (nativeAd.getStore() != null && storeView != null) {
                storeView.setText(nativeAd.getStore());
                storeView.setVisibility(View.VISIBLE);
            } else if (storeView != null) {
                storeView.setVisibility(View.GONE);
            }

            // Set price if available
            if (nativeAd.getPrice() != null && priceView != null) {
                priceView.setText(nativeAd.getPrice());
                priceView.setVisibility(View.VISIBLE);
            } else if (priceView != null) {
                priceView.setVisibility(View.GONE);
            }

            // Set call to action button (required)
            String ctaText = nativeAd.getCallToAction();
            if (ctaText != null && !ctaText.isEmpty()) {
                callToActionView.setText(ctaText);
            } else {
                callToActionView.setText("Learn More");
            }

            // Set advertiser name if different from attribution
            if (nativeAd.getAdvertiser() != null && advertiserNameView != null) {
                advertiserNameView.setText(nativeAd.getAdvertiser());
                advertiserNameView.setVisibility(View.VISIBLE);
            } else if (advertiserNameView != null) {
                advertiserNameView.setVisibility(View.GONE);
            }

            // Register all views with NativeAdView (REQUIRED for validator)
            currentAdView.setHeadlineView(headlineView);
            currentAdView.setBodyView(bodyView);
            currentAdView.setMediaView(mediaView);
            currentAdView.setCallToActionView(callToActionView);
            currentAdView.setAdvertiserView(advertiserView); // CRITICAL for ad attribution

            // Register optional views if they exist and have content
            if (iconView != null && nativeAd.getIcon() != null) {
                currentAdView.setIconView(iconView);
            }
            if (starRatingView != null && nativeAd.getStarRating() != null) {
                currentAdView.setStarRatingView(starRatingView);
            }
            if (storeView != null && nativeAd.getStore() != null) {
                currentAdView.setStoreView(storeView);
            }
            if (priceView != null && nativeAd.getPrice() != null) {
                currentAdView.setPriceView(priceView);
            }

            // Set the native ad object (REQUIRED)
            currentAdView.setNativeAd(nativeAd);

            // Store reference to current ad
            currentNativeAd = nativeAd;

            // Add to validator container for testing
            addToValidatorContainer(currentAdView);

            // Return success with ad data
            JSObject ret = new JSObject();
            ret.put("success", true);
            ret.put("message", "Native ad loaded successfully");
            ret.put("adData", createAdDataObject(nativeAd));
            call.resolve(ret);

        } catch (Exception e) {
            Log.e(TAG, "Exception creating native ad view", e);
            JSObject ret = new JSObject();
            ret.put("success", false);
            ret.put("error", e.getMessage());
            call.resolve(ret);
        }
    }

    private void addToValidatorContainer(NativeAdView adView) {
        try {
            // Find the root view in the main activity
            ViewGroup rootView = (ViewGroup) getActivity().findViewById(android.R.id.content);

            // Create a properly sized container for validator detection
            FrameLayout validatorContainer = rootView.findViewById(android.R.id.custom);
            if (validatorContainer == null) {
                validatorContainer = new FrameLayout(getContext());
                validatorContainer.setId(android.R.id.custom);

                // Make container FULLY VISIBLE for validator detection
                // AdMob validator MUST be able to see the view structure
                validatorContainer.setVisibility(View.VISIBLE);

                // Set proper size that meets AdMob requirements (minimum 32x32dp)
                ViewGroup.LayoutParams containerParams = new ViewGroup.LayoutParams(
                    ViewGroup.LayoutParams.MATCH_PARENT, // Full width for proper detection
                    Math.max(dpToPx(300), dpToPx(32)) // Ensure minimum 32dp height
                );
                validatorContainer.setLayoutParams(containerParams);

                // CRITICAL: Ensure container meets minimum size requirements
                validatorContainer.setMinimumWidth(dpToPx(32));
                validatorContainer.setMinimumHeight(dpToPx(32));

                // Position ON-SCREEN for validator to properly detect
                // The validator needs to see the actual rendered view
                validatorContainer.setTranslationX(0);
                validatorContainer.setTranslationY(0);

                // Add background to make it visible during testing
                validatorContainer.setBackgroundColor(Color.parseColor("#F0F0F0"));
                validatorContainer.setPadding(dpToPx(8), dpToPx(8), dpToPx(8), dpToPx(8));

                rootView.addView(validatorContainer);

                Log.d(TAG, "Created ON-SCREEN validator container for proper AdMob detection");
            }

            // Clear previous ad and add new one
            validatorContainer.removeAllViews();

            // CRITICAL: Ensure ad view meets minimum size requirements before adding
            ViewGroup.LayoutParams adParams = new ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
            );
            adView.setLayoutParams(adParams);
            adView.setMinimumWidth(dpToPx(32)); // Minimum 32dp width
            adView.setMinimumHeight(dpToPx(32)); // Minimum 32dp height

            validatorContainer.addView(adView);

            Log.d(TAG, "Native ad view added to VISIBLE validator container with minimum 32x32dp size - validator should now detect properly");

        } catch (Exception e) {
            Log.e(TAG, "Failed to add ad view to validator container", e);
        }
    }

    private JSObject createAdDataObject(NativeAd nativeAd) {
        JSObject adData = new JSObject();

        try {
            adData.put("headline", nativeAd.getHeadline() != null ? nativeAd.getHeadline() : "");
            adData.put("body", nativeAd.getBody() != null ? nativeAd.getBody() : "");
            adData.put("callToAction", nativeAd.getCallToAction() != null ? nativeAd.getCallToAction() : "");
            adData.put("advertiser", nativeAd.getAdvertiser() != null ? nativeAd.getAdvertiser() : "");

            // Add icon URL if available
            if (nativeAd.getIcon() != null && nativeAd.getIcon().getUri() != null) {
                adData.put("icon", nativeAd.getIcon().getUri().toString());
            }

            // Add star rating if available
            if (nativeAd.getStarRating() != null) {
                adData.put("starRating", nativeAd.getStarRating());
            }

            // Add store if available
            if (nativeAd.getStore() != null) {
                adData.put("store", nativeAd.getStore());
            }

            // Add price if available
            if (nativeAd.getPrice() != null) {
                adData.put("price", nativeAd.getPrice());
            }

        } catch (Exception e) {
            Log.e(TAG, "Error creating ad data object", e);
        }

        return adData;
    }

    private int dpToPx(int dp) {
        float density = getContext().getResources().getDisplayMetrics().density;
        return Math.round(dp * density);
    }

    private int pxToPx(int px) {
        // For validator requirements that specify pixels (not dp)
        return px;
    }

    @PluginMethod
    public void destroyNativeAd(PluginCall call) {
        getActivity().runOnUiThread(() -> {
            try {
                if (currentNativeAd != null) {
                    currentNativeAd.destroy();
                    currentNativeAd = null;
                }
                if (currentAdView != null) {
                    ViewGroup parent = (ViewGroup) currentAdView.getParent();
                    if (parent != null) {
                        parent.removeView(currentAdView);
                    }
                    currentAdView = null;
                }

                Log.d(TAG, "Native ad destroyed");
                JSObject ret = new JSObject();
                ret.put("success", true);
                ret.put("message", "Native ad destroyed");
                call.resolve(ret);

            } catch (Exception e) {
                Log.e(TAG, "Failed to destroy native ad", e);
                JSObject ret = new JSObject();
                ret.put("success", false);
                ret.put("error", e.getMessage());
                call.resolve(ret);
            }
        });
    }
}
