Write-Host "========================================" -ForegroundColor Green
Write-Host "Building Prepaid Meter Android APK" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

# Set Java Home to Android Studio's JDK
$env:JAVA_HOME = "C:\Program Files\Android\Android Studio\jbr"
$env:PATH = "$env:JAVA_HOME\bin;$env:PATH"

Write-Host ""
Write-Host "Java Home set to: $env:JAVA_HOME" -ForegroundColor Yellow

# Step 1: Build React app
Write-Host ""
Write-Host "Step 1: Building React app for production..." -ForegroundColor Cyan
npm run build
if ($LASTEXITCODE -ne 0) {
    Write-Host "Error: Failed to build React app" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Step 2: Copy assets
Write-Host ""
Write-Host "Step 2: Copying web assets to Android..." -ForegroundColor Cyan
npx cap copy android
if ($LASTEXITCODE -ne 0) {
    Write-Host "Error: Failed to copy assets" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Step 3: Sync
Write-Host ""
Write-Host "Step 3: Syncing Capacitor..." -ForegroundColor Cyan
npx cap sync android
if ($LASTEXITCODE -ne 0) {
    Write-Host "Error: Failed to sync Capacitor" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Step 4: Build APK
Write-Host ""
Write-Host "Step 4: Building APK..." -ForegroundColor Cyan
Set-Location android
.\gradlew.bat assembleDebug
$buildResult = $LASTEXITCODE
Set-Location ..

if ($buildResult -ne 0) {
    Write-Host ""
    Write-Host "Command line build failed. Let's try Android Studio instead:" -ForegroundColor Yellow
    Write-Host "1. Open Android Studio" -ForegroundColor White
    Write-Host "2. Go to Build > Build Bundle(s) / APK(s) > Build APK(s)" -ForegroundColor White
    Write-Host ""
    Write-Host "Opening Android Studio now..." -ForegroundColor Cyan
    npx cap open android
    Read-Host "Press Enter after building in Android Studio"
} else {
    Write-Host ""
    Write-Host "========================================" -ForegroundColor Green
    Write-Host "SUCCESS! APK built successfully!" -ForegroundColor Green
    Write-Host "========================================" -ForegroundColor Green
    Write-Host ""
    Write-Host "Your APK is located at:" -ForegroundColor Yellow
    Write-Host "android\app\build\outputs\apk\debug\app-debug.apk" -ForegroundColor White
    
    # Check if APK exists and show file info
    $apkPath = "android\app\build\outputs\apk\debug\app-debug.apk"
    if (Test-Path $apkPath) {
        $apkInfo = Get-Item $apkPath
        Write-Host ""
        Write-Host "APK Details:" -ForegroundColor Yellow
        Write-Host "Size: $([math]::Round($apkInfo.Length / 1MB, 2)) MB" -ForegroundColor White
        Write-Host "Created: $($apkInfo.CreationTime)" -ForegroundColor White
    }
}

Write-Host ""
Read-Host "Press Enter to exit"
