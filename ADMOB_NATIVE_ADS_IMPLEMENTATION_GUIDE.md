# AdMob Native Ads Implementation Guide for Capacitor

## Overview
This guide provides a complete implementation of AdMob Native Ads in a Capacitor React application, ensuring compliance with Google's AdMob policies and requirements.

## Prerequisites
- Capacitor project setup
- AdMob account with app registered
- Native ad unit IDs from AdMob console

## 1. Plugin Installation

```bash
npm install capacitor-admob-ads
npx cap sync
```

## 2. Android Configuration

### 2.1 Update `android/app/src/main/AndroidManifest.xml`
```xml
<application>
    <!-- AdMob App ID -->
    <meta-data
        android:name="com.google.android.gms.ads.APPLICATION_ID"
        android:value="ca-app-pub-YOUR_APP_ID~YOUR_APP_ID"/>
    
    <!-- Network Security Config for test ads -->
    <meta-data
        android:name="android.webkit.WebView.MetricsOptOut"
        android:value="true" />
</application>

<!-- Required permissions -->
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
```

### 2.2 Update `android/app/build.gradle`
```gradle
dependencies {
    implementation 'com.google.android.gms:play-services-ads:22.6.0'
    // ... other dependencies
}
```

## 3. iOS Configuration (if needed)

### 3.1 Update `ios/App/App/Info.plist`
```xml
<key>GADApplicationIdentifier</key>
<string>ca-app-pub-YOUR_APP_ID~YOUR_APP_ID</string>
```

## 4. React Component Implementation

### 4.1 Create NativeAd Component (`src/components/Dashboard/NativeAd.jsx`)

```jsx
import React, { useState, useEffect } from 'react'
import { AdMob } from 'capacitor-admob-ads'
import './NativeAd.css'

const NativeAd = ({ theme, className = '' }) => {
  const [adStatus, setAdStatus] = useState('initializing')
  const [ads, setAds] = useState([])
  const [error, setError] = useState(null)

  // AdMob configuration
  const AD_UNIT_ID = 'ca-app-pub-3940256099942544/2247696110' // Test ID
  const APP_ID = 'ca-app-pub-3940256099942544~3347511713' // Test ID

  useEffect(() => {
    initializeAdMob()
  }, [])

  const initializeAdMob = async () => {
    try {
      setAdStatus('initializing')
      
      // Initialize AdMob
      await AdMob.initialize({
        requestTrackingAuthorization: true,
        testingDevices: ['YOUR_TEST_DEVICE_ID'],
        initializeForTesting: true
      })

      // Load native ad
      await loadNativeAd()
    } catch (error) {
      console.error('AdMob initialization failed:', error)
      setError(error.message)
      setAdStatus('error')
    }
  }

  const loadNativeAd = async () => {
    try {
      setAdStatus('loading')
      
      const result = await AdMob.loadNativeAd({
        adUnitId: AD_UNIT_ID,
        nativeAdOptions: {
          adChoicesPlacement: 'TOP_RIGHT',
          mediaAspectRatio: 'LANDSCAPE'
        }
      })

      if (result && result.ads && result.ads.length > 0) {
        setAds(result.ads)
        setAdStatus('loaded')
      } else {
        setAdStatus('no-ads')
      }
    } catch (error) {
      console.error('Failed to load native ad:', error)
      setError(error.message)
      setAdStatus('error')
    }
  }

  const handleAdClick = async (adId) => {
    try {
      await AdMob.recordNativeAdClick({ adId })
    } catch (error) {
      console.error('Failed to record ad click:', error)
    }
  }

  if (adStatus === 'error' || adStatus === 'no-ads') {
    return null // Hide component on error or no ads
  }

  if (adStatus === 'loading' || adStatus === 'initializing') {
    return (
      <div className={`native-ad-container ${className}`}>
        <div className={`p-4 rounded-lg ${theme.card} border ${theme.border}`}>
          <div className="flex items-center justify-center h-32">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={`native-ad-container ${className}`}>
      {ads.length > 0 && (
        <div className="space-y-4">
          {ads.map((ad, index) => (
            <div 
              key={ad.id || index} 
              className={`relative p-4 rounded-lg ${theme.card} border ${theme.border}`}
              style={{ minWidth: '120px', minHeight: '120px' }}
            >
              {/* Ad Attribution Label */}
              <div className="ad-attribution">
                Ad
              </div>
              
              {/* AdChoices Button */}
              <button className="ad-choices-button">
                AdChoices
              </button>
              
              {/* Ad Content */}
              <div className="flex items-start space-x-3">
                {ad.icon && (
                  <img 
                    src={ad.icon} 
                    alt="Ad Icon" 
                    className="w-12 h-12 rounded-lg flex-shrink-0"
                    crossOrigin="anonymous"
                  />
                )}
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className={`text-sm font-medium ${theme.text} truncate`}>
                      {ad.headline || 'Sponsored Content'}
                    </h3>
                  </div>
                </div>
              </div>
              
              <div className="space-y-3">
                <p className={`text-sm ${theme.text}`}>{ad.body}</p>
                
                {ad.cover && (
                  <div className="rounded-lg overflow-hidden bg-gray-200 min-h-[120px]">
                    <img 
                      src={ad.cover} 
                      alt="Ad Cover" 
                      className="w-full h-40 object-cover"
                      style={{ minWidth: '120px', minHeight: '120px' }}
                      crossOrigin="anonymous"
                    />
                  </div>
                )}
                
                <button 
                  className={`w-full py-2 ${theme.primary} text-white rounded-lg text-sm font-medium`}
                  onClick={() => handleAdClick(ad.id)}
                >
                  {ad.cta || 'Learn More'}
                </button>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}

export default NativeAd
```

### 4.2 Create CSS Styles (`src/components/Dashboard/NativeAd.css`)

```css
/* Native Ad Component Styles */
.native-ad-container {
  width: 100%;
  min-height: 120px; /* Ensure minimum height for AdMob requirements */
}

/* AdMob Native Ad Requirements */
.native-ad-container > div {
  min-width: 120px;
  min-height: 120px;
}

/* Ad Attribution Styling */
.ad-attribution {
  background: rgba(0, 0, 0, 0.6);
  color: white;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 3px;
  position: absolute;
  top: 8px;
  left: 8px;
  z-index: 10;
}

/* AdChoices Button Styling */
.ad-choices-button {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid #ddd;
  font-size: 10px;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  position: absolute;
  top: 8px;
  right: 8px;
  z-index: 10;
}
```

## 5. Integration in Pages

### 5.1 Import and Use in Components
```jsx
import NativeAd from '../components/Dashboard/NativeAd'

// In your page component
<div className="space-y-4">
  <FirstCard />
  <NativeAd theme={theme} className="my-4" />
  <SecondCard />
  {/* ... other cards */}
</div>
```

## 6. AdMob Compliance Requirements

### 6.1 Critical Size Requirements
- **Minimum Size**: Native ads must be at least 32x32dp (≈120px at 1x density)
- **Video Ads**: MediaView must be at least 120x120dp for video content
- **Ad Attribution Badge**: Minimum 15px height and width
- **AdChoices Overlay**: Minimum 15px height and width

### 6.2 Required Elements (MUST IMPLEMENT)
- **Ad Attribution**: Display "Ad", "Advertisement", or "Sponsored" (minimum 15px)
- **AdChoices**: Automatically added by SDK, must be clearly visible
- **Title**: Required, support up to 25 characters without truncation
- **Call to Action**: Required button/text, support up to 15 characters
- **Icon**: Required if provided, must be square aspect ratio (1:1)

### 6.3 Text Truncation Rules
- **Title**: No truncation up to 25 characters (12.5 for Asian languages)
- **Body**: No truncation up to 90 characters (45 for Asian languages)
- **CTA**: No truncation up to 15 characters
- **Advertiser**: No truncation up to 25 characters

### 6.4 Click Handling Policies
- **No White Space Clicks**: Background must not be clickable
- **Proper Click Areas**: Only designated ad elements should be clickable
- **AdChoices Separate**: AdChoices must have separate click handling

### 6.5 Visual Requirements
- **Text Contrast**: Sufficient contrast for accessibility
- **Clear Differentiation**: Ads must be distinguishable from app content
- **No Camouflaging**: Don't hide ad attribution or AdChoices

## 7. Testing and Debugging

### 7.1 Test Ad Unit IDs
```javascript
// Use these for testing
const TEST_AD_UNITS = {
  NATIVE: 'ca-app-pub-3940256099942544/2247696110',
  APP_ID: 'ca-app-pub-3940256099942544~3347511713'
}
```

### 7.2 Debug Implementation
- Monitor console logs for AdMob events
- Test on real devices (not emulators)
- Verify ad attribution and AdChoices visibility

## 8. Production Deployment

### 8.1 Replace Test IDs
- Update with your actual AdMob app and ad unit IDs
- Remove test device configurations
- Test thoroughly before release

### 8.2 Build and Deploy
```bash
npm run build
npx cap sync android
cd android && ./gradlew assembleRelease
```

## 9. Common Issues and Solutions

### 9.1 Ads Not Loading
- Verify internet connection
- Check AdMob account status
- Ensure correct ad unit IDs
- Test with test ad units first

### 9.2 Validation Errors
- Ensure minimum size requirements (32x32dp)
- Add proper ad attribution labels
- Include AdChoices button
- Test on real devices

### 9.3 Performance Optimization
- Load ads asynchronously
- Implement proper error handling
- Cache ad responses when possible
- Monitor ad performance metrics

## 10. Best Practices

1. **User Experience**: Don't overwhelm users with too many ads
2. **Performance**: Load ads efficiently without blocking UI
3. **Compliance**: Always follow AdMob policies and guidelines
4. **Testing**: Test thoroughly on multiple devices and network conditions
5. **Monitoring**: Track ad performance and user engagement

## Conclusion

This implementation provides a robust, compliant AdMob native ads solution for Capacitor applications. The component handles initialization, loading, error states, and ensures compliance with Google's requirements.

Remember to always test with real devices and follow AdMob's latest guidelines for the best results.
