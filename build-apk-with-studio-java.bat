@echo off
echo ========================================
echo Building Prepaid Meter Android APK
echo (Using Android Studio's Java)
echo ========================================

echo.
echo Setting up Java environment...
set JAVA_HOME=C:\Program Files\Android\Android Studio\jbr
set PATH=%JAVA_HOME%\bin;%PATH%

echo Java Home: %JAVA_HOME%
echo.

echo Step 1: Building React app for production...
call npm run build
if %errorlevel% neq 0 (
    echo Error: Failed to build React app
    pause
    exit /b 1
)

echo.
echo Step 2: Copying web assets to Android...
call npx cap copy android
if %errorlevel% neq 0 (
    echo Error: Failed to copy assets
    pause
    exit /b 1
)

echo.
echo Step 3: Syncing Capacitor...
call npx cap sync android
if %errorlevel% neq 0 (
    echo Error: Failed to sync Capacitor
    pause
    exit /b 1
)

echo.
echo Step 4: Building APK with Android Studio's Java...
cd android
call gradlew assembleDebug
if %errorlevel% neq 0 (
    echo Error: Failed to build APK
    echo.
    echo Try using Android Studio instead:
    echo 1. Open Android Studio
    echo 2. Go to Build ^> Build Bundle(s) / APK(s) ^> Build APK(s)
    cd ..
    pause
    exit /b 1
)
cd ..

echo.
echo ========================================
echo SUCCESS! APK built successfully!
echo ========================================
echo.
echo Your APK is located at:
echo android\app\build\outputs\apk\debug\app-debug.apk
echo.
echo File size:
dir "android\app\build\outputs\apk\debug\app-debug.apk" | findstr "app-debug.apk"
echo.
echo You can now install this APK on your phone and tablet!
echo.
echo Installation steps:
echo 1. Copy the APK file to your device
echo 2. Enable "Unknown Sources" in device settings
echo 3. Tap the APK file to install
echo.
pause
