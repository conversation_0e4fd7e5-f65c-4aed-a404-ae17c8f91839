<?xml version="1.0" encoding="utf-8"?>
<resources>

    <!-- Base application theme. -->
    <style name="AppTheme" parent="Theme.AppCompat.Light.DarkActionBar">
        <!-- Customize your theme here. -->
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
        <!-- Navigation bar styling for base theme -->
        <item name="android:navigationBarColor">@color/navigationBarColor</item>
        <item name="android:windowLightNavigationBar">false</item>
        <item name="android:statusBarColor">@color/statusBarColor</item>
        <item name="android:windowLightStatusBar">false</item>
    </style>

    <style name="AppTheme.NoActionBar" parent="Theme.AppCompat.DayNight.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="android:background">@null</item>
        <!-- Navigation bar styling -->
        <item name="android:navigationBarColor">@color/navigationBarColor</item>
        <item name="android:windowLightNavigationBar">false</item>
        <item name="android:statusBarColor">@color/statusBarColor</item>
        <item name="android:windowLightStatusBar">false</item>
    </style>


    <style name="AppTheme.NoActionBarLaunch" parent="Theme.SplashScreen">
        <item name="android:background">@drawable/splash</item>
        <!-- Navigation bar styling for launch theme -->
        <item name="android:navigationBarColor">@color/navigationBarColor</item>
        <item name="android:windowLightNavigationBar">false</item>
        <item name="android:statusBarColor">@color/statusBarColor</item>
        <item name="android:windowLightStatusBar">false</item>
    </style>
</resources>