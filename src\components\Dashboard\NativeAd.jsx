import React, { useState, useEffect, useRef } from 'react'
import { Capacitor } from '@capacitor/core'
import { useTheme } from '../../context/ThemeContext'
import { ADMOB_CONFIG, getAdUnitId, validateAdText } from '../../config/admob.config'
import './NativeAd.css'

const NativeAd = ({ className = '' }) => {
  const { theme } = useTheme()
  const [ads, setAds] = useState([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState(null)
  const [adStatus, setAdStatus] = useState('idle') // idle, loading, loaded, error, no-ads
  const adContainerRef = useRef(null)

  // Get native ad unit ID from configuration
  const AD_UNIT_ID = getAdUnitId('native')

  // Get the AdmobAds plugin from Capacitor
  const getAdMobPlugin = () => {
    try {
      // Try our custom native plugin first (for proper validator support)
      const { AdMobNative } = Capacitor.Plugins
      if (AdMobNative) {
        return AdMobNative
      }

      // Fallback to capacitor-admob-ads plugin
      const { AdmobAds } = Capacitor.Plugins
      if (!AdmobAds) {
        throw new Error('AdmobAds plugin not found. Make sure capacitor-admob-ads is installed and synced.')
      }
      return AdmobAds
    } catch (error) {
      throw error
    }
  }

  const loadNativeAd = async () => {
    setIsLoading(true)
    setError(null)
    setAdStatus('loading')

    try {
      const AdMobPlugin = getAdMobPlugin()

      // Check if we're using our custom native plugin
      const isCustomPlugin = AdMobPlugin.constructor.name === 'AdMobNative' ||
                            AdMobPlugin.loadNativeAd.toString().includes('adUnitId')

      let result
      if (isCustomPlugin) {
        // Use our custom plugin with proper validator support
        console.log('Using custom AdMobNative plugin for proper validator support')
        result = await AdMobPlugin.loadNativeAd({
          adUnitId: AD_UNIT_ID
        })

        // Transform result to match expected format
        if (result && result.success && result.adData) {
          result = {
            ads: [{
              id: 'native-ad-1',
              headline: result.adData.headline,
              body: result.adData.body,
              cta: result.adData.callToAction,
              icon: result.adData.icon,
              cover: null, // MediaView handles this
              advertiser: result.adData.advertiser,
              adChoicesUrl: null // Handled by SDK
            }]
          }
        }
      } else {
        // Use capacitor-admob-ads plugin as fallback
        console.log('Using capacitor-admob-ads plugin (limited validator support)')
        result = await AdMobPlugin.loadNativeAd({
          adId: AD_UNIT_ID,
          isTesting: true,
          adsCount: 1,
          nativeAdOptions: {
            adChoicesPlacement: 'TOP_RIGHT',
            mediaAspectRatio: 'LANDSCAPE',
            requestMultipleImages: false
          }
        })
      }

      console.log('AdMob Load Result:', JSON.stringify(result, null, 2))

      if (result && result.ads && result.ads.length > 0) {
        setAds(result.ads)
        setAdStatus('loaded')
        setError(null)

        // Log ad structure for debugging validator issues
        result.ads.forEach((ad, index) => {
          console.log(`Ad ${index} Structure:`, {
            hasHeadline: !!ad.headline,
            hasBody: !!ad.body,
            hasCta: !!ad.cta,
            hasIcon: !!ad.icon,
            hasCover: !!ad.cover,
            hasAdvertiser: !!ad.advertiser,
            hasAdChoicesUrl: !!ad.adChoicesUrl,
            headlineLength: ad.headline?.length || 0,
            bodyLength: ad.body?.length || 0,
            ctaLength: ad.cta?.length || 0
          })
        })
      } else if (result && result.success === false) {
        setAdStatus('error')
        setError('Failed to load ad: ' + (result.error || 'Unknown error'))
      } else {
        setAdStatus('no-ads')
        setError('No advertisements available at this time. Result: ' + JSON.stringify(result))
      }
    } catch (error) {
      // More detailed error information
      const errorMsg = error.message || error.toString()
      console.error('AdMob Load Error:', error)
      setError('Failed to load advertisement: ' + errorMsg)
      setAdStatus('error')
    } finally {
      setIsLoading(false)
    }
  }

  const handleAdClick = async (adId) => {
    try {
      const AdmobAds = getAdMobPlugin()
      await AdmobAds.triggerNativeAd({ id: adId })
    } catch (error) {
      // Show user-friendly error
      if (typeof window !== 'undefined') {
        alert(`Ad Click Error: ${error.message}`)
      }
    }
  }

  // Initialize and load native ads
  const initializeAndLoadAds = async () => {
    try {
      const AdMobPlugin = getAdMobPlugin()

      // Check if we're using our custom plugin that needs initialization
      const isCustomPlugin = AdMobPlugin.constructor.name === 'AdMobNative' ||
                            AdMobPlugin.initialize

      if (isCustomPlugin && AdMobPlugin.initialize) {
        console.log('Initializing custom AdMobNative plugin...')
        const initResult = await AdMobPlugin.initialize()
        console.log('AdMob initialization result:', initResult)

        if (!initResult.success) {
          throw new Error(initResult.error || 'Failed to initialize AdMob')
        }
      }

      // Load the native ad
      await loadNativeAd()
    } catch (error) {
      setError('Failed to load AdMob: ' + error.message)
      setAdStatus('error')
    }
  }

  useEffect(() => {
    // Initialize AdMob and load ads on all platforms
    // Small delay to ensure container is rendered
    setTimeout(initializeAndLoadAds, 1000)
  }, [])

  // Show platform indicator for web testing
  const isWeb = Capacitor.getPlatform() === 'web'

  // Validator debugging function
  const validateAdStructure = (ad) => {
    const issues = []

    // Check required components
    if (!ad.headline || ad.headline.length === 0) {
      issues.push('❌ Missing headline (required)')
    } else if (ad.headline.length > 25) {
      issues.push('⚠️ Headline too long (>25 chars)')
    }

    if (!ad.cta || ad.cta.length === 0) {
      issues.push('❌ Missing call-to-action (required)')
    } else if (ad.cta.length > 15) {
      issues.push('⚠️ CTA too long (>15 chars)')
    }

    if (ad.body && ad.body.length > 90) {
      issues.push('⚠️ Body text too long (>90 chars)')
    }

    if (!ad.icon) {
      issues.push('⚠️ Missing icon (recommended)')
    }

    if (!ad.advertiser) {
      issues.push('⚠️ Missing advertiser name (recommended)')
    }

    return issues
  }

  // Don't render if no ads or error
  if (adStatus === 'error' || adStatus === 'no-ads') {
    return (
      <div className={`native-ad-container ${className}`}>
        <div className="text-center py-4 text-gray-500 dark:text-gray-400 text-sm">
          {error || 'Advertisement unavailable'}
        </div>
      </div>
    )
  }

  // Don't render anything if loading and no ads
  if (isLoading && ads.length === 0) {
    return (
      <div className={`native-ad-container ${className}`}>
        <div className="native-ad-loading">
          <div className="loading-spinner"></div>
          <p className="loading-text">Loading advertisement...</p>
        </div>
      </div>
    )
  }

  return (
    <div className={`native-ad-container ${className}`}>
      {/* Platform indicator for testing */}
      {isWeb && (
        <div className={`mb-2 px-3 py-1 rounded-full text-xs ${theme.background} border ${theme.border} inline-block`}>
          🔧 Web Testing Mode (Mock Ads)
        </div>
      )}



      {/* Display native ads if available */}
      {ads.length > 0 && ads.map((ad, index) => (
        <div
          key={ad.id || index}
          ref={adContainerRef}
          className={`relative rounded-lg ${theme.card} border ${theme.border} overflow-hidden cursor-pointer`}
          style={{
            minWidth: '120px',  // AdMob minimum requirement
            minHeight: '120px', // AdMob minimum requirement for MediaView
            width: 'auto',
            height: 'auto'
          }}
          onClick={() => handleAdClick(ad.id)}
        >
          {/* Ad Attribution Badge - AdMob Compliant (minimum 15px) */}
          <div
            className="ad-attribution-badge"
            style={{
              position: 'absolute',
              top: '4px',
              left: '4px',
              backgroundColor: 'rgba(0, 0, 0, 0.9)',
              color: 'white',
              fontSize: '10px',
              fontWeight: '700',
              padding: '2px 6px',
              borderRadius: '2px',
              zIndex: 30,
              minWidth: '15px',
              minHeight: '15px',
              textTransform: 'uppercase',
              letterSpacing: '0.5px',
              fontFamily: 'Arial, sans-serif'
            }}
          >
            AD
          </div>

          {/* AdChoices Overlay - AdMob Compliant */}
          <div
            className="ad-choices-overlay"
            style={{
              position: 'absolute',
              top: '4px',
              right: '4px',
              backgroundColor: 'rgba(255, 255, 255, 0.95)',
              border: '1px solid #ddd',
              fontSize: '12px',
              fontWeight: '500',
              padding: '4px 6px',
              borderRadius: '3px',
              zIndex: 30,
              minWidth: '15px',
              minHeight: '15px',
              color: '#666',
              cursor: 'pointer',
              fontFamily: 'Arial, sans-serif'
            }}
            onClick={(e) => {
              e.stopPropagation()
              // Handle AdChoices click if URL is available
              if (ad.adChoicesUrl) {
                window.open(ad.adChoicesUrl, '_blank')
              }
            }}
          >
            ⓘ
          </div>

          {/* Clickable Ad Content Area */}
          <div
            className="ad-content-clickable p-4"
            style={{ paddingTop: '40px' }} // Space for attribution and AdChoices
          >
            {/* Ad Content Layout */}
            <div className="flex items-start space-x-3 mb-3">
              {/* Icon (Required if provided) */}
              {ad.icon && (
                <img
                  src={ad.icon}
                  alt="Ad Icon"
                  className="w-12 h-12 rounded-lg flex-shrink-0"
                  style={{ aspectRatio: '1:1' }} // Square aspect ratio required
                  crossOrigin="anonymous"
                  onError={(e) => {
                    e.target.style.display = 'none'
                  }}
                />
              )}

              {/* Title and Advertiser */}
              <div className="flex-1 min-w-0">
                {/* Title (Required - max 25 chars before truncation) */}
                <h3
                  className={`text-sm font-semibold ${theme.text} mb-1`}
                  style={{
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap',
                    maxWidth: '100%'
                  }}
                  title={ad.headline || 'Sponsored Content'}
                >
                  {validateAdText(ad.headline || 'Sponsored Content', 'headline')}
                </h3>

                {/* Advertiser Name (Recommended) */}
                {ad.advertiser && (
                  <p className={`text-xs ${theme.textSecondary}`}>
                    {validateAdText(ad.advertiser, 'advertiser')}
                  </p>
                )}
              </div>
            </div>

            {/* Body Text (Recommended - max 90 chars before truncation) */}
            {ad.body && (
              <p
                className={`text-sm ${theme.text} mb-3`}
                style={{ lineHeight: '1.4' }}
              >
                {validateAdText(ad.body, 'body')}
              </p>
            )}

            {/* Cover Image */}
            {ad.cover && (
              <div className="rounded-lg overflow-hidden mb-3">
                <img
                  src={ad.cover}
                  alt="Ad Media"
                  className="w-full h-32 object-cover"
                  crossOrigin="anonymous"
                  onError={(e) => {
                    e.target.style.display = 'none'
                  }}
                />
              </div>
            )}

            {/* Call to Action Button (Required) */}
            <button
              className={`w-full py-3 px-4 ${theme.primary} text-white rounded-lg text-sm font-semibold transition-colors hover:opacity-90`}
              style={{ minHeight: '40px' }}
            >
              {validateAdText(ad.cta || 'Learn More', 'cta')}
            </button>
          </div>

          {/* AdChoices */}
          {ad.adChoicesUrl && (
            <div className="ad-choices">
              <button
                className="ad-choices-button"
                onClick={(e) => {
                  e.stopPropagation()
                  window.open(ad.adChoicesUrl, '_blank')
                }}
              >
                ⓘ
              </button>
            </div>
          )}
        </div>
      ))}
    </div>
  )
}

export default NativeAd
