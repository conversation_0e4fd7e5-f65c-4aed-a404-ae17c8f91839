/* AdMob Native Ad Component - Policy Compliant Styles */
.native-ad-container {
  width: 100%;
  min-height: 120px; /* AdMob requirement: minimum 120px for video ads */
}

/* AdMob Native Ad Requirements - Minimum Size Compliance */
.native-ad-container > div {
  min-width: 120px;  /* AdMob requirement: minimum 32dp (≈120px) */
  min-height: 120px; /* AdMob requirement: minimum 32dp (≈120px) */
}

/* Ad Attribution Badge - AdMob Policy Compliant */
.ad-attribution-badge {
  /* Minimum 15px height and width as per AdMob policy */
  min-width: 15px !important;
  min-height: 15px !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  user-select: none;
  pointer-events: none; /* Not clickable as per policy */
}

/* AdChoices Overlay - AdMob Policy Compliant */
.ad-choices-overlay {
  /* Minimum 15px height and width as per AdMob policy */
  min-width: 15px !important;
  min-height: 15px !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  transition: all 0.2s ease;
}

.ad-choices-overlay:hover {
  background-color: rgba(255, 255, 255, 1) !important;
  border-color: #999 !important;
}

/* Ad Content Clickable Area - No White Space Clicks */
.ad-content-clickable {
  /* Ensure no background clicks (AdMob policy) */
  background: transparent;
  border: none;
  outline: none;
}

.ad-content-clickable:focus {
  outline: 2px solid rgba(59, 130, 246, 0.5);
  outline-offset: 2px;
}

/* Text Contrast and Legibility - AdMob Requirements */
.native-ad-container h3,
.native-ad-container p,
.native-ad-container button {
  /* Ensure sufficient contrast for accessibility */
  text-shadow: none;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Responsive Design - Maintain Minimum Sizes */
@media (max-width: 480px) {
  .native-ad-container > div {
    min-width: 120px;  /* Maintain minimum even on small screens */
    min-height: 120px;
  }
}
