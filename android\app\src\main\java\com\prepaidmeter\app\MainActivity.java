package com.prepaidmeter.app;

import android.os.Bundle;

import com.getcapacitor.BridgeActivity;
import com.capacitorjs.plugins.localnotifications.LocalNotificationsPlugin;
import com.g12.capacitor.admob.ads.AdmobAdsPlugin;
import com.prepaidmeter.app.AdMobNativePlugin;

public class MainActivity extends BridgeActivity {
    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // Navigation bar and status bar colors are now handled dynamically by JavaScript

        // Register the LocalNotifications plugin
        registerPlugin(LocalNotificationsPlugin.class);

        // Register the AdmobAds plugin
        registerPlugin(AdmobAdsPlugin.class);

        // Register our custom AdMob Native plugin
        registerPlugin(AdMobNativePlugin.class);
    }


}
