import sharp from 'sharp';
import fs from 'fs';
import path from 'path';

// Web icon sizes
const webIcons = [
  { size: 48, name: 'icon-48.webp' },
  { size: 72, name: 'icon-72.webp' },
  { size: 96, name: 'icon-96.webp' },
  { size: 128, name: 'icon-128.webp' },
  { size: 192, name: 'icon-192.webp' },
  { size: 256, name: 'icon-256.webp' },
  { size: 512, name: 'icon-512.webp' }
];

const sourceIcon = 'resources/icon.png';
const outputDir = 'icons';

async function generateWebIcons() {
  console.log('🌐 Generating web icons from your custom logo...');

  try {
    // Check if source icon exists
    if (!fs.existsSync(sourceIcon)) {
      console.error('❌ Source icon not found:', sourceIcon);
      return;
    }

    console.log('✅ Found source icon:', sourceIcon);

    // Ensure output directory exists
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
      console.log(`📁 Created directory: ${outputDir}`);
    }

    for (const icon of webIcons) {
      const outputPath = path.join(outputDir, icon.name);
      
      await sharp(sourceIcon)
        .resize(icon.size, icon.size, {
          fit: 'contain',
          background: { r: 255, g: 255, b: 255, alpha: 0 }
        })
        .webp({ quality: 90 })
        .toFile(outputPath);

      console.log(`✅ Generated ${icon.name} (${icon.size}x${icon.size})`);
    }

    console.log('🎉 All web icons generated successfully!');
    console.log('🌐 Your custom logo will now appear as web app icons');

  } catch (error) {
    console.error('❌ Error generating web icons:', error);
  }
}

generateWebIcons();
