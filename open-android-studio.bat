@echo off
echo ========================================
echo Opening Android Studio with your project
echo ========================================
echo.
echo This will open Android Studio with your Prepaid Meter project.
echo.
echo After it opens:
echo 1. Wait for <PERSON>radle sync to complete
echo 2. Go to Build ^> Build Bundle(s) / APK(s) ^> Build APK(s)
echo 3. Wait for build to complete
echo 4. Click "locate" to find your APK
echo.
echo Opening Android Studio now...
echo.

npx cap open android

echo.
echo If Android Studio didn't open, try opening it manually:
echo 1. Open Android Studio
echo 2. File ^> Open
echo 3. Select the "android" folder in your project
echo 4. Click OK
echo.
pause
