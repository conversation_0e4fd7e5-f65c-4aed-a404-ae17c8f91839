function Y(r){"@babel/helpers - typeof";return Y=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Y(r)}function O(r){if(r===null||r===!0||r===!1)return NaN;var e=Number(r);return isNaN(e)?e:e<0?Math.ceil(e):Math.floor(e)}function h(r,e){if(e.length<r)throw new TypeError(r+" argument"+(r>1?"s":"")+" required, but only "+e.length+" present")}function b(r){h(1,arguments);var e=Object.prototype.toString.call(r);return r instanceof Date||Y(r)==="object"&&e==="[object Date]"?new Date(r.getTime()):typeof r=="number"||e==="[object Number]"?new Date(r):((typeof r=="string"||e==="[object String]")&&typeof console<"u"&&(console.warn("Starting with v2.0.0-beta.1 date-fns doesn't accept strings as date arguments. Please use `parseISO` to parse strings. See: https://github.com/date-fns/date-fns/blob/master/docs/upgradeGuide.md#string-arguments"),console.warn(new Error().stack)),new Date(NaN))}function K(r,e){h(2,arguments);var t=b(r).getTime(),a=O(e);return new Date(t+a)}var z={};function N(){return z}function Z(r){var e=new Date(Date.UTC(r.getFullYear(),r.getMonth(),r.getDate(),r.getHours(),r.getMinutes(),r.getSeconds(),r.getMilliseconds()));return e.setUTCFullYear(r.getFullYear()),r.getTime()-e.getTime()}function ee(r){return h(1,arguments),r instanceof Date||Y(r)==="object"&&Object.prototype.toString.call(r)==="[object Date]"}function te(r){if(h(1,arguments),!ee(r)&&typeof r!="number")return!1;var e=b(r);return!isNaN(Number(e))}function re(r,e){h(2,arguments);var t=O(e);return K(r,-t)}var ae=864e5;function ne(r){h(1,arguments);var e=b(r),t=e.getTime();e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0);var a=e.getTime(),n=t-a;return Math.floor(n/ae)+1}function E(r){h(1,arguments);var e=1,t=b(r),a=t.getUTCDay(),n=(a<e?7:0)+a-e;return t.setUTCDate(t.getUTCDate()-n),t.setUTCHours(0,0,0,0),t}function G(r){h(1,arguments);var e=b(r),t=e.getUTCFullYear(),a=new Date(0);a.setUTCFullYear(t+1,0,4),a.setUTCHours(0,0,0,0);var n=E(a),i=new Date(0);i.setUTCFullYear(t,0,4),i.setUTCHours(0,0,0,0);var o=E(i);return e.getTime()>=n.getTime()?t+1:e.getTime()>=o.getTime()?t:t-1}function ie(r){h(1,arguments);var e=G(r),t=new Date(0);t.setUTCFullYear(e,0,4),t.setUTCHours(0,0,0,0);var a=E(t);return a}var oe=6048e5;function ue(r){h(1,arguments);var e=b(r),t=E(e).getTime()-ie(e).getTime();return Math.round(t/oe)+1}function _(r,e){var t,a,n,i,o,s,l,d;h(1,arguments);var f=N(),c=O((t=(a=(n=(i=e?.weekStartsOn)!==null&&i!==void 0?i:e==null||(o=e.locale)===null||o===void 0||(s=o.options)===null||s===void 0?void 0:s.weekStartsOn)!==null&&n!==void 0?n:f.weekStartsOn)!==null&&a!==void 0?a:(l=f.locale)===null||l===void 0||(d=l.options)===null||d===void 0?void 0:d.weekStartsOn)!==null&&t!==void 0?t:0);if(!(c>=0&&c<=6))throw new RangeError("weekStartsOn must be between 0 and 6 inclusively");var v=b(r),m=v.getUTCDay(),w=(m<c?7:0)+m-c;return v.setUTCDate(v.getUTCDate()-w),v.setUTCHours(0,0,0,0),v}function I(r,e){var t,a,n,i,o,s,l,d;h(1,arguments);var f=b(r),c=f.getUTCFullYear(),v=N(),m=O((t=(a=(n=(i=e?.firstWeekContainsDate)!==null&&i!==void 0?i:e==null||(o=e.locale)===null||o===void 0||(s=o.options)===null||s===void 0?void 0:s.firstWeekContainsDate)!==null&&n!==void 0?n:v.firstWeekContainsDate)!==null&&a!==void 0?a:(l=v.locale)===null||l===void 0||(d=l.options)===null||d===void 0?void 0:d.firstWeekContainsDate)!==null&&t!==void 0?t:1);if(!(m>=1&&m<=7))throw new RangeError("firstWeekContainsDate must be between 1 and 7 inclusively");var w=new Date(0);w.setUTCFullYear(c+1,0,m),w.setUTCHours(0,0,0,0);var D=_(w,e),T=new Date(0);T.setUTCFullYear(c,0,m),T.setUTCHours(0,0,0,0);var F=_(T,e);return f.getTime()>=D.getTime()?c+1:f.getTime()>=F.getTime()?c:c-1}function se(r,e){var t,a,n,i,o,s,l,d;h(1,arguments);var f=N(),c=O((t=(a=(n=(i=e?.firstWeekContainsDate)!==null&&i!==void 0?i:e==null||(o=e.locale)===null||o===void 0||(s=o.options)===null||s===void 0?void 0:s.firstWeekContainsDate)!==null&&n!==void 0?n:f.firstWeekContainsDate)!==null&&a!==void 0?a:(l=f.locale)===null||l===void 0||(d=l.options)===null||d===void 0?void 0:d.firstWeekContainsDate)!==null&&t!==void 0?t:1),v=I(r,e),m=new Date(0);m.setUTCFullYear(v,0,c),m.setUTCHours(0,0,0,0);var w=_(m,e);return w}var de=6048e5;function le(r,e){h(1,arguments);var t=b(r),a=_(t,e).getTime()-se(t,e).getTime();return Math.round(a/de)+1}function u(r,e){for(var t=r<0?"-":"",a=Math.abs(r).toString();a.length<e;)a="0"+a;return t+a}var p={y:function(e,t){var a=e.getUTCFullYear(),n=a>0?a:1-a;return u(t==="yy"?n%100:n,t.length)},M:function(e,t){var a=e.getUTCMonth();return t==="M"?String(a+1):u(a+1,2)},d:function(e,t){return u(e.getUTCDate(),t.length)},a:function(e,t){var a=e.getUTCHours()/12>=1?"pm":"am";switch(t){case"a":case"aa":return a.toUpperCase();case"aaa":return a;case"aaaaa":return a[0];case"aaaa":default:return a==="am"?"a.m.":"p.m."}},h:function(e,t){return u(e.getUTCHours()%12||12,t.length)},H:function(e,t){return u(e.getUTCHours(),t.length)},m:function(e,t){return u(e.getUTCMinutes(),t.length)},s:function(e,t){return u(e.getUTCSeconds(),t.length)},S:function(e,t){var a=t.length,n=e.getUTCMilliseconds(),i=Math.floor(n*Math.pow(10,a-3));return u(i,t.length)}},M={midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},ce={G:function(e,t,a){var n=e.getUTCFullYear()>0?1:0;switch(t){case"G":case"GG":case"GGG":return a.era(n,{width:"abbreviated"});case"GGGGG":return a.era(n,{width:"narrow"});case"GGGG":default:return a.era(n,{width:"wide"})}},y:function(e,t,a){if(t==="yo"){var n=e.getUTCFullYear(),i=n>0?n:1-n;return a.ordinalNumber(i,{unit:"year"})}return p.y(e,t)},Y:function(e,t,a,n){var i=I(e,n),o=i>0?i:1-i;if(t==="YY"){var s=o%100;return u(s,2)}return t==="Yo"?a.ordinalNumber(o,{unit:"year"}):u(o,t.length)},R:function(e,t){var a=G(e);return u(a,t.length)},u:function(e,t){var a=e.getUTCFullYear();return u(a,t.length)},Q:function(e,t,a){var n=Math.ceil((e.getUTCMonth()+1)/3);switch(t){case"Q":return String(n);case"QQ":return u(n,2);case"Qo":return a.ordinalNumber(n,{unit:"quarter"});case"QQQ":return a.quarter(n,{width:"abbreviated",context:"formatting"});case"QQQQQ":return a.quarter(n,{width:"narrow",context:"formatting"});case"QQQQ":default:return a.quarter(n,{width:"wide",context:"formatting"})}},q:function(e,t,a){var n=Math.ceil((e.getUTCMonth()+1)/3);switch(t){case"q":return String(n);case"qq":return u(n,2);case"qo":return a.ordinalNumber(n,{unit:"quarter"});case"qqq":return a.quarter(n,{width:"abbreviated",context:"standalone"});case"qqqqq":return a.quarter(n,{width:"narrow",context:"standalone"});case"qqqq":default:return a.quarter(n,{width:"wide",context:"standalone"})}},M:function(e,t,a){var n=e.getUTCMonth();switch(t){case"M":case"MM":return p.M(e,t);case"Mo":return a.ordinalNumber(n+1,{unit:"month"});case"MMM":return a.month(n,{width:"abbreviated",context:"formatting"});case"MMMMM":return a.month(n,{width:"narrow",context:"formatting"});case"MMMM":default:return a.month(n,{width:"wide",context:"formatting"})}},L:function(e,t,a){var n=e.getUTCMonth();switch(t){case"L":return String(n+1);case"LL":return u(n+1,2);case"Lo":return a.ordinalNumber(n+1,{unit:"month"});case"LLL":return a.month(n,{width:"abbreviated",context:"standalone"});case"LLLLL":return a.month(n,{width:"narrow",context:"standalone"});case"LLLL":default:return a.month(n,{width:"wide",context:"standalone"})}},w:function(e,t,a,n){var i=le(e,n);return t==="wo"?a.ordinalNumber(i,{unit:"week"}):u(i,t.length)},I:function(e,t,a){var n=ue(e);return t==="Io"?a.ordinalNumber(n,{unit:"week"}):u(n,t.length)},d:function(e,t,a){return t==="do"?a.ordinalNumber(e.getUTCDate(),{unit:"date"}):p.d(e,t)},D:function(e,t,a){var n=ne(e);return t==="Do"?a.ordinalNumber(n,{unit:"dayOfYear"}):u(n,t.length)},E:function(e,t,a){var n=e.getUTCDay();switch(t){case"E":case"EE":case"EEE":return a.day(n,{width:"abbreviated",context:"formatting"});case"EEEEE":return a.day(n,{width:"narrow",context:"formatting"});case"EEEEEE":return a.day(n,{width:"short",context:"formatting"});case"EEEE":default:return a.day(n,{width:"wide",context:"formatting"})}},e:function(e,t,a,n){var i=e.getUTCDay(),o=(i-n.weekStartsOn+8)%7||7;switch(t){case"e":return String(o);case"ee":return u(o,2);case"eo":return a.ordinalNumber(o,{unit:"day"});case"eee":return a.day(i,{width:"abbreviated",context:"formatting"});case"eeeee":return a.day(i,{width:"narrow",context:"formatting"});case"eeeeee":return a.day(i,{width:"short",context:"formatting"});case"eeee":default:return a.day(i,{width:"wide",context:"formatting"})}},c:function(e,t,a,n){var i=e.getUTCDay(),o=(i-n.weekStartsOn+8)%7||7;switch(t){case"c":return String(o);case"cc":return u(o,t.length);case"co":return a.ordinalNumber(o,{unit:"day"});case"ccc":return a.day(i,{width:"abbreviated",context:"standalone"});case"ccccc":return a.day(i,{width:"narrow",context:"standalone"});case"cccccc":return a.day(i,{width:"short",context:"standalone"});case"cccc":default:return a.day(i,{width:"wide",context:"standalone"})}},i:function(e,t,a){var n=e.getUTCDay(),i=n===0?7:n;switch(t){case"i":return String(i);case"ii":return u(i,t.length);case"io":return a.ordinalNumber(i,{unit:"day"});case"iii":return a.day(n,{width:"abbreviated",context:"formatting"});case"iiiii":return a.day(n,{width:"narrow",context:"formatting"});case"iiiiii":return a.day(n,{width:"short",context:"formatting"});case"iiii":default:return a.day(n,{width:"wide",context:"formatting"})}},a:function(e,t,a){var n=e.getUTCHours(),i=n/12>=1?"pm":"am";switch(t){case"a":case"aa":return a.dayPeriod(i,{width:"abbreviated",context:"formatting"});case"aaa":return a.dayPeriod(i,{width:"abbreviated",context:"formatting"}).toLowerCase();case"aaaaa":return a.dayPeriod(i,{width:"narrow",context:"formatting"});case"aaaa":default:return a.dayPeriod(i,{width:"wide",context:"formatting"})}},b:function(e,t,a){var n=e.getUTCHours(),i;switch(n===12?i=M.noon:n===0?i=M.midnight:i=n/12>=1?"pm":"am",t){case"b":case"bb":return a.dayPeriod(i,{width:"abbreviated",context:"formatting"});case"bbb":return a.dayPeriod(i,{width:"abbreviated",context:"formatting"}).toLowerCase();case"bbbbb":return a.dayPeriod(i,{width:"narrow",context:"formatting"});case"bbbb":default:return a.dayPeriod(i,{width:"wide",context:"formatting"})}},B:function(e,t,a){var n=e.getUTCHours(),i;switch(n>=17?i=M.evening:n>=12?i=M.afternoon:n>=4?i=M.morning:i=M.night,t){case"B":case"BB":case"BBB":return a.dayPeriod(i,{width:"abbreviated",context:"formatting"});case"BBBBB":return a.dayPeriod(i,{width:"narrow",context:"formatting"});case"BBBB":default:return a.dayPeriod(i,{width:"wide",context:"formatting"})}},h:function(e,t,a){if(t==="ho"){var n=e.getUTCHours()%12;return n===0&&(n=12),a.ordinalNumber(n,{unit:"hour"})}return p.h(e,t)},H:function(e,t,a){return t==="Ho"?a.ordinalNumber(e.getUTCHours(),{unit:"hour"}):p.H(e,t)},K:function(e,t,a){var n=e.getUTCHours()%12;return t==="Ko"?a.ordinalNumber(n,{unit:"hour"}):u(n,t.length)},k:function(e,t,a){var n=e.getUTCHours();return n===0&&(n=24),t==="ko"?a.ordinalNumber(n,{unit:"hour"}):u(n,t.length)},m:function(e,t,a){return t==="mo"?a.ordinalNumber(e.getUTCMinutes(),{unit:"minute"}):p.m(e,t)},s:function(e,t,a){return t==="so"?a.ordinalNumber(e.getUTCSeconds(),{unit:"second"}):p.s(e,t)},S:function(e,t){return p.S(e,t)},X:function(e,t,a,n){var i=n._originalDate||e,o=i.getTimezoneOffset();if(o===0)return"Z";switch(t){case"X":return R(o);case"XXXX":case"XX":return C(o);case"XXXXX":case"XXX":default:return C(o,":")}},x:function(e,t,a,n){var i=n._originalDate||e,o=i.getTimezoneOffset();switch(t){case"x":return R(o);case"xxxx":case"xx":return C(o);case"xxxxx":case"xxx":default:return C(o,":")}},O:function(e,t,a,n){var i=n._originalDate||e,o=i.getTimezoneOffset();switch(t){case"O":case"OO":case"OOO":return"GMT"+H(o,":");case"OOOO":default:return"GMT"+C(o,":")}},z:function(e,t,a,n){var i=n._originalDate||e,o=i.getTimezoneOffset();switch(t){case"z":case"zz":case"zzz":return"GMT"+H(o,":");case"zzzz":default:return"GMT"+C(o,":")}},t:function(e,t,a,n){var i=n._originalDate||e,o=Math.floor(i.getTime()/1e3);return u(o,t.length)},T:function(e,t,a,n){var i=n._originalDate||e,o=i.getTime();return u(o,t.length)}};function H(r,e){var t=r>0?"-":"+",a=Math.abs(r),n=Math.floor(a/60),i=a%60;if(i===0)return t+String(n);var o=e;return t+String(n)+o+u(i,2)}function R(r,e){if(r%60===0){var t=r>0?"-":"+";return t+u(Math.abs(r)/60,2)}return C(r,e)}function C(r,e){var t=e||"",a=r>0?"-":"+",n=Math.abs(r),i=u(Math.floor(n/60),2),o=u(n%60,2);return a+i+t+o}var Q=function(e,t){switch(e){case"P":return t.date({width:"short"});case"PP":return t.date({width:"medium"});case"PPP":return t.date({width:"long"});case"PPPP":default:return t.date({width:"full"})}},j=function(e,t){switch(e){case"p":return t.time({width:"short"});case"pp":return t.time({width:"medium"});case"ppp":return t.time({width:"long"});case"pppp":default:return t.time({width:"full"})}},fe=function(e,t){var a=e.match(/(P+)(p+)?/)||[],n=a[1],i=a[2];if(!i)return Q(e,t);var o;switch(n){case"P":o=t.dateTime({width:"short"});break;case"PP":o=t.dateTime({width:"medium"});break;case"PPP":o=t.dateTime({width:"long"});break;case"PPPP":default:o=t.dateTime({width:"full"});break}return o.replace("{{date}}",Q(n,t)).replace("{{time}}",j(i,t))},me={p:j,P:fe},he=["D","DD"],ve=["YY","YYYY"];function ge(r){return he.indexOf(r)!==-1}function we(r){return ve.indexOf(r)!==-1}function X(r,e,t){if(r==="YYYY")throw new RangeError("Use `yyyy` instead of `YYYY` (in `".concat(e,"`) for formatting years to the input `").concat(t,"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md"));if(r==="YY")throw new RangeError("Use `yy` instead of `YY` (in `".concat(e,"`) for formatting years to the input `").concat(t,"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md"));if(r==="D")throw new RangeError("Use `d` instead of `D` (in `".concat(e,"`) for formatting days of the month to the input `").concat(t,"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md"));if(r==="DD")throw new RangeError("Use `dd` instead of `DD` (in `".concat(e,"`) for formatting days of the month to the input `").concat(t,"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md"))}var be={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}},ye=function(e,t,a){var n,i=be[e];return typeof i=="string"?n=i:t===1?n=i.one:n=i.other.replace("{{count}}",t.toString()),a!=null&&a.addSuffix?a.comparison&&a.comparison>0?"in "+n:n+" ago":n};function $(r){return function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},t=e.width?String(e.width):r.defaultWidth,a=r.formats[t]||r.formats[r.defaultWidth];return a}}var pe={full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},Te={full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},Ce={full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},Oe={date:$({formats:pe,defaultWidth:"full"}),time:$({formats:Te,defaultWidth:"full"}),dateTime:$({formats:Ce,defaultWidth:"full"})},Me={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"},De=function(e,t,a,n){return Me[e]};function k(r){return function(e,t){var a=t!=null&&t.context?String(t.context):"standalone",n;if(a==="formatting"&&r.formattingValues){var i=r.defaultFormattingWidth||r.defaultWidth,o=t!=null&&t.width?String(t.width):i;n=r.formattingValues[o]||r.formattingValues[i]}else{var s=r.defaultWidth,l=t!=null&&t.width?String(t.width):r.defaultWidth;n=r.values[l]||r.values[s]}var d=r.argumentCallback?r.argumentCallback(e):e;return n[d]}}var Pe={narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},Se={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},ke={narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},xe={narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},We={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},Ue={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},Ye=function(e,t){var a=Number(e),n=a%100;if(n>20||n<10)switch(n%10){case 1:return a+"st";case 2:return a+"nd";case 3:return a+"rd"}return a+"th"},Ee={ordinalNumber:Ye,era:k({values:Pe,defaultWidth:"wide"}),quarter:k({values:Se,defaultWidth:"wide",argumentCallback:function(e){return e-1}}),month:k({values:ke,defaultWidth:"wide"}),day:k({values:xe,defaultWidth:"wide"}),dayPeriod:k({values:We,defaultWidth:"wide",formattingValues:Ue,defaultFormattingWidth:"wide"})};function x(r){return function(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},a=t.width,n=a&&r.matchPatterns[a]||r.matchPatterns[r.defaultMatchWidth],i=e.match(n);if(!i)return null;var o=i[0],s=a&&r.parsePatterns[a]||r.parsePatterns[r.defaultParseWidth],l=Array.isArray(s)?Ne(s,function(c){return c.test(o)}):_e(s,function(c){return c.test(o)}),d;d=r.valueCallback?r.valueCallback(l):l,d=t.valueCallback?t.valueCallback(d):d;var f=e.slice(o.length);return{value:d,rest:f}}}function _e(r,e){for(var t in r)if(r.hasOwnProperty(t)&&e(r[t]))return t}function Ne(r,e){for(var t=0;t<r.length;t++)if(e(r[t]))return t}function Fe(r){return function(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},a=e.match(r.matchPattern);if(!a)return null;var n=a[0],i=e.match(r.parsePattern);if(!i)return null;var o=r.valueCallback?r.valueCallback(i[0]):i[0];o=t.valueCallback?t.valueCallback(o):o;var s=e.slice(n.length);return{value:o,rest:s}}}var qe=/^(\d+)(th|st|nd|rd)?/i,Le=/\d+/i,$e={narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},He={any:[/^b/i,/^(a|c)/i]},Re={narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},Qe={any:[/1/i,/2/i,/3/i,/4/i]},Xe={narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},Ge={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},Ie={narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},je={narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},Be={narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},Ae={any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},Ve={ordinalNumber:Fe({matchPattern:qe,parsePattern:Le,valueCallback:function(e){return parseInt(e,10)}}),era:x({matchPatterns:$e,defaultMatchWidth:"wide",parsePatterns:He,defaultParseWidth:"any"}),quarter:x({matchPatterns:Re,defaultMatchWidth:"wide",parsePatterns:Qe,defaultParseWidth:"any",valueCallback:function(e){return e+1}}),month:x({matchPatterns:Xe,defaultMatchWidth:"wide",parsePatterns:Ge,defaultParseWidth:"any"}),day:x({matchPatterns:Ie,defaultMatchWidth:"wide",parsePatterns:je,defaultParseWidth:"any"}),dayPeriod:x({matchPatterns:Be,defaultMatchWidth:"any",parsePatterns:Ae,defaultParseWidth:"any"})},Je={code:"en-US",formatDistance:ye,formatLong:Oe,formatRelative:De,localize:Ee,match:Ve,options:{weekStartsOn:0,firstWeekContainsDate:1}},Ke=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,ze=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,Ze=/^'([^]*?)'?$/,et=/''/g,tt=/[a-zA-Z]/;function at(r,e,t){var a,n,i,o,s,l,d,f,c,v,m,w,D,T;h(2,arguments);var F=String(e),P=N(),S=(a=(n=void 0)!==null&&n!==void 0?n:P.locale)!==null&&a!==void 0?a:Je,q=O((i=(o=(s=(l=void 0)!==null&&l!==void 0?l:void 0)!==null&&s!==void 0?s:P.firstWeekContainsDate)!==null&&o!==void 0?o:(d=P.locale)===null||d===void 0||(f=d.options)===null||f===void 0?void 0:f.firstWeekContainsDate)!==null&&i!==void 0?i:1);if(!(q>=1&&q<=7))throw new RangeError("firstWeekContainsDate must be between 1 and 7 inclusively");var L=O((c=(v=(m=(w=void 0)!==null&&w!==void 0?w:void 0)!==null&&m!==void 0?m:P.weekStartsOn)!==null&&v!==void 0?v:(D=P.locale)===null||D===void 0||(T=D.options)===null||T===void 0?void 0:T.weekStartsOn)!==null&&c!==void 0?c:0);if(!(L>=0&&L<=6))throw new RangeError("weekStartsOn must be between 0 and 6 inclusively");if(!S.localize)throw new RangeError("locale must contain localize property");if(!S.formatLong)throw new RangeError("locale must contain formatLong property");var W=b(r);if(!te(W))throw new RangeError("Invalid time value");var B=Z(W),A=re(W,B),V={firstWeekContainsDate:q,weekStartsOn:L,locale:S,_originalDate:W},J=F.match(ze).map(function(g){var y=g[0];if(y==="p"||y==="P"){var U=me[y];return U(g,S.formatLong)}return g}).join("").match(Ke).map(function(g){if(g==="''")return"'";var y=g[0];if(y==="'")return rt(g);var U=ce[y];if(U)return we(g)&&X(g,e,String(r)),ge(g)&&X(g,e,String(r)),U(A,g,S.localize,V);if(y.match(tt))throw new RangeError("Format string contains an unescaped latin alphabet character `"+y+"`");return g}).join("");return J}function rt(r){var e=r.match(Ze);return e?e[1].replace(et,"'"):r}export{at as f};
