# AdMob Native Ads Implementation Checklist

*Comprehensive guide based on official Google AdMob documentation*

## Table of Contents
1. [Prerequisites](#prerequisites)
2. [Required Ad Elements](#required-ad-elements)
3. [Implementation Checklist](#implementation-checklist)
4. [Best Practices](#best-practices)
5. [Size and Layout Requirements](#size-and-layout-requirements)
6. [Text and Image Guidelines](#text-and-image-guidelines)
7. [Policy Compliance](#policy-compliance)
8. [Testing and Validation](#testing-and-validation)
9. [Common Pitfalls](#common-pitfalls)
10. [Resources](#resources)

## Prerequisites

### ✅ Setup Requirements
- [ ] Complete the [AdMob Get Started guide](https://developers.google.com/admob/android/quick-start)
- [ ] Add Google Mobile Ads SDK to your project
- [ ] Configure your AdMob account and create native ad units
- [ ] Enable hardware acceleration for video ads
- [ ] Set up test ad unit IDs for development

### ✅ Test Ad Unit ID
Use this test ID during development:
```
ca-app-pub-****************/**********
```

## Required Ad Elements

### ✅ Mandatory Components
- [ ] **Ad Attribution Badge**: Display "Ad", "Advertisement", or "Sponsored" (minimum 15px height/width)
- [ ] **AdChoices Overlay**: Automatically added by SDK in specified corner
- [ ] **Native Ad View Container**: All assets must be inside NativeAdView
- [ ] **Call to Action**: Required button or text (may truncate after 15 characters)
- [ ] **Title/Headline**: Required text (may truncate after 25 characters)

### ✅ Conditional Requirements
- [ ] **Icon**: Required if provided (square 1:1 aspect ratio)
- [ ] **MediaView**: Required for video ads (minimum 120x120dp/pts)
- [ ] **App Icon**: Required for app install ads
- [ ] **Download Button**: Required for app install ads

## Implementation Checklist

### ✅ AdLoader Setup
- [ ] Create AdLoader with Builder pattern
- [ ] Implement `forNativeAd()` listener
- [ ] Set up `AdListener` for lifecycle events
- [ ] Configure `NativeAdOptions` if needed
- [ ] Handle `onAdFailedToLoad()` appropriately

### ✅ Loading Ads
- [ ] Use `loadAd()` for single ad requests
- [ ] Use `loadAds()` only for Google ads (not mediation)
- [ ] Implement proper error handling
- [ ] Don't call `loadAd()` until previous request finishes
- [ ] Limit retry attempts on failures

### ✅ Displaying Ads
- [ ] Create `NativeAdView` instance
- [ ] Inflate XML layout or create programmatically
- [ ] Populate asset views with ad data
- [ ] Register each asset view with `NativeAdView`
- [ ] Register `MediaView` for large media assets
- [ ] Call `setNativeAd()` to register the ad object
- [ ] Add view to parent container

### ✅ Resource Management
- [ ] Call `destroy()` on all loaded native ads
- [ ] Destroy ads in activity's `onDestroy()` method
- [ ] Check if activity is destroyed before displaying
- [ ] Clear cache and reload ads every hour
- [ ] Limit caching to immediately visible ads only

## Best Practices

### ✅ Design Guidelines
- [ ] **Match App Design**: Use consistent fonts, colors, and styles
- [ ] **Clear Ad Indication**: Make ads distinguishable but not jarring
- [ ] **Natural Placement**: Fit ads within content flow
- [ ] **Discoverable Content**: Use larger layouts where appropriate
- [ ] **Obvious CTA**: Include clear call-to-action buttons

### ✅ Placement Strategy
- [ ] **Natural Integration**: Fit within app's content and design flow
- [ ] **No Friction**: Don't interfere with user tasks
- [ ] **User Journey Awareness**: Show ads when users are considering content
- [ ] **List Implementation**: Precache ads for list views
- [ ] **Loading Screens**: Utilize loading time for ad display

### ✅ Performance Optimization
- [ ] Precache ads for lists
- [ ] Clear cache every hour
- [ ] Only cache immediately visible ads
- [ ] Use Firebase A/B testing for designs
- [ ] Monitor performance with AdMob reports

## Size and Layout Requirements

### ✅ Minimum Sizes
- [ ] **Overall Ad**: Minimum 32x32dp (Android) or 32x32pts (iOS)
- [ ] **MediaView for Video**: Minimum 120x120dp (Android) or 120x120pts (iOS)
- [ ] **Ad Attribution**: Minimum 15px height and width

### ✅ Aspect Ratios
- [ ] **Video Elements**: 4:3, 16:9, 1:1, 3:4, or 9:16
- [ ] **Icon Assets**: 1:1 (square)
- [ ] **Image Cropping**: Max 10% symmetric width cropping (no height cropping)

### ✅ File Size Limits
- [ ] **Video Assets**: Maximum 512 MB
- [ ] **Image Assets**: Maximum 1 million bytes

## Text and Image Guidelines

### ✅ Text Requirements
- [ ] **Sufficient Contrast**: Ensure text is clearly legible
- [ ] **No Truncation Limits**:
  - Title: Support up to 25 characters
  - Body: Support up to 90 characters
  - CTA: Support up to 15 characters
- [ ] **Consistent Font Size**: Match surrounding app elements
- [ ] **Localization**: Support appropriate languages

### ✅ Image/Video Guidelines
- [ ] **No Distortion**: Don't stretch or squeeze assets
- [ ] **Maintain Aspect Ratios**: Use proper scaling only
- [ ] **Video Minimums**: At least 256px in longer dimension
- [ ] **Quality Standards**: Use high-quality assets

## Policy Compliance

### ✅ Differentiation Requirements
- [ ] **Clear Ad Distinction**: Don't camouflage ads as content
- [ ] **Visible Attribution**: Don't hide ad badges or AdChoices
- [ ] **No Content Overlap**: Prevent accidental clicks
- [ ] **Navigation Clarity**: Don't disguise ads as navigation

### ✅ Click Handling
- [ ] **No Clickable White Space**: Background must not be clickable
- [ ] **No Custom Click Handlers**: Let SDK handle clicks
- [ ] **Proper Asset Registration**: Register all clickable elements
- [ ] **Invalid Click Prevention**: Avoid overlapping elements

### ✅ Dismiss Options
- [ ] **Full-Screen Ads**: Configure dismiss settings appropriately
- [ ] **Clear Close Button**: Make dismiss option visible
- [ ] **No Overlap**: Prevent close button from overlapping ad elements

## Testing and Validation

### ✅ Development Testing
- [ ] Use test ad unit IDs during development
- [ ] Test on multiple device sizes and orientations
- [ ] Verify all required elements are visible
- [ ] Test click handling and navigation
- [ ] Validate memory management (no leaks)

### ✅ Policy Validation
- [ ] Use [Native Ads Validator](https://developers.google.com/admob/android/native/validator)
- [ ] Check ad attribution visibility
- [ ] Verify AdChoices overlay placement
- [ ] Test differentiation from content
- [ ] Validate text contrast and legibility

### ✅ Performance Testing
- [ ] Test ad loading times
- [ ] Monitor memory usage
- [ ] Verify proper ad destruction
- [ ] Test cache management
- [ ] Validate error handling

## Common Pitfalls

### ❌ Avoid These Mistakes
- [ ] **Memory Leaks**: Always call `destroy()` on ads
- [ ] **Continuous Retries**: Limit failed load attempts
- [ ] **Improper Sizing**: Ensure minimum size requirements
- [ ] **Missing Attribution**: Always include ad badges
- [ ] **Content Confusion**: Don't make ads look like content
- [ ] **Click Interference**: Avoid overlapping clickable elements
- [ ] **Cache Mismanagement**: Don't cache too many ads
- [ ] **Mediation Issues**: Don't use `loadAds()` with mediation

## Resources

### 📚 Official Documentation
- [Native Ads Loading Guide](https://developers.google.com/admob/android/native)
- [Native Advanced Implementation](https://developers.google.com/admob/android/native/advanced)
- [Native Ads Playbook](https://admob.google.com/home/<USER>/native-ads-playbook/)
- [Native Ads Policies](https://support.google.com/admob/answer/6329638)
- [Native Templates](https://developers.google.com/admob/android/native/templates)

### 🛠️ Tools and Testing
- [Native Ads Validator](https://developers.google.com/admob/android/native/validator)
- [Firebase A/B Testing](https://firebase.google.com/docs/ab-testing/)
- [AdMob Reports](https://support.google.com/admob/answer/2751663)
- [Test Ads Guide](https://developers.google.com/admob/android/test-ads)

### 📱 Sample Code
- [Android Java Examples](https://github.com/googleads/googleads-mobile-android-examples/tree/main/java/admob/NativeAdvancedExample)
- [Android Kotlin Examples](https://github.com/googleads/googleads-mobile-android-examples/tree/main/kotlin/admob/NativeAdvancedExample)
- [Jetpack Compose Examples](https://github.com/googleads/googleads-mobile-android-examples/tree/main/kotlin/advanced/JetpackComposeDemo)

---

*Last Updated: Based on official Google AdMob documentation as of August 2025*
*Always refer to the latest official documentation for the most current requirements*
