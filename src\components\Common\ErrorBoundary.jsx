import React from 'react'
import { useRouteError, isRouteErrorResponse } from 'react-router-dom'
import { useTheme } from '../../context/ThemeContext'

// Global Error Boundary Component
export function GlobalErrorBoundary({ children }) {
  return (
    <React.ErrorBoundary
      fallback={<ErrorFallback />}
      onError={(error, errorInfo) => {
        console.error('Global Error Boundary caught an error:', error, errorInfo)
        // Here you could send to error reporting service
      }}
    >
      {children}
    </React.ErrorBoundary>
  )
}

// Route Error Boundary for React Router
export function RouteErrorBoundary() {
  const error = useRouteError()
  const { theme } = useTheme()

  console.error('Route Error:', error)

  if (isRouteErrorResponse(error)) {
    return (
      <div className={`min-h-screen flex items-center justify-center ${theme.background}`}>
        <div className={`max-w-md mx-auto text-center p-6 ${theme.card} rounded-lg shadow-lg`}>
          <h1 className={`text-2xl font-bold mb-4 ${theme.text}`}>
            {error.status} {error.statusText}
          </h1>
          <p className={`${theme.textSecondary} mb-4`}>
            {error.status === 404 
              ? "The page you're looking for doesn't exist."
              : "Something went wrong with your request."
            }
          </p>
          <button
            onClick={() => window.location.href = '/'}
            className={`px-4 py-2 ${theme.primary} text-white rounded-lg hover:opacity-90 transition-opacity`}
          >
            Go Home
          </button>
        </div>
      </div>
    )
  }

  if (error instanceof Error) {
    return (
      <div className={`min-h-screen flex items-center justify-center ${theme.background}`}>
        <div className={`max-w-md mx-auto text-center p-6 ${theme.card} rounded-lg shadow-lg`}>
          <h1 className={`text-2xl font-bold mb-4 ${theme.text}`}>
            Oops! Something went wrong
          </h1>
          <p className={`${theme.textSecondary} mb-4`}>
            {error.message}
          </p>
          {import.meta.env.DEV && (
            <details className="mt-4 text-left">
              <summary className={`cursor-pointer ${theme.textSecondary}`}>
                Stack Trace (Development)
              </summary>
              <pre className="mt-2 text-xs overflow-auto bg-gray-100 p-2 rounded">
                {error.stack}
              </pre>
            </details>
          )}
          <button
            onClick={() => window.location.reload()}
            className={`px-4 py-2 ${theme.primary} text-white rounded-lg hover:opacity-90 transition-opacity mr-2`}
          >
            Reload Page
          </button>
          <button
            onClick={() => window.location.href = '/'}
            className={`px-4 py-2 ${theme.secondary} ${theme.text} rounded-lg hover:opacity-90 transition-opacity`}
          >
            Go Home
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className={`min-h-screen flex items-center justify-center ${theme.background}`}>
      <div className={`max-w-md mx-auto text-center p-6 ${theme.card} rounded-lg shadow-lg`}>
        <h1 className={`text-2xl font-bold mb-4 ${theme.text}`}>
          Unknown Error
        </h1>
        <p className={`${theme.textSecondary} mb-4`}>
          An unexpected error occurred. Please try refreshing the page.
        </p>
        <button
          onClick={() => window.location.reload()}
          className={`px-4 py-2 ${theme.primary} text-white rounded-lg hover:opacity-90 transition-opacity`}
        >
          Reload Page
        </button>
      </div>
    </div>
  )
}

// Simple Error Fallback Component
function ErrorFallback({ error, resetErrorBoundary }) {
  const { theme } = useTheme()

  return (
    <div className={`min-h-screen flex items-center justify-center ${theme.background}`}>
      <div className={`max-w-md mx-auto text-center p-6 ${theme.card} rounded-lg shadow-lg`}>
        <h2 className={`text-xl font-bold mb-4 ${theme.text}`}>
          Something went wrong
        </h2>
        <p className={`${theme.textSecondary} mb-4`}>
          {error?.message || 'An unexpected error occurred'}
        </p>
        <button
          onClick={resetErrorBoundary}
          className={`px-4 py-2 ${theme.primary} text-white rounded-lg hover:opacity-90 transition-opacity`}
        >
          Try Again
        </button>
      </div>
    </div>
  )
}
